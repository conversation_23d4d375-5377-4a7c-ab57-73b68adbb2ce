import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';
import '../services/hadith_service.dart';

/// Provider لإدارة الأحاديث النبوية
class HadithProvider with ChangeNotifier {
  List<HadithBook> _hadithBooks = [];
  List<Hadith> _currentHadiths = [];
  List<Hadith> _searchResults = [];
  List<Hadith> _favoriteHadiths = [];
  List<Hadith> _randomHadiths = [];
  
  HadithBook? _selectedBook;
  HadithChapter? _selectedChapter;
  Hadith? _currentHadith;
  
  bool _isLoading = false;
  String _searchQuery = '';
  String _selectedBookId = 'bukhari';

  // Getters
  List<HadithBook> get hadithBooks => _hadithBooks;
  List<Hadith> get currentHadiths => _currentHadiths;
  List<Hadith> get searchResults => _searchResults;
  List<Hadith> get favoriteHadiths => _favoriteHadiths;
  List<Hadith> get randomHadiths => _randomHadiths;
  
  HadithBook? get selectedBook => _selectedBook;
  HadithChapter? get selectedChapter => _selectedChapter;
  Hadith? get currentHadith => _currentHadith;
  
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  String get selectedBookId => _selectedBookId;

  bool get hasHadiths => _currentHadiths.isNotEmpty;
  bool get hasSearchResults => _searchResults.isNotEmpty;
  bool get hasFavorites => _favoriteHadiths.isNotEmpty;

  /// تحميل جميع كتب الحديث
  Future<void> loadHadithBooks() async {
    try {
      _setLoading(true);
      _hadithBooks = await HadithService.getAllHadithBooks();
      
      // تحديد الكتاب الافتراضي
      if (_hadithBooks.isNotEmpty) {
        _selectedBook = _hadithBooks.first;
        _selectedBookId = _selectedBook!.id;
        await loadHadithsFromBook(_selectedBookId);
      }
      
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل كتب الحديث: $e');
    }
  }

  /// تحميل أحاديث من كتاب معين
  Future<void> loadHadithsFromBook(String bookId) async {
    try {
      _setLoading(true);
      _selectedBookId = bookId;
      
      // العثور على الكتاب المحدد
      _selectedBook = _hadithBooks.where((book) => book.id == bookId).firstOrNull;
      
      _currentHadiths = await HadithService.getHadithsByBook(bookId);
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل أحاديث الكتاب: $e');
    }
  }

  /// البحث في الأحاديث
  Future<void> searchHadiths(String query) async {
    try {
      _searchQuery = query;
      
      if (query.trim().isEmpty) {
        _searchResults = [];
        notifyListeners();
        return;
      }

      _setLoading(true);
      _searchResults = await HadithService.searchHadiths(query);
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في البحث في الأحاديث: $e');
    }
  }

  /// الحصول على أحاديث عشوائية
  Future<void> loadRandomHadiths({int count = 5}) async {
    try {
      _setLoading(true);
      _randomHadiths = await HadithService.getRandomHadiths(count: count);
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل الأحاديث العشوائية: $e');
    }
  }

  /// تحديد الحديث الحالي
  void setCurrentHadith(Hadith hadith) {
    _currentHadith = hadith;
    notifyListeners();
  }

  /// تحديد الفصل الحالي
  void setCurrentChapter(HadithChapter chapter) {
    _selectedChapter = chapter;
    _currentHadiths = chapter.hadiths;
    notifyListeners();
  }

  /// إضافة حديث للمفضلة
  void addToFavorites(Hadith hadith) {
    if (!_favoriteHadiths.any((h) => h.id == hadith.id)) {
      _favoriteHadiths.add(hadith);
      notifyListeners();
      _saveFavorites();
    }
  }

  /// إزالة حديث من المفضلة
  void removeFromFavorites(Hadith hadith) {
    _favoriteHadiths.removeWhere((h) => h.id == hadith.id);
    notifyListeners();
    _saveFavorites();
  }

  /// التحقق من وجود حديث في المفضلة
  bool isFavorite(Hadith hadith) {
    return _favoriteHadiths.any((h) => h.id == hadith.id);
  }

  /// تبديل حالة المفضلة
  void toggleFavorite(Hadith hadith) {
    if (isFavorite(hadith)) {
      removeFromFavorites(hadith);
    } else {
      addToFavorites(hadith);
    }
  }

  /// الحصول على الحديث التالي
  Hadith? getNextHadith() {
    if (_currentHadith == null || _currentHadiths.isEmpty) return null;
    
    final currentIndex = _currentHadiths.indexOf(_currentHadith!);
    if (currentIndex < _currentHadiths.length - 1) {
      return _currentHadiths[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على الحديث السابق
  Hadith? getPreviousHadith() {
    if (_currentHadith == null || _currentHadiths.isEmpty) return null;
    
    final currentIndex = _currentHadiths.indexOf(_currentHadith!);
    if (currentIndex > 0) {
      return _currentHadiths[currentIndex - 1];
    }
    return null;
  }

  /// الانتقال للحديث التالي
  void goToNextHadith() {
    final nextHadith = getNextHadith();
    if (nextHadith != null) {
      setCurrentHadith(nextHadith);
    }
  }

  /// الانتقال للحديث السابق
  void goToPreviousHadith() {
    final previousHadith = getPreviousHadith();
    if (previousHadith != null) {
      setCurrentHadith(previousHadith);
    }
  }

  /// الحصول على حديث بالرقم
  Future<Hadith?> getHadithByNumber(int hadithNumber) async {
    try {
      return await HadithService.getHadithByNumber(_selectedBookId, hadithNumber);
    } catch (e) {
      debugPrint('خطأ في جلب الحديث بالرقم: $e');
      return null;
    }
  }

  /// تصفية الأحاديث حسب الدرجة
  List<Hadith> getHadithsByGrade(HadithGrade grade) {
    return _currentHadiths.where((hadith) => hadith.grade == grade).toList();
  }

  /// الحصول على الأحاديث الصحيحة
  List<Hadith> get sahihHadiths => getHadithsByGrade(HadithGrade.sahih);

  /// الحصول على الأحاديث الحسنة
  List<Hadith> get hasanHadiths => getHadithsByGrade(HadithGrade.hasan);

  /// مسح البحث
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
    notifyListeners();
  }

  /// مسح جميع المفضلة
  void clearFavorites() {
    _favoriteHadiths.clear();
    notifyListeners();
    _saveFavorites();
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// حفظ المفضلة (يمكن تطوير هذا لاحقاً مع SharedPreferences)
  void _saveFavorites() {
    // TODO: حفظ المفضلة في التخزين المحلي
    debugPrint('تم حفظ ${_favoriteHadiths.length} حديث في المفضلة');
  }

  /// تحميل المفضلة (يمكن تطوير هذا لاحقاً مع SharedPreferences)
  void _loadFavorites() {
    // TODO: تحميل المفضلة من التخزين المحلي
  }

  /// إحصائيات الأحاديث
  Map<String, int> get hadithStats {
    return {
      'total': _currentHadiths.length,
      'sahih': sahihHadiths.length,
      'hasan': hasanHadiths.length,
      'favorites': _favoriteHadiths.length,
    };
  }

  /// الحصول على كتاب بالمعرف
  HadithBook? getBookById(String id) {
    return _hadithBooks.where((book) => book.id == id).firstOrNull;
  }

  /// الحصول على فصل بالمعرف
  HadithChapter? getChapterById(String bookId, String chapterId) {
    final book = getBookById(bookId);
    if (book == null) return null;
    return book.chapters.where((chapter) => chapter.id == chapterId).firstOrNull;
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await loadHadithBooks();
    await loadRandomHadiths();
  }
}
