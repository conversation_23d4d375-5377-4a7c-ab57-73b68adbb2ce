import 'package:flutter/foundation.dart';
import '../models/library_models.dart';
import '../services/hadith_api_service.dart';
import '../services/tafsir_api_service.dart';
import '../services/fiqh_books_service.dart';

/// مزود البيانات للمكتبة الدينية
class LibraryProvider extends ChangeNotifier {
  // الحالة العامة
  bool _isLoading = false;
  String? _error;

  // الكتب الفقهية
  List<Book> _fiqhBooks = [];
  Book? _currentBook;
  Chapter? _currentChapter;
  Section? _currentSection;

  // الأحاديث
  List<Hadith> _hadiths = [];
  List<Hadith> _favoriteHadiths = [];
  String _selectedHadithSource = 'bukhari';

  // التفاسير
  List<Tafsir> _availableTafsirs = [];
  String _selectedTafsirId = '131'; // التفسير الميسر
  List<VerseWithTafsir> _currentTafsirs = [];

  // البحث
  List<SearchResult> _searchResults = [];
  String _lastSearchQuery = '';

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  List<Book> get fiqhBooks => _fiqhBooks;
  Book? get currentBook => _currentBook;
  Chapter? get currentChapter => _currentChapter;
  Section? get currentSection => _currentSection;
  
  List<Hadith> get hadiths => _hadiths;
  List<Hadith> get favoriteHadiths => _favoriteHadiths;
  String get selectedHadithSource => _selectedHadithSource;
  
  List<Tafsir> get availableTafsirs => _availableTafsirs;
  String get selectedTafsirId => _selectedTafsirId;
  List<VerseWithTafsir> get currentTafsirs => _currentTafsirs;
  
  List<SearchResult> get searchResults => _searchResults;
  String get lastSearchQuery => _lastSearchQuery;

  /// تهيئة المكتبة
  Future<void> initializeLibrary() async {
    try {
      _setLoading(true);
      _clearError();

      debugPrint('📚 تهيئة المكتبة الدينية...');

      // تحميل الكتب الفقهية
      await loadFiqhBooks();

      // تحميل التفاسير المتاحة
      await loadAvailableTafsirs();

      // تحميل بعض الأحاديث
      await loadHadiths();

      debugPrint('✅ تم تهيئة المكتبة بنجاح');
    } catch (e) {
      _setError('فشل في تهيئة المكتبة: $e');
      debugPrint('❌ خطأ في تهيئة المكتبة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الكتب الفقهية
  Future<void> loadFiqhBooks() async {
    try {
      debugPrint('📖 تحميل الكتب الفقهية...');
      _fiqhBooks = await FiqhBooksService.getAllFiqhBooks();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الكتب: $e');
      throw Exception('فشل في تحميل الكتب الفقهية');
    }
  }

  /// تحديد الكتاب الحالي
  Future<void> setCurrentBook(String bookId) async {
    try {
      _currentBook = await FiqhBooksService.getBookById(bookId);
      _currentChapter = null;
      _currentSection = null;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الكتاب: $e');
    }
  }

  /// تحديد الفصل الحالي
  Future<void> setCurrentChapter(String bookId, String chapterId) async {
    try {
      _currentChapter = await FiqhBooksService.getChapter(bookId, chapterId);
      _currentSection = null;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الفصل: $e');
    }
  }

  /// تحديد القسم الحالي
  Future<void> setCurrentSection(String bookId, String chapterId, String sectionId) async {
    try {
      _currentSection = await FiqhBooksService.getSection(bookId, chapterId, sectionId);
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل القسم: $e');
    }
  }

  /// تحميل الأحاديث
  Future<void> loadHadiths({int page = 1, int limit = 20}) async {
    try {
      _setLoading(true);
      debugPrint('📜 تحميل الأحاديث من $_selectedHadithSource...');
      
      // محاولة جلب من API أولاً
      List<Hadith> newHadiths = await HadithApiService.getHadithsBySource(
        _selectedHadithSource,
        page: page,
        limit: limit,
      );
      
      // إذا فشل API، استخدم البيانات المحلية
      if (newHadiths.isEmpty) {
        newHadiths = HadithApiService.getLocalHadiths();
      }
      
      if (page == 1) {
        _hadiths = newHadiths;
      } else {
        _hadiths.addAll(newHadiths);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الأحاديث: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تغيير مصدر الأحاديث
  Future<void> setHadithSource(String source) async {
    if (_selectedHadithSource != source) {
      _selectedHadithSource = source;
      await loadHadiths();
    }
  }

  /// البحث في الأحاديث
  Future<void> searchHadiths(String query) async {
    try {
      _setLoading(true);
      debugPrint('🔍 البحث في الأحاديث: $query');
      
      final results = await HadithApiService.searchHadiths(query);
      
      if (results.isEmpty) {
        // البحث المحلي
        final localHadiths = HadithApiService.getLocalHadiths();
        _hadiths = localHadiths.where((hadith) =>
          hadith.text.contains(query) ||
          hadith.narrator.contains(query) ||
          (hadith.keywords?.any((keyword) => keyword.contains(query)) ?? false)
        ).toList();
      } else {
        _hadiths = results;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('فشل في البحث: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة/إزالة حديث من المفضلة
  void toggleFavoriteHadith(Hadith hadith) {
    final index = _favoriteHadiths.indexWhere((h) => h.id == hadith.id);
    
    if (index >= 0) {
      _favoriteHadiths.removeAt(index);
    } else {
      _favoriteHadiths.add(hadith);
    }
    
    notifyListeners();
  }

  /// التحقق من كون الحديث مفضل
  bool isHadithFavorite(Hadith hadith) {
    return _favoriteHadiths.any((h) => h.id == hadith.id);
  }

  /// تحميل التفاسير المتاحة
  Future<void> loadAvailableTafsirs() async {
    try {
      debugPrint('📖 تحميل التفاسير المتاحة...');
      _availableTafsirs = await TafsirApiService.getAvailableTafsirs();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل التفاسير: $e');
    }
  }

  /// تحديد التفسير المختار
  void setSelectedTafsir(String tafsirId) {
    _selectedTafsirId = tafsirId;
    notifyListeners();
  }

  /// جلب تفسير آية
  Future<void> getVerseTafsir(int surahNumber, int verseNumber) async {
    try {
      _setLoading(true);
      debugPrint('📖 جلب تفسير الآية $surahNumber:$verseNumber...');
      
      final tafsir = await TafsirApiService.getVerseTafsir(
        surahNumber,
        verseNumber,
        _selectedTafsirId,
      );
      
      if (tafsir != null) {
        _currentTafsirs = [tafsir];
      } else {
        _currentTafsirs = [TafsirApiService.getLocalSampleTafsir()];
      }
      
      notifyListeners();
    } catch (e) {
      _setError('فشل في جلب التفسير: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// البحث الشامل
  Future<void> searchAll(String query) async {
    try {
      _setLoading(true);
      _lastSearchQuery = query;
      _searchResults.clear();
      
      debugPrint('🔍 البحث الشامل: $query');
      
      // البحث في الكتب الفقهية
      final fiqhResults = await FiqhBooksService.searchInBooks(query);
      _searchResults.addAll(fiqhResults);
      
      // البحث في الأحاديث
      final hadithResults = await HadithApiService.searchHadiths(query);
      for (final hadith in hadithResults) {
        _searchResults.add(SearchResult(
          id: hadith.id,
          title: 'حديث شريف',
          content: hadith.text,
          source: hadith.source,
          type: SearchResultType.hadith,
          metadata: {
            'narrator': hadith.narrator,
            'grade': hadith.grade,
          },
        ));
      }
      
      notifyListeners();
    } catch (e) {
      _setError('فشل في البحث: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// مسح نتائج البحث
  void clearSearchResults() {
    _searchResults.clear();
    _lastSearchQuery = '';
    notifyListeners();
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تحديد خطأ
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// مسح الخطأ
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await initializeLibrary();
  }
}
