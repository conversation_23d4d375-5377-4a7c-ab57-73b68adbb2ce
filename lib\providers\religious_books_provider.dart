import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';
import '../services/religious_books_service.dart';

/// Provider لإدارة الكتب الدينية
class ReligiousBooksProvider with ChangeNotifier {
  List<ReligiousBook> _books = [];
  List<BookCategory> _categories = [];
  LibraryStats? _stats;
  ReligiousBook? _currentBook;
  BookChapter? _currentChapter;
  BookSection? _currentSection;
  bool _isLoading = false;
  String _searchQuery = '';
  List<ReligiousBook> _searchResults = [];
  String _selectedCategory = 'all';

  // Getters
  List<ReligiousBook> get books => _books;
  List<BookCategory> get categories => _categories;
  LibraryStats? get stats => _stats;
  ReligiousBook? get currentBook => _currentBook;
  BookChapter? get currentChapter => _currentChapter;
  BookSection? get currentSection => _currentSection;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  List<ReligiousBook> get searchResults => _searchResults;
  String get selectedCategory => _selectedCategory;

  /// تحميل جميع الكتب
  Future<void> loadBooks() async {
    try {
      _isLoading = true;
      notifyListeners();

      _books = await ReligiousBooksService.getAllBooks();
      _categories = await ReligiousBooksService.getBookCategories();
      _stats = await ReligiousBooksService.getLibraryStats();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('خطأ في تحميل الكتب: $e');
    }
  }

  /// تحديد الكتاب الحالي
  void setCurrentBook(ReligiousBook book) {
    _currentBook = book;
    _currentChapter = null;
    _currentSection = null;
    notifyListeners();
  }

  /// تحديد الفصل الحالي
  void setCurrentChapter(BookChapter chapter) {
    _currentChapter = chapter;
    _currentSection = null;
    notifyListeners();
  }

  /// تحديد القسم الحالي
  void setCurrentSection(BookSection section) {
    _currentSection = section;
    notifyListeners();
  }

  /// البحث في الكتب
  Future<void> searchBooks(String query) async {
    try {
      _searchQuery = query;
      
      if (query.trim().isEmpty) {
        _searchResults = [];
        notifyListeners();
        return;
      }

      _isLoading = true;
      notifyListeners();

      _searchResults = await ReligiousBooksService.searchBooks(query);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('خطأ في البحث: $e');
    }
  }

  /// تصفية الكتب حسب الفئة
  void filterByCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  /// الحصول على الكتب المفلترة
  List<ReligiousBook> get filteredBooks {
    if (_selectedCategory == 'all') {
      return _books;
    }
    return _books.where((book) => book.category == _selectedCategory).toList();
  }

  /// الحصول على الكتب الشعرية
  List<ReligiousBook> get poeticBooks {
    return _books.where((book) => book.isPoetic).toList();
  }

  /// الحصول على كتب التفسير
  List<ReligiousBook> get tafsirBooks {
    return _books.where((book) => book.type == BookType.tafsir).toList();
  }

  /// الحصول على الكتب الفقهية
  List<ReligiousBook> get fiqhBooks {
    return _books.where((book) => book.type == BookType.fiqh).toList();
  }

  /// الحصول على كتب الحديث
  List<ReligiousBook> get hadithBooks {
    return _books.where((book) => book.type == BookType.hadith).toList();
  }

  /// الحصول على كتاب بالمعرف
  ReligiousBook? getBookById(String id) {
    try {
      return _books.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على فصل بالمعرف
  BookChapter? getChapterById(String bookId, String chapterId) {
    try {
      final book = getBookById(bookId);
      if (book == null) return null;
      return book.chapters.firstWhere((chapter) => chapter.id == chapterId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على قسم بالمعرف
  BookSection? getSectionById(String bookId, String chapterId, String sectionId) {
    try {
      final chapter = getChapterById(bookId, chapterId);
      if (chapter == null) return null;
      return chapter.sections.firstWhere((section) => section.id == sectionId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على الفصل التالي
  BookChapter? getNextChapter() {
    if (_currentBook == null || _currentChapter == null) return null;
    
    final currentIndex = _currentBook!.chapters.indexOf(_currentChapter!);
    if (currentIndex < _currentBook!.chapters.length - 1) {
      return _currentBook!.chapters[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على الفصل السابق
  BookChapter? getPreviousChapter() {
    if (_currentBook == null || _currentChapter == null) return null;
    
    final currentIndex = _currentBook!.chapters.indexOf(_currentChapter!);
    if (currentIndex > 0) {
      return _currentBook!.chapters[currentIndex - 1];
    }
    return null;
  }

  /// الحصول على القسم التالي
  BookSection? getNextSection() {
    if (_currentChapter == null || _currentSection == null) return null;
    
    final currentIndex = _currentChapter!.sections.indexOf(_currentSection!);
    if (currentIndex < _currentChapter!.sections.length - 1) {
      return _currentChapter!.sections[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على القسم السابق
  BookSection? getPreviousSection() {
    if (_currentChapter == null || _currentSection == null) return null;
    
    final currentIndex = _currentChapter!.sections.indexOf(_currentSection!);
    if (currentIndex > 0) {
      return _currentChapter!.sections[currentIndex - 1];
    }
    return null;
  }

  /// الانتقال للفصل التالي
  void goToNextChapter() {
    final nextChapter = getNextChapter();
    if (nextChapter != null) {
      setCurrentChapter(nextChapter);
    }
  }

  /// الانتقال للفصل السابق
  void goToPreviousChapter() {
    final previousChapter = getPreviousChapter();
    if (previousChapter != null) {
      setCurrentChapter(previousChapter);
    }
  }

  /// الانتقال للقسم التالي
  void goToNextSection() {
    final nextSection = getNextSection();
    if (nextSection != null) {
      setCurrentSection(nextSection);
    }
  }

  /// الانتقال للقسم السابق
  void goToPreviousSection() {
    final previousSection = getPreviousSection();
    if (previousSection != null) {
      setCurrentSection(previousSection);
    }
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
    notifyListeners();
  }

  /// إعادة تعيين الفلاتر
  void resetFilters() {
    _selectedCategory = 'all';
    clearSearch();
    notifyListeners();
  }

  /// الحصول على عدد الكتب في فئة معينة
  int getBooksCountInCategory(String category) {
    if (category == 'all') return _books.length;
    return _books.where((book) => book.category == category).length;
  }

  /// الحصول على الكتب الأكثر شعبية (يمكن تطويرها لاحقاً)
  List<ReligiousBook> get popularBooks {
    // في الوقت الحالي نرجع أول 5 كتب
    return _books.take(5).toList();
  }

  /// الحصول على الكتب المضافة حديثاً
  List<ReligiousBook> get recentBooks {
    final sortedBooks = List<ReligiousBook>.from(_books);
    sortedBooks.sort((a, b) => b.publishDate.compareTo(a.publishDate));
    return sortedBooks.take(5).toList();
  }
}
