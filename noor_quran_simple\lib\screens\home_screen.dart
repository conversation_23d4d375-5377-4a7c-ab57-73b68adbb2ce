import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/audio_provider.dart';
import 'surah_list_screen.dart';
import 'surah_index_screen.dart';
import 'search_history_screen.dart';
import 'reciter_selection_screen.dart';
import 'audio_test_screen.dart';
import 'library_main_screen.dart';
import 'settings_screen.dart';
import 'search_screen.dart';
import 'bookmarks_screen.dart';
import '../test_data_loading.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeTab(),
    const SurahListScreen(),
    const SearchScreen(),
    const BookmarksScreen(),
    const SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.menu_book), label: 'السور'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'البحث'),
          BottomNavigationBarItem(icon: Icon(Icons.bookmark), label: 'المفضلة'),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }
}

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        actions: [
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return IconButton(
                icon: Icon(themeProvider.themeModeIcon),
                onPressed: () {
                  themeProvider.toggleTheme();
                },
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(context),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildQuickActions(context),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildLastRead(context),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildAudioPlayer(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppConstants.primaryColor, AppConstants.secondaryColor],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        children: [
          const Icon(Icons.menu_book, size: 60, color: Colors.white),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'أهلاً بك في تطبيق نور القرآن',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          const Text(
            'اقرأ القرآن الكريم بسهولة ويسر',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'قائمة السور',
                Icons.list,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SurahListScreen(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: _buildActionCard(
                context,
                'البحث',
                Icons.search,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SearchScreen()),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'فهرس السور',
                Icons.grid_view,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SurahIndexScreen(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: _buildActionCard(
                context,
                'سجل البحث',
                Icons.history,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SearchHistoryScreen(),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'المكتبة الدينية',
                Icons.library_books,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LibraryMainScreen(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: _buildActionCard(
                context,
                'تغيير القارئ',
                Icons.record_voice_over,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ReciterSelectionScreen(),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'اختبار الصوت',
                Icons.audiotrack,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AudioTestScreen(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: _buildActionCard(
                context,
                'الإعدادات',
                Icons.settings,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'اختبار البيانات',
                Icons.bug_report,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TestDataLoadingScreen(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              Icon(icon, size: 40, color: AppConstants.primaryColor),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                title,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLastRead(BuildContext context) {
    return Consumer<QuranProvider>(
      builder: (context, quranProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'آخر قراءة',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Row(
                  children: [
                    const Icon(Icons.bookmark, color: AppConstants.accentColor),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      'سورة رقم ${quranProvider.currentSurah} - آية ${quranProvider.currentAyah}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to last read position
                  },
                  child: const Text('متابعة القراءة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAudioPlayer(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'مشغل الصوت',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        audioProvider.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        size: 40,
                      ),
                      onPressed: () {
                        if (audioProvider.isPlaying) {
                          audioProvider.pauseAudio();
                        } else {
                          // Play sample audio
                          audioProvider.playAudio(
                            'https://server8.mp3quran.net/afs/001.mp3',
                          );
                        }
                      },
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'القارئ: ${_getReciterName(audioProvider.selectedReciter)}',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                            ),
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          LinearProgressIndicator(
                            value: audioProvider.duration.inMilliseconds > 0
                                ? audioProvider.position.inMilliseconds /
                                      audioProvider.duration.inMilliseconds
                                : 0.0,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getReciterName(String identifier) {
    final reciter = AppConstants.popularReciters.firstWhere(
      (r) => r['identifier'] == identifier,
      orElse: () => {'name': 'غير معروف'},
    );
    return reciter['name'] ?? 'غير معروف';
  }
}
