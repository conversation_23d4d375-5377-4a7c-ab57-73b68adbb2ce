import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/analytics_event.dart';

/// خدمة التحليلات الذكية - لمسة خاصة لفهم سلوك المستخدم
class AnalyticsService {
  static const String _keyPrefix = 'analytics_';
  static const String _eventsKey = '${_keyPrefix}events';
  static const String _sessionKey = '${_keyPrefix}session';
  static const String _userStatsKey = '${_keyPrefix}user_stats';

  static bool _isInitialized = false;
  static List<AnalyticsEvent> _events = [];
  static DateTime? _sessionStart;

  /// تهيئة خدمة التحليلات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadStoredEvents();
      _sessionStart = DateTime.now();
      _isInitialized = true;
      
      debugPrint('✅ تم تهيئة خدمة التحليلات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التحليلات: $e');
    }
  }

  /// تسجيل حدث جديد
  static Future<void> logEvent(AnalyticsEvent event) async {
    if (!_isInitialized) await initialize();

    _events.add(event);
    await _saveEvents();
    
    debugPrint('📊 تم تسجيل حدث: ${event.name}');
  }

  /// تسجيل فتح التطبيق
  static Future<void> logAppOpen() async {
    await logEvent(AnalyticsEvent(
      name: 'app_open',
      timestamp: DateTime.now(),
      properties: {
        'session_id': _generateSessionId(),
        'platform': defaultTargetPlatform.name,
      },
    ));
  }

  /// تسجيل بداية جلسة القراءة
  static Future<void> logReadingSessionStart() async {
    await logEvent(AnalyticsEvent(
      name: 'reading_session_start',
      timestamp: DateTime.now(),
      properties: {
        'session_start': DateTime.now().toIso8601String(),
      },
    ));
  }

  /// تسجيل نهاية جلسة القراءة
  static Future<void> logReadingSessionEnd(Duration duration, int ayahsRead) async {
    await logEvent(AnalyticsEvent(
      name: 'reading_session_end',
      timestamp: DateTime.now(),
      properties: {
        'duration_minutes': duration.inMinutes,
        'ayahs_read': ayahsRead,
        'reading_speed': ayahsRead / (duration.inMinutes > 0 ? duration.inMinutes : 1),
      },
    ));
  }

  /// تسجيل قراءة سورة
  static Future<void> logSurahRead(int surahNumber, String surahName, Duration readingTime) async {
    await logEvent(AnalyticsEvent(
      name: 'surah_read',
      timestamp: DateTime.now(),
      properties: {
        'surah_number': surahNumber,
        'surah_name': surahName,
        'reading_time_seconds': readingTime.inSeconds,
      },
    ));
  }

  /// تسجيل البحث
  static Future<void> logSearch(String query, int resultsCount) async {
    await logEvent(AnalyticsEvent(
      name: 'search_performed',
      timestamp: DateTime.now(),
      properties: {
        'query_length': query.length,
        'results_count': resultsCount,
        'has_results': resultsCount > 0,
      },
    ));
  }

  /// تسجيل إضافة علامة مرجعية
  static Future<void> logBookmarkAdded(int surahNumber, int ayahNumber) async {
    await logEvent(AnalyticsEvent(
      name: 'bookmark_added',
      timestamp: DateTime.now(),
      properties: {
        'surah_number': surahNumber,
        'ayah_number': ayahNumber,
      },
    ));
  }

  /// تسجيل تشغيل الصوت
  static Future<void> logAudioPlay(String reciterName, int surahNumber) async {
    await logEvent(AnalyticsEvent(
      name: 'audio_play',
      timestamp: DateTime.now(),
      properties: {
        'reciter_name': reciterName,
        'surah_number': surahNumber,
      },
    ));
  }

  /// تسجيل تغيير الإعدادات
  static Future<void> logSettingsChange(String settingName, dynamic oldValue, dynamic newValue) async {
    await logEvent(AnalyticsEvent(
      name: 'settings_changed',
      timestamp: DateTime.now(),
      properties: {
        'setting_name': settingName,
        'old_value': oldValue.toString(),
        'new_value': newValue.toString(),
      },
    ));
  }

  /// تسجيل استخدام ميزة
  static Future<void> logFeatureUsed(String featureName, Map<String, dynamic>? additionalData) async {
    await logEvent(AnalyticsEvent(
      name: 'feature_used',
      timestamp: DateTime.now(),
      properties: {
        'feature_name': featureName,
        ...?additionalData,
      },
    ));
  }

  /// الحصول على إحصائيات الاستخدام
  static Future<Map<String, dynamic>> getUsageStatistics() async {
    if (!_isInitialized) await initialize();

    final now = DateTime.now();
    final last7Days = now.subtract(const Duration(days: 7));
    final last30Days = now.subtract(const Duration(days: 30));

    final recentEvents = _events.where((e) => e.timestamp.isAfter(last7Days)).toList();
    final monthlyEvents = _events.where((e) => e.timestamp.isAfter(last30Days)).toList();

    // حساب الإحصائيات
    final readingSessions = recentEvents.where((e) => e.name == 'reading_session_end').toList();
    final totalReadingTime = readingSessions.fold<int>(0, (sum, event) {
      return sum + (event.properties['duration_minutes'] as int? ?? 0);
    });

    final surahsRead = recentEvents.where((e) => e.name == 'surah_read').length;
    final searchesPerformed = recentEvents.where((e) => e.name == 'search_performed').length;
    final bookmarksAdded = recentEvents.where((e) => e.name == 'bookmark_added').length;

    return {
      'last_7_days': {
        'total_events': recentEvents.length,
        'reading_sessions': readingSessions.length,
        'total_reading_minutes': totalReadingTime,
        'surahs_read': surahsRead,
        'searches_performed': searchesPerformed,
        'bookmarks_added': bookmarksAdded,
      },
      'last_30_days': {
        'total_events': monthlyEvents.length,
        'average_daily_usage': monthlyEvents.length / 30,
      },
      'most_used_features': _getMostUsedFeatures(recentEvents),
      'reading_patterns': _getReadingPatterns(readingSessions),
    };
  }

  /// الحصول على الميزات الأكثر استخداماً
  static Map<String, int> _getMostUsedFeatures(List<AnalyticsEvent> events) {
    final featureUsage = <String, int>{};
    
    for (final event in events) {
      if (event.name == 'feature_used') {
        final featureName = event.properties['feature_name'] as String? ?? 'unknown';
        featureUsage[featureName] = (featureUsage[featureName] ?? 0) + 1;
      }
    }
    
    // ترتيب حسب الاستخدام
    final sortedEntries = featureUsage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Map.fromEntries(sortedEntries.take(5));
  }

  /// الحصول على أنماط القراءة
  static Map<String, dynamic> _getReadingPatterns(List<AnalyticsEvent> sessions) {
    if (sessions.isEmpty) return {};

    final durations = sessions.map((e) => e.properties['duration_minutes'] as int? ?? 0).toList();
    final ayahCounts = sessions.map((e) => e.properties['ayahs_read'] as int? ?? 0).toList();

    final avgDuration = durations.fold<int>(0, (sum, d) => sum + d) / durations.length;
    final avgAyahs = ayahCounts.fold<int>(0, (sum, a) => sum + a) / ayahCounts.length;

    return {
      'average_session_minutes': avgDuration.round(),
      'average_ayahs_per_session': avgAyahs.round(),
      'total_sessions': sessions.length,
      'longest_session_minutes': durations.isNotEmpty ? durations.reduce((a, b) => a > b ? a : b) : 0,
    };
  }

  /// تحميل الأحداث المحفوظة
  static Future<void> _loadStoredEvents() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final eventsJson = prefs.getStringList(_eventsKey) ?? [];
      
      _events = eventsJson.map((json) => AnalyticsEvent.fromJson(json)).toList();
      
      // الاحتفاظ بآخر 1000 حدث فقط
      if (_events.length > 1000) {
        _events = _events.sublist(_events.length - 1000);
        await _saveEvents();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الأحداث: $e');
      _events = [];
    }
  }

  /// حفظ الأحداث
  static Future<void> _saveEvents() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final eventsJson = _events.map((event) => event.toJson()).toList();
      await prefs.setStringList(_eventsKey, eventsJson);
    } catch (e) {
      debugPrint('خطأ في حفظ الأحداث: $e');
    }
  }

  /// توليد معرف جلسة
  static String _generateSessionId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${_events.length}';
  }

  /// مسح البيانات القديمة
  static Future<void> clearOldData() async {
    final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
    _events.removeWhere((event) => event.timestamp.isBefore(cutoffDate));
    await _saveEvents();
  }

  /// تصدير البيانات للتحليل
  static Future<List<Map<String, dynamic>>> exportData() async {
    return _events.map((event) => event.toMap()).toList();
  }
}
