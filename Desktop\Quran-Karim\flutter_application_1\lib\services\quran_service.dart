import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/surah.dart';
import '../constants/app_constants.dart';
import '../data/quran_data.dart';
import 'database_service.dart';

class QuranService {
  final DatabaseService _databaseService = DatabaseService();

  // External API endpoints
  static const String _externalApiBase = 'https://api.alquran.cloud/v1';
  static const String _quranEndpoint = '/quran/ar.alafasy';
  static const String _surahEndpoint = '/surah';

  // Local endpoints (deprecated)
  static const String _surahsEndpoint = '/surah';
  static const String _ayahsEndpoint = '/ayah';

  // New method to fetch from external API
  Future<List<Surah>> _getSurahsFromExternalAPI() async {
    try {
      final response = await http.get(
        Uri.parse('$_externalApiBase$_quranEndpoint'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200 && jsonData['data'] != null) {
          final List<dynamic> surahsData = jsonData['data']['surahs'];

          return surahsData.map((surahJson) {
            return Surah(
              number: surahJson['number'],
              name: surahJson['name'],
              englishName: surahJson['englishName'],
              englishNameTranslation: surahJson['englishNameTranslation'],
              numberOfAyahs: surahJson['ayahs'].length,
              revelationType: surahJson['revelationType'],
              juz: surahJson['ayahs'].isNotEmpty
                  ? surahJson['ayahs'][0]['juz']
                  : 1,
              hizb: surahJson['ayahs'].isNotEmpty
                  ? surahJson['ayahs'][0]['hizbQuarter']
                  : 1,
              rukus: surahJson['ayahs'].isNotEmpty
                  ? surahJson['ayahs'][0]['ruku']
                  : 1,
            );
          }).toList();
        }
      }

      throw Exception('Invalid API response');
    } catch (e) {
      debugPrint('Error fetching from external API: $e');
      rethrow;
    }
  }

  Future<List<Surah>> getAllSurahs() async {
    try {
      // Try to get from external API first
      final apiSurahs = await _getSurahsFromExternalAPI();
      if (apiSurahs.isNotEmpty) {
        return apiSurahs;
      }
    } catch (e) {
      debugPrint('Failed to fetch from external API: $e');
    }

    try {
      // Fallback to local database
      final localSurahs = await _databaseService.getAllSurahs();
      if (localSurahs.isNotEmpty) {
        return localSurahs;
      }
    } catch (e) {
      debugPrint('Database not available: $e');
    }

    try {
      // If not available locally, fetch from API
      final response = await http.get(
        Uri.parse('${AppConstants.quranApiBaseUrl}$_surahsEndpoint'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final List<dynamic> surahsData = jsonData['data'];

        final surahs = surahsData
            .map((surahJson) => Surah.fromJson(surahJson))
            .toList();

        // Try to save to local database
        try {
          await _databaseService.insertSurahs(surahs);
        } catch (e) {
          // Ignore database errors
        }

        return surahs;
      } else {
        throw Exception('فشل في تحميل السور من الخادم');
      }
    } catch (e) {
      // If API fails, return hardcoded basic surah list
      return QuranData.getAllSurahs();
    }
  }

  // New method to fetch ayahs from external API
  Future<List<Ayah>> _getSurahAyahsFromExternalAPI(int surahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_externalApiBase$_surahEndpoint/$surahNumber/ar.alafasy'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200 && jsonData['data'] != null) {
          final List<dynamic> ayahsData = jsonData['data']['ayahs'];

          return ayahsData.map((ayahJson) {
            return Ayah(
              number: ayahJson['number'],
              numberInSurah: ayahJson['numberInSurah'],
              text: ayahJson['text'],
              surahNumber: surahNumber,
              juz: ayahJson['juz'],
              manzil: ayahJson['manzil'],
              page: ayahJson['page'],
              ruku: ayahJson['ruku'],
              hizbQuarter: ayahJson['hizbQuarter'],
              sajda: ayahJson['sajda'] ?? false,
              audioUrl: ayahJson['audio'],
            );
          }).toList();
        }
      }

      throw Exception('Invalid API response');
    } catch (e) {
      debugPrint('Error fetching ayahs from external API: $e');
      rethrow;
    }
  }

  Future<List<Ayah>> getSurahAyahs(int surahNumber) async {
    try {
      // Try to get from external API first
      final apiAyahs = await _getSurahAyahsFromExternalAPI(surahNumber);
      if (apiAyahs.isNotEmpty) {
        return apiAyahs;
      }
    } catch (e) {
      debugPrint('Failed to fetch ayahs from external API: $e');
    }

    try {
      // Fallback to local database
      final localSurah = await _databaseService.getSurahWithAyahs(surahNumber);
      if (localSurah != null && localSurah.ayahs.isNotEmpty) {
        return localSurah.ayahs;
      }
    } catch (e) {
      debugPrint('Database not available: $e');
    }

    // Final fallback to local data
    final surah = QuranData.getSampleSurahs()
        .where((s) => s.number == surahNumber)
        .firstOrNull;
    return surah?.ayahs ?? [];
  }

  Future<Surah> getSurahWithAyahs(int surahNumber) async {
    try {
      // First try to get from local database
      final localSurah = await _databaseService.getSurahWithAyahs(surahNumber);
      if (localSurah != null && localSurah.ayahs.isNotEmpty) {
        return localSurah;
      }
    } catch (e) {
      // Database not available, try sample data
      final sampleSurahs = QuranData.getSampleSurahs();
      final sampleSurah = sampleSurahs
          .where((s) => s.number == surahNumber)
          .firstOrNull;
      if (sampleSurah != null) {
        return sampleSurah;
      }
    }

    try {
      // If not available locally, fetch from API
      final response = await http.get(
        Uri.parse(
          '${AppConstants.quranApiBaseUrl}$_surahsEndpoint/$surahNumber',
        ),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final surah = Surah.fromJson(jsonData['data']);

        // Try to save to local database
        try {
          await _databaseService.insertSurahWithAyahs(surah);
        } catch (e) {
          // Ignore database errors
        }

        return surah;
      } else {
        throw Exception('فشل في تحميل السورة من الخادم');
      }
    } catch (e) {
      // If API fails, try sample data
      final sampleSurahs = QuranData.getSampleSurahs();
      final sampleSurah = sampleSurahs
          .where((s) => s.number == surahNumber)
          .firstOrNull;
      if (sampleSurah != null) {
        return sampleSurah;
      }

      throw Exception('فشل في تحميل السورة: ${e.toString()}');
    }
  }

  Future<List<Ayah>> searchInQuran(String query) async {
    try {
      // Search in local database first
      final localResults = await _databaseService.searchAyahs(query);
      if (localResults.isNotEmpty) {
        return localResults;
      }

      // If no local results, you could implement API search here
      // For now, return empty list
      return [];
    } catch (e) {
      throw Exception('فشل في البحث: ${e.toString()}');
    }
  }

  Future<List<Ayah>> getBookmarkedAyahs() async {
    try {
      return await _databaseService.getBookmarkedAyahs();
    } catch (e) {
      // Return empty list if database not available
      return [];
    }
  }

  Future<void> saveBookmarkedAyahs(List<Ayah> ayahs) async {
    try {
      await _databaseService.saveBookmarkedAyahs(ayahs);
    } catch (e) {
      // Ignore database errors in web environment
    }
  }

  Future<Ayah?> getAyahByNumber(int ayahNumber) async {
    try {
      return await _databaseService.getAyahByNumber(ayahNumber);
    } catch (e) {
      throw Exception('فشل في تحميل الآية: ${e.toString()}');
    }
  }

  Future<List<Ayah>> getAyahsByJuz(int juzNumber) async {
    try {
      return await _databaseService.getAyahsByJuz(juzNumber);
    } catch (e) {
      throw Exception('فشل في تحميل آيات الجزء: ${e.toString()}');
    }
  }

  Future<List<Ayah>> getAyahsByPage(int pageNumber) async {
    try {
      return await _databaseService.getAyahsByPage(pageNumber);
    } catch (e) {
      throw Exception('فشل في تحميل آيات الصفحة: ${e.toString()}');
    }
  }

  // Helper method to get basic surah list when API is not available (deprecated - use QuranData instead)
  List<Surah> _getBasicSurahList() {
    return [
      Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'Meccan',
        juz: 1,
        hizb: 1,
        rukus: 1,
      ),
      Surah(
        number: 2,
        name: 'البقرة',
        englishName: 'Al-Baqarah',
        englishNameTranslation: 'The Cow',
        numberOfAyahs: 286,
        revelationType: 'Medinan',
        juz: 1,
        hizb: 1,
        rukus: 40,
      ),
      Surah(
        number: 3,
        name: 'آل عمران',
        englishName: 'Aal-E-Imran',
        englishNameTranslation: 'The Family of Imran',
        numberOfAyahs: 200,
        revelationType: 'Medinan',
        juz: 3,
        hizb: 5,
        rukus: 20,
      ),
      Surah(
        number: 4,
        name: 'النساء',
        englishName: 'An-Nisa',
        englishNameTranslation: 'The Women',
        numberOfAyahs: 176,
        revelationType: 'Medinan',
        juz: 4,
        hizb: 7,
        rukus: 24,
      ),
      Surah(
        number: 5,
        name: 'المائدة',
        englishName: 'Al-Maidah',
        englishNameTranslation: 'The Table',
        numberOfAyahs: 120,
        revelationType: 'Medinan',
        juz: 6,
        hizb: 11,
        rukus: 16,
      ),
      Surah(
        number: 6,
        name: 'الأنعام',
        englishName: 'Al-Anaam',
        englishNameTranslation: 'The Cattle',
        numberOfAyahs: 165,
        revelationType: 'Meccan',
        juz: 7,
        hizb: 13,
        rukus: 20,
      ),
      Surah(
        number: 7,
        name: 'الأعراف',
        englishName: 'Al-Araf',
        englishNameTranslation: 'The Heights',
        numberOfAyahs: 206,
        revelationType: 'Meccan',
        juz: 8,
        hizb: 15,
        rukus: 24,
      ),
      Surah(
        number: 8,
        name: 'الأنفال',
        englishName: 'Al-Anfal',
        englishNameTranslation: 'The Spoils of War',
        numberOfAyahs: 75,
        revelationType: 'Medinan',
        juz: 9,
        hizb: 17,
        rukus: 10,
      ),
      Surah(
        number: 9,
        name: 'التوبة',
        englishName: 'At-Taubah',
        englishNameTranslation: 'The Repentance',
        numberOfAyahs: 129,
        revelationType: 'Medinan',
        juz: 10,
        hizb: 19,
        rukus: 16,
      ),
      Surah(
        number: 10,
        name: 'يونس',
        englishName: 'Yunus',
        englishNameTranslation: 'Jonah',
        numberOfAyahs: 109,
        revelationType: 'Meccan',
        juz: 11,
        hizb: 21,
        rukus: 11,
      ),
      Surah(
        number: 11,
        name: 'هود',
        englishName: 'Hud',
        englishNameTranslation: 'Hud',
        numberOfAyahs: 123,
        revelationType: 'Meccan',
        juz: 11,
        hizb: 21,
        rukus: 10,
      ),
      Surah(
        number: 12,
        name: 'يوسف',
        englishName: 'Yusuf',
        englishNameTranslation: 'Joseph',
        numberOfAyahs: 111,
        revelationType: 'Meccan',
        juz: 12,
        hizb: 23,
        rukus: 12,
      ),
      Surah(
        number: 13,
        name: 'الرعد',
        englishName: 'Ar-Rad',
        englishNameTranslation: 'The Thunder',
        numberOfAyahs: 43,
        revelationType: 'Medinan',
        juz: 13,
        hizb: 25,
        rukus: 6,
      ),
      Surah(
        number: 14,
        name: 'إبراهيم',
        englishName: 'Ibrahim',
        englishNameTranslation: 'Abraham',
        numberOfAyahs: 52,
        revelationType: 'Meccan',
        juz: 13,
        hizb: 25,
        rukus: 7,
      ),
      Surah(
        number: 15,
        name: 'الحجر',
        englishName: 'Al-Hijr',
        englishNameTranslation: 'The Rocky Tract',
        numberOfAyahs: 99,
        revelationType: 'Meccan',
        juz: 14,
        hizb: 27,
        rukus: 6,
      ),
      Surah(
        number: 16,
        name: 'النحل',
        englishName: 'An-Nahl',
        englishNameTranslation: 'The Bee',
        numberOfAyahs: 128,
        revelationType: 'Meccan',
        juz: 14,
        hizb: 27,
        rukus: 16,
      ),
      Surah(
        number: 17,
        name: 'الإسراء',
        englishName: 'Al-Isra',
        englishNameTranslation: 'The Night Journey',
        numberOfAyahs: 111,
        revelationType: 'Meccan',
        juz: 15,
        hizb: 29,
        rukus: 12,
      ),
      Surah(
        number: 18,
        name: 'الكهف',
        englishName: 'Al-Kahf',
        englishNameTranslation: 'The Cave',
        numberOfAyahs: 110,
        revelationType: 'Meccan',
        juz: 15,
        hizb: 29,
        rukus: 12,
      ),
      Surah(
        number: 19,
        name: 'مريم',
        englishName: 'Maryam',
        englishNameTranslation: 'Mary',
        numberOfAyahs: 98,
        revelationType: 'Meccan',
        juz: 16,
        hizb: 31,
        rukus: 6,
      ),
      Surah(
        number: 20,
        name: 'طه',
        englishName: 'Taha',
        englishNameTranslation: 'Ta-Ha',
        numberOfAyahs: 135,
        revelationType: 'Meccan',
        juz: 16,
        hizb: 31,
        rukus: 8,
      ),
    ];
  }

  // Method to initialize the database with Quran data
  Future<void> initializeQuranData() async {
    try {
      await _databaseService.initializeDatabase();

      // Check if data already exists
      final existingSurahs = await _databaseService.getAllSurahs();
      if (existingSurahs.isEmpty) {
        // Load basic surah data
        final basicSurahs = QuranData.getAllSurahs();
        await _databaseService.insertSurahs(basicSurahs);
      }
    } catch (e) {
      // Ignore database initialization errors (e.g., in web environment)
    }
  }

  // Method to download and cache all Quran data
  Future<void> downloadCompleteQuran() async {
    try {
      final surahs = await getAllSurahs();

      for (final surah in surahs) {
        try {
          await getSurahWithAyahs(surah.number);
          // Add a small delay to avoid overwhelming the API
          await Future.delayed(const Duration(milliseconds: 100));
        } catch (e) {
          // Continue with next surah if one fails
          continue;
        }
      }
    } catch (e) {
      throw Exception('فشل في تحميل القرآن الكامل: ${e.toString()}');
    }
  }

  // Method to check if complete Quran is available offline
  Future<bool> isCompleteQuranAvailable() async {
    try {
      final surahs = await _databaseService.getAllSurahs();
      if (surahs.length < AppConstants.totalSurahs) {
        return false;
      }

      // Check if at least some surahs have ayahs
      int surahsWithAyahs = 0;
      for (final surah in surahs.take(10)) {
        // Check first 10 surahs
        final surahWithAyahs = await _databaseService.getSurahWithAyahs(
          surah.number,
        );
        if (surahWithAyahs != null && surahWithAyahs.ayahs.isNotEmpty) {
          surahsWithAyahs++;
        }
      }

      return surahsWithAyahs >= 5; // At least 5 surahs should have ayahs
    } catch (e) {
      return false;
    }
  }
}
