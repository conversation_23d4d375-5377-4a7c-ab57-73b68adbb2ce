#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def clean_quran_text():
    """تنظيف نص القرآن من الأسطر الجديدة الزائدة"""
    
    print("تنظيف نص القرآن...")
    
    try:
        # قراءة الملف
        with open('assets/data/quran_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # تنظيف النص
        for surah in data['surahs']:
            for ayah in surah['ayahs']:
                # إزالة الأسطر الجديدة والمسافات الزائدة
                ayah['text'] = ayah['text'].strip().replace('\n', '').replace('\r', '')
        
        # حفظ الملف المنظف
        with open('assets/data/quran_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("تم تنظيف النص بنجاح!")
        
        # إحصائيات
        total_surahs = len(data['surahs'])
        total_ayahs = sum(len(surah['ayahs']) for surah in data['surahs'])
        print(f"عدد السور: {total_surahs}")
        print(f"إجمالي الآيات: {total_ayahs}")
        
    except Exception as e:
        print(f"خطأ في تنظيف النص: {e}")

if __name__ == "__main__":
    clean_quran_text()
