import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/library_provider.dart';
import '../providers/audio_provider.dart';
import '../models/library_models.dart';
import '../models/reciter.dart' as audio_models;

class ReciterSelectionScreen extends StatefulWidget {
  const ReciterSelectionScreen({super.key});

  @override
  State<ReciterSelectionScreen> createState() => _ReciterSelectionScreenState();
}

class _ReciterSelectionScreenState extends State<ReciterSelectionScreen> {
  String _searchQuery = '';
  String _selectedCountry = 'الكل';
  String _selectedStyle = 'الكل';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LibraryProvider>().loadReciters();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final libraryProvider = context.watch<LibraryProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    final filteredReciters = libraryProvider.reciters.where((reciter) {
      // تصفية حسب البلد
      if (_selectedCountry != 'الكل' && reciter.country != _selectedCountry) {
        return false;
      }

      // تصفية حسب النمط
      if (_selectedStyle != 'الكل' && reciter.style != _selectedStyle) {
        return false;
      }

      // تصفية حسب البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return reciter.nameArabic.toLowerCase().contains(query) ||
            reciter.name.toLowerCase().contains(query);
      }

      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار القارئ'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showReciterInfo(context),
            tooltip: 'معلومات القراء',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'البحث عن قارئ...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Filters
          _buildFilters(libraryProvider),

          // Current Selection
          if (libraryProvider.selectedReciter != null)
            Container(
              margin: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
              ),
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: themeProvider.getThemeColor(context).withOpacity(0.1),
                borderRadius:
                    BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: themeProvider.getThemeColor(context).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: themeProvider.getThemeColor(context),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'القارئ المختار حالياً',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: themeProvider.getThemeColor(context),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          libraryProvider.selectedReciter!.nameArabic,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Reciters List
          Expanded(
            child: filteredReciters.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: theme.textTheme.bodyMedium?.color
                              ?.withOpacity(0.3),
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        Text(
                          'لا توجد نتائج للبحث',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.textTheme.bodyMedium?.color
                                ?.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                    ),
                    itemCount: filteredReciters.length,
                    itemBuilder: (context, index) {
                      final reciter = filteredReciters[index];
                      return _buildReciterCard(
                          reciter, libraryProvider, theme, themeProvider);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildReciterCard(
    Reciter reciter,
    LibraryProvider libraryProvider,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    final isSelected = libraryProvider.selectedReciter?.id == reciter.id;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: InkWell(
        onTap: () => _showReciterConfirmationDialog(reciter, libraryProvider),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Selection Indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? themeProvider.getThemeColor(context)
                        : theme.dividerColor,
                    width: 2,
                  ),
                  color: isSelected
                      ? themeProvider.getThemeColor(context)
                      : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),
              const SizedBox(width: AppConstants.paddingMedium),

              // Reciter Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reciter.nameArabic,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? themeProvider.getThemeColor(context)
                            : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      reciter.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color:
                            theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: theme.textTheme.bodySmall?.color
                              ?.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reciter.country,
                          style: theme.textTheme.bodySmall,
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.music_note,
                          size: 16,
                          color: theme.textTheme.bodySmall?.color
                              ?.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reciter.style,
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Popular Badge
              if (reciter.isAvailable)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'متاح',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReciterInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات القراء'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'أنواع القراءة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• مرتل: قراءة بطيئة ومتأنية'),
              Text('• مجود: قراءة بالتجويد والتحسين'),
              Text('• تعليمي: قراءة تعليمية للحفظ'),
              SizedBox(height: 16),
              Text(
                'ملاحظة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'يمكنك تغيير القارئ في أي وقت من الإعدادات أو من هذه الشاشة.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(LibraryProvider libraryProvider) {
    final availableCountries = [
      'الكل',
      'مصر',
      'السعودية',
      'الكويت',
      'الإمارات',
      'موريتانيا'
    ];
    final availableStyles = ['الكل', 'مرتل', 'مجود'];

    return Container(
      padding:
          const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'البلد',
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                DropdownButtonFormField<String>(
                  value: _selectedCountry,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: availableCountries.map((country) {
                    return DropdownMenuItem(
                      value: country,
                      child:
                          Text(country, style: const TextStyle(fontSize: 14)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCountry = value!;
                    });
                  },
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'النمط',
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                DropdownButtonFormField<String>(
                  value: _selectedStyle,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: availableStyles.map((style) {
                    return DropdownMenuItem(
                      value: style,
                      child: Text(style, style: const TextStyle(fontSize: 14)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStyle = value!;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تأكيد تغيير القارئ
  void _showReciterConfirmationDialog(
      Reciter reciter, LibraryProvider libraryProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تغيير القارئ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل تريد تغيير القارئ إلى:'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppConstants.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: AppConstants.primaryColor.withOpacity(0.2),
                    child: const Icon(
                      Icons.person,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          reciter.nameArabic,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          '${reciter.country} - ${reciter.style}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'سيتم استخدام صوت هذا القارئ عند تشغيل القرآن الكريم.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final audioProvider = context.read<AudioProvider>();
              final wasPlaying =
                  audioProvider.playerState == PlayerState.playing;

              // تحديث القارئ المختار في كلا الـ providers
              libraryProvider.setSelectedReciter(reciter);

              // تحويل نوع القارئ للـ AudioProvider
              final audioReciter =
                  audio_models.PopularReciters.getById(reciter.id);
              if (audioReciter != null) {
                await audioProvider.selectReciter(audioReciter);
              }

              // إذا كان هناك تشغيل حالي، قم بتغيير القارئ فوراً
              if (wasPlaying && audioProvider.currentSurah != null) {
                await audioProvider.playSurah(audioProvider.currentSurah!);
              }

              if (!mounted) return;

              Navigator.pop(context);

              if (!mounted) return;

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(wasPlaying
                            ? 'تم تغيير القارئ إلى: ${reciter.nameArabic}'
                            : 'تم اختيار القارئ: ${reciter.nameArabic}'),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                  action: SnackBarAction(
                    label: wasPlaying ? 'ممتاز' : 'تجربة',
                    textColor: Colors.white,
                    onPressed: () => wasPlaying ? null : _testReciter(reciter),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// تجربة صوت القارئ
  void _testReciter(Reciter reciter) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تجربة صوت ${reciter.nameArabic}...'),
        duration: const Duration(seconds: 2),
      ),
    );
    // يمكن إضافة تشغيل عينة صوتية هنا
  }
}
