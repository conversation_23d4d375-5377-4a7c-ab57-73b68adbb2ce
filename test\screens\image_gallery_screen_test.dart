import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/screens/image_gallery_screen.dart';

void main() {
  group('ImageGalleryScreen Tests', () {
    testWidgets('should display image gallery screen',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      // التحقق من وجود العنوان
      expect(find.text('فهرس الصور'), findsOneWidget);

      // التحقق من وجود شريط البحث
      expect(find.byIcon(Icons.search), findsOneWidget);

      // التحقق من وجود أزرار التبديل
      expect(find.byIcon(Icons.view_list), findsOneWidget);
    });

    testWidgets('should show categories', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود فئة "الكل"
      expect(find.text('الكل'), findsOneWidget);

      // التحقق من وجود فئة "الفن الإسلامي"
      expect(find.text('الفن الإسلامي'), findsOneWidget);

      // التحقق من وجود فئة "المساجد"
      expect(find.text('المساجد'), findsOneWidget);

      // التحقق من وجود فئة "الخط العربي"
      expect(find.text('الخط العربي'), findsOneWidget);
    });

    testWidgets('should display images in grid view',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود شبكة الصور
      expect(find.byType(GridView), findsOneWidget);

      // التحقق من وجود بطاقات الصور
      expect(find.byType(Card), findsWidgets);
    });

    testWidgets('should switch to list view', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على زر تبديل العرض
      await tester.tap(find.byIcon(Icons.view_list));
      await tester.pumpAndSettle();

      // التحقق من تغيير العرض إلى قائمة (البحث عن ListView الرئيسي فقط)
      expect(find.byType(ListView).at(1), findsOneWidget);
    });

    testWidgets('should show search dialog', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على زر البحث
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // التحقق من ظهور نافذة البحث
      expect(find.text('البحث في الصور'), findsOneWidget);
      expect(find.text('ابحث عن صورة...'), findsOneWidget);
    });

    testWidgets('should show image details', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على أول صورة
      await tester.tap(find.byType(Card).first);
      await tester.pumpAndSettle();

      // التحقق من ظهور تفاصيل الصورة
      expect(find.text('إغلاق'), findsOneWidget);
      expect(find.text('تحميل'), findsOneWidget);
      expect(find.text('مشاركة'), findsOneWidget);
    });

    testWidgets('should filter by category', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على فئة "المساجد"
      await tester.tap(find.text('المساجد'));
      await tester.pumpAndSettle();

      // التحقق من تطبيق الفلتر
      // يجب أن تظهر صور المساجد فقط
      expect(find.byType(Card), findsWidgets);
    });

    testWidgets('should show statistics', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على قائمة الخيارات
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // النقر على الإحصائيات
      await tester.tap(find.text('الإحصائيات'));
      await tester.pumpAndSettle();

      // التحقق من ظهور نافذة الإحصائيات
      expect(find.text('إحصائيات المعرض'), findsOneWidget);
    });

    testWidgets('should show favorites', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ImageGalleryScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // النقر على قائمة الخيارات
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // النقر على المفضلة (استخدام byKey أو byType بدلاً من النص المكرر)
      await tester.tap(find.byIcon(Icons.favorite));
      await tester.pumpAndSettle();

      // التحقق من ظهور نافذة المفضلة
      expect(find.text('الصور المفضلة'), findsOneWidget);
    });
  });
}
