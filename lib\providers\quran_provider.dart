import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/surah.dart';
import '../services/quran_service.dart';
import '../constants/app_constants.dart';

class QuranProvider extends ChangeNotifier {
  final QuranService _quranService = QuranService();

  List<Surah> _surahs = [];
  Surah? _currentSurah;
  Ayah? _currentAyah;
  List<Ayah> _bookmarkedAyahs = [];
  List<Ayah> _searchResults = [];

  bool _isLoading = false;
  bool _isSearching = false;
  String _searchQuery = '';
  String? _errorMessage;

  int _lastReadSurahNumber = 1;
  int _lastReadAyahNumber = 1;

  // Getters
  List<Surah> get surahs => _surahs;
  Surah? get currentSurah => _currentSurah;
  Ayah? get currentAyah => _currentAyah;
  List<Ayah> get bookmarkedAyahs => _bookmarkedAyahs;
  List<Ayah> get searchResults => _searchResults;

  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String get searchQuery => _searchQuery;
  String? get errorMessage => _errorMessage;

  int get lastReadSurahNumber => _lastReadSurahNumber;
  int get lastReadAyahNumber => _lastReadAyahNumber;

  bool get hasSurahs => _surahs.isNotEmpty;
  bool get hasSearchResults => _searchResults.isNotEmpty;
  bool get hasBookmarks => _bookmarkedAyahs.isNotEmpty;

  QuranProvider() {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() => _loadInitialData());
  }

  Future<void> _loadInitialData() async {
    await _loadLastReadPosition();
    await loadSurahs();
    await _loadBookmarks();

    // إصلاح البيانات المحفوظة إذا كانت تحتوي على أخطاء
    if (_bookmarkedAyahs.isNotEmpty && _surahs.isNotEmpty) {
      await fixBookmarkedAyahs();
    }

    // إضافة بعض الآيات للمفضلة للاختبار إذا كانت فارغة
    if (_bookmarkedAyahs.isEmpty && _surahs.isNotEmpty) {
      await _addSampleBookmarks();
    }
  }

  /// إضافة آيات عينة للمفضلة للاختبار
  Future<void> _addSampleBookmarks() async {
    try {
      if (_surahs.isNotEmpty) {
        // إضافة الفاتحة
        final fatiha = _surahs.firstWhere((s) => s.number == 1);
        if (fatiha.ayahs.isNotEmpty) {
          // التأكد من أن الآية لها surahNumber صحيح
          final ayah = fatiha.ayahs.first;
          if (ayah.surahNumber == 0) {
            // إنشاء آية جديدة بـ surahNumber صحيح
            final correctedAyah = Ayah(
              number: ayah.number,
              numberInSurah: ayah.numberInSurah,
              text: ayah.text,
              surahNumber: 1, // تصحيح رقم السورة
              juz: ayah.juz,
              manzil: ayah.manzil,
              page: ayah.page,
              ruku: ayah.ruku,
              hizbQuarter: ayah.hizbQuarter,
              sajda: ayah.sajda,
              audioUrl: ayah.audioUrl,
            );
            await toggleBookmark(correctedAyah);
          } else {
            await toggleBookmark(ayah);
          }
        }

        // إضافة آية من البقرة إذا كانت متوفرة
        final baqarah = _surahs.where((s) => s.number == 2).firstOrNull;
        if (baqarah != null && baqarah.ayahs.isNotEmpty) {
          final ayah = baqarah.ayahs.first;
          if (ayah.surahNumber == 0) {
            // إنشاء آية جديدة بـ surahNumber صحيح
            final correctedAyah = Ayah(
              number: ayah.number,
              numberInSurah: ayah.numberInSurah,
              text: ayah.text,
              surahNumber: 2, // تصحيح رقم السورة
              juz: ayah.juz,
              manzil: ayah.manzil,
              page: ayah.page,
              ruku: ayah.ruku,
              hizbQuarter: ayah.hizbQuarter,
              sajda: ayah.sajda,
              audioUrl: ayah.audioUrl,
            );
            await toggleBookmark(correctedAyah);
          } else {
            await toggleBookmark(ayah);
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الآيات العينة: $e');
    }
  }

  Future<void> loadSurahs() async {
    if (_isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      // محاولة تحميل من الخدمة أولاً للحصول على جميع السور
      try {
        _surahs = await _quranService.getAllSurahs();
        debugPrint('✅ تم تحميل ${_surahs.length} سورة من الخدمة');
      } catch (e) {
        debugPrint('⚠️ فشل تحميل من الخدمة، استخدام الملف المحلي: $e');
        await _loadSurahsFromLocal();
        debugPrint('✅ تم تحميل ${_surahs.length} سورة من الملف المحلي');
      }

      // Set current surah to last read if available
      if (_surahs.isNotEmpty && _lastReadSurahNumber > 0) {
        _currentSurah = _surahs.firstWhere(
          (surah) => surah.number == _lastReadSurahNumber,
          orElse: () => _surahs.first,
        );
      }
    } catch (e) {
      _setError('فشل في تحميل السور: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل السور من الملف المحلي
  Future<void> _loadSurahsFromLocal() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/quran_text.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> surahsData = jsonData['surahs'];

      _surahs = surahsData.map((surahJson) {
        final List<dynamic> ayahsData = surahJson['ayahs'];
        final List<Ayah> ayahs = ayahsData.map((ayahJson) {
          return Ayah(
            number: ayahJson['number'],
            text: ayahJson['text'],
            numberInSurah: ayahJson['numberInSurah'],
            surahNumber: surahJson['number'],
            juz: 1, // قيمة افتراضية
            manzil: 1, // قيمة افتراضية
            page: 1, // قيمة افتراضية
            ruku: 1, // قيمة افتراضية
            hizbQuarter: 1, // قيمة افتراضية
            sajda: false, // قيمة افتراضية
          );
        }).toList();

        return Surah(
          number: surahJson['number'],
          name: surahJson['name'],
          englishName: surahJson['englishName'],
          englishNameTranslation: surahJson['englishNameTranslation'],
          revelationType: surahJson['revelationType'],
          numberOfAyahs: surahJson['numberOfAyahs'],
          juz: 1, // قيمة افتراضية
          hizb: 1, // قيمة افتراضية
          rukus: 1, // قيمة افتراضية
          ayahs: ayahs,
        );
      }).toList();

      debugPrint('📖 تم تحميل ${_surahs.length} سورة من الملف المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل السور من الملف المحلي: $e');
      rethrow;
    }
  }

  Future<void> loadSurahWithAyahs(int surahNumber) async {
    if (_isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      final surahWithAyahs = await _quranService.getSurahWithAyahs(surahNumber);

      // Update the surah in the list
      final index = _surahs.indexWhere((s) => s.number == surahNumber);
      if (index != -1) {
        _surahs[index] = surahWithAyahs;
      }

      _currentSurah = surahWithAyahs;

      // Set current ayah to last read if it's the same surah
      if (surahNumber == _lastReadSurahNumber &&
          surahWithAyahs.ayahs.isNotEmpty) {
        _currentAyah = surahWithAyahs.ayahs.firstWhere(
          (ayah) => ayah.numberInSurah == _lastReadAyahNumber,
          orElse: () => surahWithAyahs.ayahs.first,
        );
      }
    } catch (e) {
      _setError('فشل في تحميل السورة: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> searchInQuran(String query) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      _searchQuery = '';
      _isSearching = false;
      notifyListeners();
      return;
    }

    _isSearching = true;
    _searchQuery = query;
    notifyListeners();

    try {
      _searchResults = await _quranService.searchInQuran(query);
    } catch (e) {
      _setError('فشل في البحث: ${e.toString()}');
      _searchResults.clear();
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  void clearSearch() {
    _searchResults.clear();
    _searchQuery = '';
    _isSearching = false;
    notifyListeners();
  }

  Future<void> setCurrentAyah(Ayah ayah) async {
    _currentAyah = ayah;
    await _saveLastReadPosition(ayah.surahNumber, ayah.numberInSurah);
    notifyListeners();
  }

  Future<void> setCurrentSurah(Surah surah) async {
    _currentSurah = surah;
    if (surah.ayahs.isEmpty) {
      await loadSurahWithAyahs(surah.number);
    }
    notifyListeners();
  }

  Future<void> toggleBookmark(Ayah ayah) async {
    final isBookmarked = _bookmarkedAyahs.any((a) => a.number == ayah.number);

    if (isBookmarked) {
      _bookmarkedAyahs.removeWhere((a) => a.number == ayah.number);
    } else {
      // التأكد من أن الآية لها surahNumber صحيح
      Ayah ayahToAdd = ayah;
      if (ayah.surahNumber == 0) {
        // البحث عن السورة التي تحتوي على هذه الآية
        for (final surah in _surahs) {
          if (surah.ayahs.any((a) => a.number == ayah.number)) {
            ayahToAdd = Ayah(
              number: ayah.number,
              numberInSurah: ayah.numberInSurah,
              text: ayah.text,
              surahNumber: surah.number, // استخدام رقم السورة الصحيح
              juz: ayah.juz,
              manzil: ayah.manzil,
              page: ayah.page,
              ruku: ayah.ruku,
              hizbQuarter: ayah.hizbQuarter,
              sajda: ayah.sajda,
              audioUrl: ayah.audioUrl,
            );
            break;
          }
        }
      }
      _bookmarkedAyahs.add(ayahToAdd);
    }

    await _saveBookmarks();
    notifyListeners();
  }

  bool isAyahBookmarked(Ayah ayah) {
    return _bookmarkedAyahs.any((a) => a.number == ayah.number);
  }

  Future<void> removeBookmark(Ayah ayah) async {
    _bookmarkedAyahs.removeWhere((a) => a.number == ayah.number);
    await _saveBookmarks();
    notifyListeners();
  }

  Future<void> clearAllBookmarks() async {
    _bookmarkedAyahs.clear();
    await _saveBookmarks();
    notifyListeners();
  }

  /// إصلاح البيانات المحفوظة التي لها surahNumber خاطئ
  Future<void> fixBookmarkedAyahs() async {
    bool needsUpdate = false;

    for (int i = 0; i < _bookmarkedAyahs.length; i++) {
      final ayah = _bookmarkedAyahs[i];
      if (ayah.surahNumber == 0 || ayah.surahNumber > 114) {
        // البحث عن السورة الصحيحة
        for (final surah in _surahs) {
          if (surah.ayahs.any((a) => a.number == ayah.number)) {
            debugPrint(
                '🔧 إصلاح الآية ${ayah.number} - تغيير السورة من ${ayah.surahNumber} إلى ${surah.number}');

            _bookmarkedAyahs[i] = Ayah(
              number: ayah.number,
              numberInSurah: ayah.numberInSurah,
              text: ayah.text,
              surahNumber: surah.number, // تصحيح رقم السورة
              juz: ayah.juz,
              manzil: ayah.manzil,
              page: ayah.page,
              ruku: ayah.ruku,
              hizbQuarter: ayah.hizbQuarter,
              sajda: ayah.sajda,
              audioUrl: ayah.audioUrl,
            );
            needsUpdate = true;
            break;
          }
        }
      }
    }

    if (needsUpdate) {
      await _saveBookmarks();
      notifyListeners();
      debugPrint('✅ تم إصلاح البيانات المحفوظة');
    }
  }

  Surah? getSurahByNumber(int number) {
    try {
      return _surahs.firstWhere((surah) => surah.number == number);
    } catch (e) {
      return null;
    }
  }

  List<Surah> getSurahsByJuz(int juz) {
    return _surahs.where((surah) => surah.juz == juz).toList();
  }

  List<Surah> getMakkiSurahs() {
    return _surahs.where((surah) => surah.isMakki).toList();
  }

  List<Surah> getMadaniSurahs() {
    return _surahs.where((surah) => surah.isMadani).toList();
  }

  // Navigation methods
  Ayah? getNextAyah() {
    if (_currentAyah == null || _currentSurah == null) return null;

    final currentIndex = _currentSurah!.ayahs.indexWhere(
      (ayah) => ayah.number == _currentAyah!.number,
    );

    if (currentIndex < _currentSurah!.ayahs.length - 1) {
      return _currentSurah!.ayahs[currentIndex + 1];
    }

    return null;
  }

  Ayah? getPreviousAyah() {
    if (_currentAyah == null || _currentSurah == null) return null;

    final currentIndex = _currentSurah!.ayahs.indexWhere(
      (ayah) => ayah.number == _currentAyah!.number,
    );

    if (currentIndex > 0) {
      return _currentSurah!.ayahs[currentIndex - 1];
    }

    return null;
  }

  Future<void> goToNextAyah() async {
    final nextAyah = getNextAyah();
    if (nextAyah != null) {
      await setCurrentAyah(nextAyah);
    }
  }

  Future<void> goToPreviousAyah() async {
    final previousAyah = getPreviousAyah();
    if (previousAyah != null) {
      await setCurrentAyah(previousAyah);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> _loadLastReadPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lastReadSurahNumber = prefs.getInt(AppConstants.keyLastReadSurah) ?? 1;
      _lastReadAyahNumber = prefs.getInt(AppConstants.keyLastReadAyah) ?? 1;
    } catch (e) {
      debugPrint('Error loading last read position: $e');
    }
  }

  Future<void> _saveLastReadPosition(int surahNumber, int ayahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(AppConstants.keyLastReadSurah, surahNumber);
      await prefs.setInt(AppConstants.keyLastReadAyah, ayahNumber);

      _lastReadSurahNumber = surahNumber;
      _lastReadAyahNumber = ayahNumber;
    } catch (e) {
      debugPrint('Error saving last read position: $e');
    }
  }

  Future<void> _loadBookmarks() async {
    try {
      _bookmarkedAyahs = await _quranService.getBookmarkedAyahs();

      // إصلاح الآيات التي لها surahNumber = 0
      for (int i = 0; i < _bookmarkedAyahs.length; i++) {
        final ayah = _bookmarkedAyahs[i];
        if (ayah.surahNumber == 0) {
          // البحث عن السورة الصحيحة
          for (final surah in _surahs) {
            if (surah.ayahs.any((a) => a.number == ayah.number)) {
              // إنشاء آية جديدة بـ surahNumber صحيح
              _bookmarkedAyahs[i] = Ayah(
                number: ayah.number,
                numberInSurah: ayah.numberInSurah,
                text: ayah.text,
                surahNumber: surah.number, // تصحيح رقم السورة
                juz: ayah.juz,
                manzil: ayah.manzil,
                page: ayah.page,
                ruku: ayah.ruku,
                hizbQuarter: ayah.hizbQuarter,
                sajda: ayah.sajda,
                audioUrl: ayah.audioUrl,
              );
              break;
            }
          }
        }
      }

      // حفظ البيانات المصححة
      await _saveBookmarks();
    } catch (e) {
      debugPrint('Error loading bookmarks: $e');
    }
  }

  Future<void> _saveBookmarks() async {
    try {
      await _quranService.saveBookmarkedAyahs(_bookmarkedAyahs);
    } catch (e) {
      debugPrint('Error saving bookmarks: $e');
    }
  }
}
