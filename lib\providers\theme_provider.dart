import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  double _fontSize = AppConstants.fontSizeMedium;
  bool _isLoading = false;

  ThemeMode get themeMode => _themeMode;
  double get fontSize => _fontSize;
  bool get isLoading => _isLoading;
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  ThemeProvider() {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() => _loadThemePreferences());
  }

  Future<void> _loadThemePreferences() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode
      final themeModeIndex = prefs.getInt(AppConstants.keyThemeMode) ?? 0;
      _themeMode = ThemeMode.values[themeModeIndex];

      // Load font size
      _fontSize = prefs.getDouble(AppConstants.keyFontSize) ??
          AppConstants.fontSizeMedium;
    } catch (e) {
      debugPrint('Error loading theme preferences: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    if (_themeMode == themeMode) return;

    _themeMode = themeMode;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(AppConstants.keyThemeMode, themeMode.index);
    } catch (e) {
      debugPrint('Error saving theme mode: $e');
    }
  }

  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case ThemeMode.light:
        await setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setThemeMode(ThemeMode.system);
        break;
      case ThemeMode.system:
        await setThemeMode(ThemeMode.light);
        break;
    }
  }

  Future<void> setFontSize(double fontSize) async {
    if (_fontSize == fontSize) return;

    _fontSize = fontSize;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(AppConstants.keyFontSize, fontSize);
    } catch (e) {
      debugPrint('Error saving font size: $e');
    }
  }

  Future<void> increaseFontSize() async {
    const increment = 2.0;
    const maxSize = 32.0;

    if (_fontSize < maxSize) {
      await setFontSize(_fontSize + increment);
    }
  }

  Future<void> decreaseFontSize() async {
    const decrement = 2.0;
    const minSize = 12.0;

    if (_fontSize > minSize) {
      await setFontSize(_fontSize - decrement);
    }
  }

  Future<void> resetFontSize() async {
    await setFontSize(AppConstants.fontSizeMedium);
  }

  String get themeModeDisplayName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'تلقائي';
    }
  }

  IconData get themeModeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  Color getThemeColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark
        ? AppConstants.darkAccentColor
        : AppConstants.primaryColor;
  }

  TextStyle getQuranTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize,
      color: theme.textTheme.bodyLarge?.color,
      height: 2.0, // زيادة المسافة بين الأسطر
      fontWeight: FontWeight.w500, // جعل الخط أكثر وضوحاً
      letterSpacing: 0.5, // مسافة بين الحروف
      wordSpacing: 2.0, // مسافة بين الكلمات
    );
  }

  TextStyle getTranslationTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize - 2,
      color: theme.textTheme.bodyMedium?.color,
      height: 1.6,
      fontWeight: FontWeight.normal,
    );
  }

  TextStyle getSurahNameTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    return TextStyle(
      fontSize: _fontSize + 4,
      color: theme.textTheme.headlineMedium?.color,
      fontWeight: FontWeight.bold,
    );
  }

  // Animation durations based on theme
  Duration get shortAnimationDuration => const Duration(milliseconds: 200);
  Duration get mediumAnimationDuration => const Duration(milliseconds: 300);
  Duration get longAnimationDuration => const Duration(milliseconds: 500);

  // Helper method to get appropriate colors for different states
  Color getStateColor(
    BuildContext context, {
    required bool isSelected,
    required bool isPressed,
    required bool isDisabled,
  }) {
    final theme = Theme.of(context);

    if (isDisabled) {
      return theme.disabledColor;
    }

    if (isPressed) {
      return getThemeColor(context).withOpacity(0.8);
    }

    if (isSelected) {
      return getThemeColor(context);
    }

    return theme.textTheme.bodyMedium?.color ?? Colors.grey;
  }

  // Method to get gradient colors for backgrounds
  List<Color> getGradientColors(BuildContext context) {
    final brightness = Theme.of(context).brightness;

    if (brightness == Brightness.dark) {
      return [AppConstants.darkPrimaryColor, AppConstants.darkSecondaryColor];
    } else {
      return [AppConstants.primaryColor, AppConstants.secondaryColor];
    }
  }
}
