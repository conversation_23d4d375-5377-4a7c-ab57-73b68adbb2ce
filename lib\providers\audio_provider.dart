import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/reciter.dart';
import '../models/library_models.dart' as lib;
import '../models/surah.dart';
import '../constants/app_constants.dart';

enum RepeatMode { none, one, all }

enum PlayerState { stopped, loading, playing, paused, error }

class AudioProvider extends ChangeNotifier {
  final AudioPlayer? _audioPlayer = kIsWeb ? null : AudioPlayer();

  Reciter? _selectedReciter;
  Surah? _currentSurah;
  PlayerState _playerState = PlayerState.stopped;
  RepeatMode _repeatMode = RepeatMode.none;

  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _playbackSpeed = 1.0;
  bool _isAutoPlay = false;
  String? _errorMessage;

  List<Reciter> _availableReciters = [];

  // Getters
  Reciter? get selectedReciter => _selectedReciter;
  Surah? get currentSurah => _currentSurah;
  PlayerState get playerState => _playerState;
  RepeatMode get repeatMode => _repeatMode;

  Duration get duration => _duration;
  Duration get position => _position;
  double get playbackSpeed => _playbackSpeed;
  bool get isAutoPlay => _isAutoPlay;
  String? get errorMessage => _errorMessage;

  List<Reciter> get availableReciters => _availableReciters;

  bool get isPlaying => _playerState == PlayerState.playing;
  bool get isPaused => _playerState == PlayerState.paused;
  bool get isLoading => _playerState == PlayerState.loading;
  bool get isStopped => _playerState == PlayerState.stopped;
  bool get hasError => _playerState == PlayerState.error;

  double get progress => _duration.inMilliseconds > 0
      ? _position.inMilliseconds / _duration.inMilliseconds
      : 0.0;

  AudioProvider() {
    _initializeAudio();
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      _loadSettings();
      _loadAvailableReciters();
    });
  }

  void _initializeAudio() {
    if (!kIsWeb) {
      _initializeMobileAudio();
    }
  }

  void _initializeMobileAudio() {
    if (_audioPlayer == null) return;

    // Listen to player state changes
    _audioPlayer!.playerStateStream.listen((state) {
      switch (state.processingState) {
        case ProcessingState.idle:
          _playerState = PlayerState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          _playerState = PlayerState.loading;
          break;
        case ProcessingState.ready:
          _playerState =
              state.playing ? PlayerState.playing : PlayerState.paused;
          break;
        case ProcessingState.completed:
          _onPlaybackCompleted();
          break;
      }
      notifyListeners();
    });

    // Listen to position changes
    _audioPlayer!.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });

    // Listen to duration changes
    _audioPlayer!.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });

    // Listen to errors
    _audioPlayer!.playbackEventStream.listen(
      (event) {},
      onError: (error) {
        _setError('خطأ في تشغيل الصوت: ${error.toString()}');
      },
    );
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load selected reciter
      final reciterId = prefs.getString(AppConstants.keySelectedReciter);
      if (reciterId != null) {
        _selectedReciter = PopularReciters.getById(reciterId);
      }

      // Load auto play setting
      _isAutoPlay = prefs.getBool(AppConstants.keyAutoPlay) ?? false;

      // Load repeat mode
      final repeatModeIndex = prefs.getInt(AppConstants.keyRepeatMode) ?? 0;
      _repeatMode = RepeatMode.values[repeatModeIndex];

      // Load playback speed
      _playbackSpeed = prefs.getDouble('playback_speed') ?? 1.0;
    } catch (e) {
      debugPrint('Error loading audio settings: $e');
    }
  }

  void _loadAvailableReciters() {
    _availableReciters = PopularReciters.all;

    // Set default reciter if none selected
    if (_selectedReciter == null && _availableReciters.isNotEmpty) {
      _selectedReciter = _availableReciters.first;
    }

    notifyListeners();
  }

  Future<void> selectReciter(Reciter reciter) async {
    if (_selectedReciter?.id == reciter.id) return;

    debugPrint(
        '🔄 تغيير القارئ من ${_selectedReciter?.name} إلى ${reciter.name}');

    // حفظ حالة التشغيل الحالية
    final wasPlaying = isPlaying;
    final currentSurah = _currentSurah;
    final currentPosition = _audioPlayer?.position ?? Duration.zero;

    // إيقاف التشغيل الحالي بشكل كامل
    if (isPlaying) {
      debugPrint('⏹️ إيقاف التشغيل الحالي...');
      await stop();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // تغيير القارئ
    _selectedReciter = reciter;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter.id);
      debugPrint('💾 تم حفظ القارئ الجديد: ${reciter.name}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ القارئ: $e');
    }

    // إذا كان هناك تشغيل، أعد تشغيله بالقارئ الجديد
    if (wasPlaying && currentSurah != null) {
      debugPrint('🎵 إعادة تشغيل ${currentSurah.name} بالقارئ الجديد');
      await Future.delayed(const Duration(milliseconds: 800));

      await playSurah(currentSurah);

      // محاولة العودة إلى نفس الموضع
      if (currentPosition.inSeconds > 5) {
        await Future.delayed(const Duration(milliseconds: 1000));
        debugPrint('⏭️ العودة للموضع: ${currentPosition.inSeconds} ثانية');
        await seek(currentPosition);
      }
    }

    debugPrint('✅ تم تغيير القارئ بنجاح');
  }

  Future<void> playSurah(Surah surah) async {
    if (_selectedReciter == null) {
      _setError('لم يتم اختيار قارئ');
      return;
    }

    _currentSurah = surah;
    _clearError();
    _playerState = PlayerState.loading;
    notifyListeners();

    try {
      // Try to get audio URL from surah's first ayah if available
      String? audioUrl;
      if (surah.ayahs.isNotEmpty && surah.ayahs.first.audioUrl != null) {
        // Use the first ayah's audio URL (this will be the full surah audio from the API)
        audioUrl = surah.ayahs.first.audioUrl!;
      } else {
        // Fallback to reciter's URL
        audioUrl = _selectedReciter!.getAudioUrl(surah.number);
      }

      debugPrint('Playing audio URL: $audioUrl');

      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.setUrl(audioUrl);
        await _audioPlayer!.setSpeed(_playbackSpeed);
        await _audioPlayer!.play();
      }
    } catch (e) {
      _setError('فشل في تشغيل السورة: ${e.toString()}');
      _playerState = PlayerState.error;
      notifyListeners();
    }
  }

  Future<void> play() async {
    if (_currentSurah == null) return;

    try {
      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.play();
      }
    } catch (e) {
      _setError('فشل في التشغيل: ${e.toString()}');
    }
  }

  Future<void> pause() async {
    try {
      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.pause();
      }
    } catch (e) {
      _setError('فشل في الإيقاف المؤقت: ${e.toString()}');
    }
  }

  Future<void> stop() async {
    try {
      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.stop();
      }
      _position = Duration.zero;
      _playerState = PlayerState.stopped;
      notifyListeners();
    } catch (e) {
      _setError('فشل في الإيقاف: ${e.toString()}');
    }
  }

  Future<void> seek(Duration position) async {
    try {
      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.seek(position);
      }
    } catch (e) {
      _setError('فشل في التنقل: ${e.toString()}');
    }
  }

  /// تشغيل السورة بصوت قارئ محدد من المكتبة
  Future<void> playSurahWithLibraryReciter(
      Surah surah, lib.Reciter reciter) async {
    // إيقاف التشغيل الحالي أولاً إذا كان هناك تشغيل
    if (_audioPlayer != null && _playerState == PlayerState.playing) {
      await _audioPlayer!.stop();
      debugPrint('🛑 تم إيقاف التشغيل الحالي لتغيير القارئ');
    }

    _currentSurah = surah;
    _clearError();
    _playerState = PlayerState.loading;
    notifyListeners();

    try {
      final surahNumber = surah.number.toString().padLeft(3, '0');
      final audioUrl = reciter.audioUrls[surahNumber];

      if (audioUrl == null) {
        throw Exception('لا يوجد رابط صوتي للسورة مع هذا القارئ');
      }

      debugPrint('🎵 تشغيل السورة ${surah.name} بصوت ${reciter.nameArabic}');
      debugPrint('🔗 رابط الصوت: $audioUrl');

      if (!kIsWeb && _audioPlayer != null) {
        // التحقق من نوع الملف (محلي أم عبر الإنترنت)
        if (audioUrl.startsWith('assets/')) {
          // ملف محلي
          await _audioPlayer!.setAsset(audioUrl);
          debugPrint('🎵 تم تحديد ملف صوتي محلي: $audioUrl');
        } else {
          // ملف عبر الإنترنت
          await _audioPlayer!.setUrl(audioUrl);
          debugPrint('🌐 تم تحديد ملف صوتي عبر الإنترنت: $audioUrl');
        }

        await _audioPlayer!.setSpeed(_playbackSpeed);
        await _audioPlayer!.play();

        // تحديث الحالة بعد بدء التشغيل
        _playerState = PlayerState.playing;
        notifyListeners();
        debugPrint('✅ تم بدء التشغيل بنجاح بصوت ${reciter.nameArabic}');
      }
    } catch (e) {
      _setError('فشل في تشغيل السورة: ${e.toString()}');
      _playerState = PlayerState.error;
      notifyListeners();
      debugPrint('❌ خطأ في تشغيل السورة: $e');
    }
  }

  /// تغيير القارئ للسورة الحالية
  Future<void> changeReciterForCurrentSurah(lib.Reciter newReciter) async {
    if (_currentSurah == null) {
      debugPrint('⚠️ لا توجد سورة قيد التشغيل حالياً');
      return;
    }

    final currentPosition = _audioPlayer?.position ?? Duration.zero;
    debugPrint(
        '🔄 تغيير القارئ للسورة ${_currentSurah!.name} من الموضع: $currentPosition');

    try {
      // إيقاف التشغيل الحالي أولاً
      if (_playerState == PlayerState.playing) {
        await stop();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // تشغيل السورة بالقارئ الجديد
      await playSurahWithLibraryReciter(_currentSurah!, newReciter);

      // العودة إلى نفس الموضع إذا أمكن
      if (currentPosition.inSeconds > 5) {
        await Future.delayed(const Duration(
            milliseconds: 1000)); // انتظار أطول للتأكد من بدء التشغيل
        await _audioPlayer?.seek(currentPosition);
        debugPrint('⏭️ تم الانتقال إلى الموضع: $currentPosition');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تغيير القارئ: $e');
      _setError('فشل في تغيير القارئ: $e');
    }
  }

  Future<void> setPlaybackSpeed(double speed) async {
    if (speed < AppConstants.minPlaybackSpeed ||
        speed > AppConstants.maxPlaybackSpeed) {
      return;
    }

    _playbackSpeed = speed;
    notifyListeners();

    try {
      if (!kIsWeb && _audioPlayer != null) {
        await _audioPlayer!.setSpeed(speed);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('playback_speed', speed);
    } catch (e) {
      debugPrint('Error setting playback speed: $e');
    }
  }

  Future<void> setRepeatMode(RepeatMode mode) async {
    _repeatMode = mode;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(AppConstants.keyRepeatMode, mode.index);
    } catch (e) {
      debugPrint('Error saving repeat mode: $e');
    }
  }

  Future<void> toggleRepeatMode() async {
    switch (_repeatMode) {
      case RepeatMode.none:
        await setRepeatMode(RepeatMode.one);
        break;
      case RepeatMode.one:
        await setRepeatMode(RepeatMode.all);
        break;
      case RepeatMode.all:
        await setRepeatMode(RepeatMode.none);
        break;
    }
  }

  Future<void> setAutoPlay(bool autoPlay) async {
    _isAutoPlay = autoPlay;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyAutoPlay, autoPlay);
    } catch (e) {
      debugPrint('Error saving auto play setting: $e');
    }
  }

  void _onPlaybackCompleted() {
    switch (_repeatMode) {
      case RepeatMode.one:
        // Repeat current surah
        if (_currentSurah != null) {
          playSurah(_currentSurah!);
        }
        break;
      case RepeatMode.all:
        // Play next surah (implementation depends on surah list)
        // This would need to be coordinated with QuranProvider
        break;
      case RepeatMode.none:
        _playerState = PlayerState.stopped;
        _position = Duration.zero;
        notifyListeners();
        break;
    }
  }

  void _setError(String error) {
    _errorMessage = error;
    _playerState = PlayerState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Helper methods
  String get repeatModeDisplayName {
    switch (_repeatMode) {
      case RepeatMode.none:
        return 'بدون تكرار';
      case RepeatMode.one:
        return 'تكرار السورة';
      case RepeatMode.all:
        return 'تكرار الكل';
    }
  }

  IconData get repeatModeIcon {
    switch (_repeatMode) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
      case RepeatMode.all:
        return Icons.repeat;
    }
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    if (!kIsWeb) {
      _audioPlayer?.dispose();
    }
    super.dispose();
  }
}
