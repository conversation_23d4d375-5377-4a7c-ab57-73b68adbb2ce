import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../providers/theme_provider.dart';
import '../models/surah.dart';
import 'quran_reader_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Auto focus on search field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final quranProvider = context.watch<QuranProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في القرآن الكريم'),
        actions: [
          if (_searchQuery.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
                quranProvider.clearSearch();
              },
              tooltip: 'مسح البحث',
            ),
        ],
      ),
      body: Column(
        children: [
          // Search Input
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: 'ابحث في القرآن الكريم...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          quranProvider.clearSearch();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                if (value.trim().isNotEmpty) {
                  quranProvider.searchInQuran(value);
                } else {
                  quranProvider.clearSearch();
                }
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  quranProvider.searchInQuran(value);
                }
              },
            ),
          ),

          // Search Results
          Expanded(
            child: _buildSearchContent(quranProvider, theme, themeProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchContent(
    QuranProvider quranProvider,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    if (_searchQuery.isEmpty) {
      return _buildSearchSuggestions(quranProvider, theme);
    }

    if (quranProvider.isSearching) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppConstants.paddingMedium),
            Text('جاري البحث...'),
          ],
        ),
      );
    }

    if (quranProvider.searchResults.isEmpty) {
      return _buildNoResults(theme);
    }

    return _buildSearchResults(quranProvider, theme, themeProvider);
  }

  Widget _buildSearchSuggestions(QuranProvider quranProvider, ThemeData theme) {
    final popularSurahs = [
      quranProvider.getSurahByNumber(1), // الفاتحة
      quranProvider.getSurahByNumber(2), // البقرة
      quranProvider.getSurahByNumber(18), // الكهف
      quranProvider.getSurahByNumber(36), // يس
      quranProvider.getSurahByNumber(112), // الإخلاص
    ].where((surah) => surah != null).cast<Surah>().toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Tips
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: theme.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'نصائح للبحث',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  const Text('• ابحث بكلمة أو عبارة من القرآن الكريم'),
                  const Text('• يمكنك البحث بجزء من الكلمة'),
                  const Text('• البحث يشمل جميع آيات القرآن الكريم'),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Popular Surahs
          if (popularSurahs.isNotEmpty) ...[
            Text(
              'السور الشائعة',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ...popularSurahs.map((surah) => Card(
              margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
              child: ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Center(
                    child: Text(
                      '${surah.number}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                title: Text(surah.name),
                subtitle: Text('${surah.numberOfAyahs} آية - ${surah.revelationTypeArabic}'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => QuranReaderScreen(surah: surah),
                    ),
                  );
                },
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildNoResults(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.3),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'لا توجد نتائج',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'جرب البحث بكلمات أخرى',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(
    QuranProvider quranProvider,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    return Column(
      children: [
        // Results Count
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          color: theme.colorScheme.surface,
          child: Text(
            'تم العثور على ${quranProvider.searchResults.length} نتيجة',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // Results List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: quranProvider.searchResults.length,
            itemBuilder: (context, index) {
              final ayah = quranProvider.searchResults[index];
              final surah = quranProvider.getSurahByNumber(ayah.surahNumber);
              return _buildSearchResultCard(ayah, surah, theme, themeProvider, quranProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultCard(
    Ayah ayah,
    Surah? surah,
    ThemeData theme,
    ThemeProvider themeProvider,
    QuranProvider quranProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: InkWell(
        onTap: () {
          if (surah != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuranReaderScreen(surah: surah),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Surah and Ayah Info
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${surah?.name ?? 'سورة غير معروفة'} - آية ${ayah.numberInSurah}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: Icon(
                      quranProvider.isAyahBookmarked(ayah)
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      color: quranProvider.isAyahBookmarked(ayah)
                          ? theme.primaryColor
                          : null,
                    ),
                    onPressed: () => quranProvider.toggleBookmark(ayah),
                    tooltip: 'إضافة للمفضلة',
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // Ayah Text with Highlighting
              RichText(
                text: _buildHighlightedText(
                  ayah.text,
                  _searchQuery,
                  themeProvider.getQuranTextStyle(context),
                  theme.primaryColor,
                ),
                textAlign: TextAlign.justify,
              ),
            ],
          ),
        ),
      ),
    );
  }

  TextSpan _buildHighlightedText(
    String text,
    String query,
    TextStyle baseStyle,
    Color highlightColor,
  ) {
    if (query.isEmpty) {
      return TextSpan(text: text, style: baseStyle);
    }

    final List<TextSpan> spans = [];
    final String lowerText = text.toLowerCase();
    final String lowerQuery = query.toLowerCase();
    
    int start = 0;
    int index = lowerText.indexOf(lowerQuery);
    
    while (index != -1) {
      // Add text before match
      if (index > start) {
        spans.add(TextSpan(
          text: text.substring(start, index),
          style: baseStyle,
        ));
      }
      
      // Add highlighted match
      spans.add(TextSpan(
        text: text.substring(index, index + query.length),
        style: baseStyle.copyWith(
          backgroundColor: highlightColor.withOpacity(0.3),
          fontWeight: FontWeight.bold,
        ),
      ));
      
      start = index + query.length;
      index = lowerText.indexOf(lowerQuery, start);
    }
    
    // Add remaining text
    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: baseStyle,
      ));
    }
    
    return TextSpan(children: spans);
  }
}
