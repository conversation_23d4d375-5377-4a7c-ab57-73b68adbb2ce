import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noor_quran/services/progress_service.dart';

void main() {
  group('ProgressService Tests', () {
    late ProgressService progressService;

    setUp(() {
      // Initialize SharedPreferences with empty values
      SharedPreferences.setMockInitialValues({});
      progressService = ProgressService();
    });

    group('Reading Progress', () {
      test('should mark surah as read', () async {
        // Act
        await progressService.markSurahAsRead(1);
        
        // Assert
        final readSurahs = await progressService.getReadSurahs();
        expect(readSurahs, contains(1));
        
        final isRead = await progressService.isSurahRead(1);
        expect(isRead, true);
      });

      test('should not duplicate read surahs', () async {
        // Act
        await progressService.markSurahAsRead(1);
        await progressService.markSurahAsRead(1);
        
        // Assert
        final readSurahs = await progressService.getReadSurahs();
        expect(readSurahs.where((s) => s == 1).length, 1);
      });

      test('should mark ayah as read', () async {
        // Act
        await progressService.markAyahAsRead(1);
        
        // Assert
        final readAyahs = await progressService.getReadAyahs();
        expect(readAyahs, contains(1));
        
        final isRead = await progressService.isAyahRead(1);
        expect(isRead, true);
      });

      test('should return false for unread surah', () async {
        // Act
        final isRead = await progressService.isSurahRead(999);
        
        // Assert
        expect(isRead, false);
      });
    });

    group('Reading Streak', () {
      test('should start with zero streak', () async {
        // Act
        final streak = await progressService.getReadingStreak();
        
        // Assert
        expect(streak, 0);
      });

      test('should increment streak when reading', () async {
        // Act
        await progressService.markSurahAsRead(1);
        
        // Assert
        final streak = await progressService.getReadingStreak();
        expect(streak, 1);
      });
    });

    group('Reading Time', () {
      test('should start with zero reading time', () async {
        // Act
        final totalTime = await progressService.getTotalReadingTime();
        
        // Assert
        expect(totalTime, Duration.zero);
      });

      test('should add reading time correctly', () async {
        // Arrange
        const duration = Duration(minutes: 30);
        
        // Act
        await progressService.addReadingTime(duration);
        
        // Assert
        final totalTime = await progressService.getTotalReadingTime();
        expect(totalTime, duration);
      });

      test('should accumulate reading time', () async {
        // Arrange
        const duration1 = Duration(minutes: 15);
        const duration2 = Duration(minutes: 20);
        
        // Act
        await progressService.addReadingTime(duration1);
        await progressService.addReadingTime(duration2);
        
        // Assert
        final totalTime = await progressService.getTotalReadingTime();
        expect(totalTime, const Duration(minutes: 35));
      });
    });

    group('Goals Management', () {
      test('should set and get daily goal', () async {
        // Arrange
        const goal = 15;
        
        // Act
        await progressService.setDailyGoal(goal);
        final retrievedGoal = await progressService.getDailyGoal();
        
        // Assert
        expect(retrievedGoal, goal);
      });

      test('should set and get weekly goal', () async {
        // Arrange
        const goal = 5;
        
        // Act
        await progressService.setWeeklyGoal(goal);
        final retrievedGoal = await progressService.getWeeklyGoal();
        
        // Assert
        expect(retrievedGoal, goal);
      });

      test('should set and get monthly goal', () async {
        // Arrange
        const goal = 20;
        
        // Act
        await progressService.setMonthlyGoal(goal);
        final retrievedGoal = await progressService.getMonthlyGoal();
        
        // Assert
        expect(retrievedGoal, goal);
      });

      test('should return default goals when not set', () async {
        // Act
        final dailyGoal = await progressService.getDailyGoal();
        final weeklyGoal = await progressService.getWeeklyGoal();
        final monthlyGoal = await progressService.getMonthlyGoal();
        
        // Assert
        expect(dailyGoal, 10); // Default daily goal
        expect(weeklyGoal, 3); // Default weekly goal
        expect(monthlyGoal, 10); // Default monthly goal
      });
    });

    group('Progress Calculations', () {
      test('should calculate daily progress', () async {
        // Arrange
        await progressService.setDailyGoal(10);
        
        // Act
        final progress = await progressService.getDailyProgress();
        
        // Assert
        expect(progress, isA<double>());
        expect(progress, greaterThanOrEqualTo(0.0));
        expect(progress, lessThanOrEqualTo(1.0));
      });

      test('should calculate weekly progress', () async {
        // Arrange
        await progressService.setWeeklyGoal(5);
        await progressService.markSurahAsRead(1);
        await progressService.markSurahAsRead(2);
        
        // Act
        final progress = await progressService.getWeeklyProgress();
        
        // Assert
        expect(progress, isA<double>());
        expect(progress, greaterThanOrEqualTo(0.0));
        expect(progress, lessThanOrEqualTo(1.0));
      });

      test('should calculate monthly progress', () async {
        // Arrange
        await progressService.setMonthlyGoal(10);
        await progressService.markSurahAsRead(1);
        await progressService.markSurahAsRead(2);
        await progressService.markSurahAsRead(3);
        
        // Act
        final progress = await progressService.getMonthlyProgress();
        
        // Assert
        expect(progress, isA<double>());
        expect(progress, greaterThanOrEqualTo(0.0));
        expect(progress, lessThanOrEqualTo(1.0));
      });
    });

    group('Statistics', () {
      test('should return comprehensive statistics', () async {
        // Arrange
        await progressService.markSurahAsRead(1);
        await progressService.markSurahAsRead(2);
        await progressService.markAyahAsRead(1);
        await progressService.markAyahAsRead(2);
        await progressService.markAyahAsRead(3);
        await progressService.addReadingTime(const Duration(minutes: 45));
        
        // Act
        final stats = await progressService.getStatistics();
        
        // Assert
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats['readSurahs'], 2);
        expect(stats['readAyahs'], 3);
        expect(stats['totalReadingTime'], 45);
        expect(stats['completionPercentage'], isA<double>());
        expect(stats['dailyProgress'], isA<double>());
        expect(stats['weeklyProgress'], isA<double>());
        expect(stats['monthlyProgress'], isA<double>());
      });

      test('should return zero statistics when no progress', () async {
        // Act
        final stats = await progressService.getStatistics();
        
        // Assert
        expect(stats['readSurahs'], 0);
        expect(stats['readAyahs'], 0);
        expect(stats['readingStreak'], 0);
        expect(stats['totalReadingTime'], 0);
        expect(stats['completionPercentage'], 0.0);
      });
    });

    group('Reset Progress', () {
      test('should reset all progress', () async {
        // Arrange
        await progressService.markSurahAsRead(1);
        await progressService.markAyahAsRead(1);
        await progressService.addReadingTime(const Duration(minutes: 30));
        
        // Act
        await progressService.resetProgress();
        
        // Assert
        final readSurahs = await progressService.getReadSurahs();
        final readAyahs = await progressService.getReadAyahs();
        final streak = await progressService.getReadingStreak();
        final totalTime = await progressService.getTotalReadingTime();
        
        expect(readSurahs, isEmpty);
        expect(readAyahs, isEmpty);
        expect(streak, 0);
        expect(totalTime, Duration.zero);
      });
    });
  });
}
