/// نماذج البيانات للمكتبة الدينية
import 'package:flutter/foundation.dart';

/// نموذج الكتاب
class Book {
  final String id;
  final String title;
  final String author;
  final String description;
  final BookType type;
  final String? imageUrl;
  final List<Chapter> chapters;
  final Map<String, dynamic>? metadata;

  const Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.type,
    this.imageUrl,
    required this.chapters,
    this.metadata,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      description: json['description'] as String,
      type: BookType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BookType.fiqh,
      ),
      imageUrl: json['imageUrl'] as String?,
      chapters:
          (json['chapters'] as List<dynamic>?)
              ?.map((e) => Chapter.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'type': type.name,
      'imageUrl': imageUrl,
      'chapters': chapters.map((e) => e.toJson()).toList(),
      'metadata': metadata,
    };
  }
}

/// أنواع الكتب
enum BookType {
  fiqh('فقه'),
  hadith('حديث'),
  tafsir('تفسير'),
  aqeedah('عقيدة'),
  sirah('سيرة'),
  adab('أدب'),
  madh('مدح نبوي'),
  awrad('أوراد'),
  tasawwuf('تصوف'),
  other('أخرى');

  const BookType(this.displayName);
  final String displayName;
}

/// نموذج الفصل
class Chapter {
  final String id;
  final String title;
  final int order;
  final List<Section> sections;
  final String? summary;

  const Chapter({
    required this.id,
    required this.title,
    required this.order,
    required this.sections,
    this.summary,
  });

  factory Chapter.fromJson(Map<String, dynamic> json) {
    return Chapter(
      id: json['id'] as String,
      title: json['title'] as String,
      order: json['order'] as int,
      sections:
          (json['sections'] as List<dynamic>?)
              ?.map((e) => Section.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      summary: json['summary'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'order': order,
      'sections': sections.map((e) => e.toJson()).toList(),
      'summary': summary,
    };
  }
}

/// نموذج القسم
class Section {
  final String id;
  final String title;
  final String content;
  final int order;
  final SectionType type;
  final String? explanation;
  final String? audioUrl;
  final Map<String, dynamic>? metadata;

  const Section({
    required this.id,
    required this.title,
    required this.content,
    required this.order,
    required this.type,
    this.explanation,
    this.audioUrl,
    this.metadata,
  });

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      order: json['order'] as int,
      type: SectionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SectionType.text,
      ),
      explanation: json['explanation'] as String?,
      audioUrl: json['audioUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'order': order,
      'type': type.name,
      'explanation': explanation,
      'audioUrl': audioUrl,
      'metadata': metadata,
    };
  }

  /// تقسيم المحتوى إلى فقرات
  List<String> get paragraphs {
    return content
        .split('\n\n')
        .where((paragraph) => paragraph.trim().isNotEmpty)
        .map((paragraph) => paragraph.trim())
        .toList();
  }
}

/// أنواع الأقسام
enum SectionType {
  text('نص'),
  verse('آية'),
  hadith('حديث'),
  dua('دعاء'),
  poem('شعر'),
  explanation('شرح'),
  other('أخرى');

  const SectionType(this.displayName);
  final String displayName;
}

/// نموذج الحديث
class Hadith {
  final String id;
  final String text;
  final String narrator;
  final String source;
  final String? grade;
  final String? explanation;
  final List<String> tags;
  final bool isFavorite;

  const Hadith({
    required this.id,
    required this.text,
    required this.narrator,
    required this.source,
    this.grade,
    this.explanation,
    required this.tags,
    this.isFavorite = false,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'] as String,
      text: json['text'] as String,
      narrator: json['narrator'] as String,
      source: json['source'] as String,
      grade: json['grade'] as String?,
      explanation: json['explanation'] as String?,
      tags: (json['tags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      isFavorite: json['isFavorite'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'narrator': narrator,
      'source': source,
      'grade': grade,
      'explanation': explanation,
      'tags': tags,
      'isFavorite': isFavorite,
    };
  }

  Hadith copyWith({
    String? id,
    String? text,
    String? narrator,
    String? source,
    String? grade,
    String? explanation,
    List<String>? tags,
    bool? isFavorite,
  }) {
    return Hadith(
      id: id ?? this.id,
      text: text ?? this.text,
      narrator: narrator ?? this.narrator,
      source: source ?? this.source,
      grade: grade ?? this.grade,
      explanation: explanation ?? this.explanation,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

/// نموذج نتيجة البحث
class SearchResult {
  final String id;
  final String title;
  final String content;
  final String type;
  final String bookId;
  final String? chapterId;
  final String? sectionId;
  final double relevanceScore;

  const SearchResult({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.bookId,
    this.chapterId,
    this.sectionId,
    required this.relevanceScore,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: json['type'] as String,
      bookId: json['bookId'] as String,
      chapterId: json['chapterId'] as String?,
      sectionId: json['sectionId'] as String?,
      relevanceScore: (json['relevanceScore'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'bookId': bookId,
      'chapterId': chapterId,
      'sectionId': sectionId,
      'relevanceScore': relevanceScore,
    };
  }
}

/// نموذج القارئ
class Reciter {
  final String id;
  final String name;
  final String nameArabic;
  final String country;
  final String style;
  final String? imageUrl;
  final bool isAvailable;
  final Map<String, String> audioUrls;

  const Reciter({
    required this.id,
    required this.name,
    required this.nameArabic,
    required this.country,
    required this.style,
    this.imageUrl,
    this.isAvailable = true,
    required this.audioUrls,
  });

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      id: json['id'] as String,
      name: json['name'] as String,
      nameArabic: json['nameArabic'] as String,
      country: json['country'] as String,
      style: json['style'] as String,
      imageUrl: json['imageUrl'] as String?,
      isAvailable: json['isAvailable'] as bool? ?? true,
      audioUrls: Map<String, String>.from(json['audioUrls'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameArabic': nameArabic,
      'country': country,
      'style': style,
      'imageUrl': imageUrl,
      'isAvailable': isAvailable,
      'audioUrls': audioUrls,
    };
  }
}
