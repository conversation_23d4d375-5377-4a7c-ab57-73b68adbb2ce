import 'package:flutter/material.dart';
import '../screens/image_gallery_screen.dart';

/// تطبيق تجريبي لعرض صفحة فهرس الصور المطورة
class ImageGalleryDemo extends StatelessWidget {
  const ImageGalleryDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'معرض الصور الإسلامية',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
          titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
      ),
      home: const ImageGalleryScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// نقطة دخول التطبيق التجريبي
void main() {
  runApp(const ImageGalleryDemo());
}

/// صفحة رئيسية بديلة مع معاينة للميزات
class ImageGalleryPreview extends StatelessWidget {
  const ImageGalleryPreview({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('معرض الصور الإسلامية'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مقدمة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.image, color: Colors.blue[600], size: 32),
                        const SizedBox(width: 12),
                        const Text(
                          'معرض الصور المطور',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'مجموعة شاملة من الصور الإسلامية مع ميزات متقدمة للبحث والتصفح',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // الميزات
            const Text(
              'الميزات الجديدة:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            Expanded(
              child: ListView(
                children: [
                  _buildFeatureCard(
                    Icons.grid_view,
                    'عرضين مختلفين',
                    'شبكة وقائمة للصور',
                    Colors.blue,
                  ),
                  _buildFeatureCard(
                    Icons.favorite,
                    'المفضلة',
                    'حفظ الصور المفضلة',
                    Colors.red,
                  ),
                  _buildFeatureCard(
                    Icons.search,
                    'البحث المتقدم',
                    'بحث في العناوين والعلامات',
                    Colors.green,
                  ),
                  _buildFeatureCard(
                    Icons.analytics,
                    'الإحصائيات',
                    'معلومات شاملة عن المعرض',
                    Colors.orange,
                  ),
                  _buildFeatureCard(
                    Icons.star,
                    'التقييمات',
                    'تقييمات وعدد التحميلات',
                    Colors.amber,
                  ),
                  _buildFeatureCard(
                    Icons.category,
                    'الفئات المتنوعة',
                    'فن إسلامي، مساجد، خط عربي',
                    Colors.purple,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // زر الدخول
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ImageGalleryScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.arrow_forward),
                label: const Text(
                  'دخول المعرض',
                  style: TextStyle(fontSize: 18),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(IconData icon, String title, String description, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(description),
      ),
    );
  }
}
