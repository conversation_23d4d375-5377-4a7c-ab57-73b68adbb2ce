import json
import requests

# تحميل البيانات من المصدر
url = "https://cdn.jsdelivr.net/npm/quran-json@3.1.2/dist/quran.json"
response = requests.get(url)
quran_data = response.json()

# تحويل البيانات إلى التنسيق المطلوب
converted_data = {
    "surahs": []
}

verse_counter = 1

for surah in quran_data:
    converted_surah = {
        "number": surah["id"],
        "name": surah["name"],
        "englishName": surah["transliteration"],
        "englishNameTranslation": surah["transliteration"],
        "revelationType": "Meccan" if surah["type"] == "meccan" else "Medinan",
        "numberOfAyahs": surah["total_verses"],
        "ayahs": []
    }
    
    for verse in surah["verses"]:
        converted_verse = {
            "number": verse_counter,
            "text": verse["text"],
            "numberInSurah": verse["id"]
        }
        converted_surah["ayahs"].append(converted_verse)
        verse_counter += 1
    
    converted_data["surahs"].append(converted_surah)

# حفظ البيانات
with open("assets/data/complete_quran.json", "w", encoding="utf-8") as f:
    json.dump(converted_data, f, ensure_ascii=False, indent=2)

print(f"تم تحويل {len(converted_data['surahs'])} سورة بنجاح!")
print(f"إجمالي الآيات: {verse_counter - 1}")
