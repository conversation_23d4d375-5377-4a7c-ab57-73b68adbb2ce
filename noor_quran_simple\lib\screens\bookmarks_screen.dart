import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../models/quran_models.dart';

class BookmarksScreen extends StatelessWidget {
  const BookmarksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: () {
              _showClearAllDialog(context);
            },
          ),
        ],
      ),
      body: Consumer<QuranProvider>(
        builder: (context, quranProvider, child) {
          if (quranProvider.bookmarks.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: quranProvider.bookmarks.length,
            itemBuilder: (context, index) {
              final bookmark = quranProvider.bookmarks[index];
              return _buildBookmarkCard(context, bookmark, quranProvider);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddBookmarkDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bookmark_border, size: 80, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'لا توجد مفضلة',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'أضف آيات إلى المفضلة لتظهر هنا',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarkCard(
    BuildContext context,
    Bookmark bookmark,
    QuranProvider quranProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppConstants.accentColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(Icons.bookmark, color: Colors.white),
        ),
        title: Text(
          bookmark.surahName,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'آية ${bookmark.ayahNumber}',
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            color: AppConstants.textSecondaryColor,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () {
                // Play bookmark audio
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () {
                quranProvider.removeBookmark(
                  bookmark.surahNumber,
                  bookmark.ayahNumber,
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف المفضلة'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
          ],
        ),
        onTap: () {
          // Navigate to bookmark position
          quranProvider.setCurrentPosition(
            bookmark.surahNumber,
            bookmark.ayahNumber,
          );
        },
      ),
    );
  }

  void _showAddBookmarkDialog(BuildContext context) {
    final surahController = TextEditingController();
    final ayahController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مفضلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: surahController,
              decoration: const InputDecoration(
                labelText: 'رقم السورة',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: ayahController,
              decoration: const InputDecoration(
                labelText: 'رقم الآية',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final surah = int.tryParse(surahController.text);
              final ayah = int.tryParse(ayahController.text);

              if (surah != null && ayah != null) {
                Provider.of<QuranProvider>(
                  context,
                  listen: false,
                ).addBookmark(surah, ayah);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إضافة المفضلة'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع المفضلة'),
        content: const Text('هل أنت متأكد من حذف جميع المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // Clear all bookmarks
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف جميع المفضلة'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
