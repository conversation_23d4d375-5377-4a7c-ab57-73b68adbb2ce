import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/quran_models.dart';
import '../models/search_result.dart';
import '../services/quran_api_service.dart';
import '../services/local_quran_service.dart';
import '../services/database_service.dart';

class QuranProvider extends ChangeNotifier {
  int _currentSurah = 1;
  int _currentAyah = 1;
  bool _isLoading = false;
  List<Bookmark> _bookmarks = [];
  List<Surah> _surahs = [];
  List<SearchResult> _searchResults = [];
  String _lastSearchQuery = '';
  final DatabaseService _databaseService = DatabaseService();

  int get currentSurah => _currentSurah;
  int get currentAyah => _currentAyah;
  bool get isLoading => _isLoading;
  List<Bookmark> get bookmarks => _bookmarks;
  List<Surah> get surahs => _surahs;
  List<SearchResult> get searchResults => _searchResults;
  String get lastSearchQuery => _lastSearchQuery;

  QuranProvider() {
    _loadPreferences();
    _loadBookmarks();
    _loadSurahs();
  }

  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentSurah = prefs.getInt(AppConstants.keyLastReadSurah) ?? 1;
      _currentAyah = prefs.getInt(AppConstants.keyLastReadAyah) ?? 1;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading preferences: $e');
    }
  }

  Future<void> _loadBookmarks() async {
    try {
      _bookmarks = await _databaseService.getAllBookmarks();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading bookmarks: $e');
    }
  }

  Future<void> _loadSurahs() async {
    try {
      _isLoading = true;
      notifyListeners();

      // محاولة تحميل البيانات المحلية أولاً
      try {
        _surahs = await LocalQuranService.getLocalQuran();
        _isLoading = false;
        notifyListeners();
        return;
      } catch (localError) {
        debugPrint('فشل في تحميل البيانات المحلية: $localError');
        // في حالة فشل البيانات المحلية، استخدم API
        _surahs = await QuranApiService.getSurahs();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error loading surahs: $e');
    }
  }

  Future<void> setCurrentPosition(int surah, int ayah) async {
    _currentSurah = surah;
    _currentAyah = ayah;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(AppConstants.keyLastReadSurah, surah);
      await prefs.setInt(AppConstants.keyLastReadAyah, ayah);

      // إضافة إلى تاريخ القراءة
      final surahName = _surahs.isNotEmpty && surah <= _surahs.length
          ? _surahs[surah - 1].name
          : 'سورة رقم $surah';
      await _databaseService.addReadingHistory(surah, ayah, surahName);
    } catch (e) {
      debugPrint('Error saving position: $e');
    }
  }

  Future<void> addBookmark(
    int surah,
    int ayah, {
    String? surahName,
    String? ayahText,
  }) async {
    try {
      final name =
          surahName ??
          (_surahs.isNotEmpty && surah <= _surahs.length
              ? _surahs[surah - 1].name
              : 'سورة رقم $surah');

      final text = ayahText ?? 'آية رقم $ayah';

      final bookmark = Bookmark(
        id: 0, // سيتم تعيينه من قاعدة البيانات
        surahNumber: surah,
        ayahNumber: ayah,
        surahName: name,
        ayahText: text,
        createdAt: DateTime.now(),
      );

      await _databaseService.addBookmark(bookmark);
      await _loadBookmarks(); // إعادة تحميل المفضلة
    } catch (e) {
      debugPrint('Error adding bookmark: $e');
    }
  }

  Future<void> removeBookmark(int surah, int ayah) async {
    try {
      await _databaseService.removeBookmark(surah, ayah);
      await _loadBookmarks(); // إعادة تحميل المفضلة
    } catch (e) {
      debugPrint('Error removing bookmark: $e');
    }
  }

  Future<bool> isBookmarked(int surah, int ayah) async {
    try {
      return await _databaseService.isBookmarked(surah, ayah);
    } catch (e) {
      debugPrint('Error checking bookmark: $e');
      return false;
    }
  }

  Future<void> clearAllBookmarks() async {
    try {
      await _databaseService.clearAllBookmarks();
      await _loadBookmarks();
    } catch (e) {
      debugPrint('Error clearing bookmarks: $e');
    }
  }

  Future<void> searchQuran(String query) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      _lastSearchQuery = '';
      notifyListeners();
      return;
    }

    try {
      _isLoading = true;
      _lastSearchQuery = query;
      notifyListeners();

      // البحث في البيانات المحلية أولاً
      try {
        _searchResults = await LocalQuranService.searchLocalQuran(query);
        _isLoading = false;
        notifyListeners();
        return;
      } catch (localError) {
        debugPrint('فشل في البحث محلياً: $localError');
        // في حالة الفشل، استخدم API
        _searchResults = await QuranApiService.searchQuran(query);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error searching Quran: $e');
    }
  }

  Future<Surah?> getSurah(int surahNumber) async {
    try {
      // أولاً: تحقق من البيانات المحملة مسبقاً
      if (_surahs.isNotEmpty) {
        final surah = _surahs.firstWhere(
          (s) => s.number == surahNumber,
          orElse: () => throw Exception('السورة غير موجودة'),
        );

        // إذا كانت السورة تحتوي على آيات، أرجعها
        if (surah.ayahs.isNotEmpty) {
          return surah;
        }
      }

      // ثانياً: محاولة جلب السورة من البيانات المحلية
      try {
        final localSurah = await LocalQuranService.getLocalSurah(surahNumber);
        if (localSurah != null && localSurah.ayahs.isNotEmpty) {
          return localSurah;
        }
      } catch (localError) {
        debugPrint('فشل في جلب السورة محلياً: $localError');
      }

      // ثالثاً: في حالة الفشل، استخدم API
      try {
        return await QuranApiService.getSurah(surahNumber);
      } catch (apiError) {
        debugPrint('فشل في جلب السورة من API: $apiError');
      }

      return null;
    } catch (e) {
      debugPrint('Error getting surah: $e');
      return null;
    }
  }

  void clearSearch() {
    _searchResults = [];
    _lastSearchQuery = '';
    notifyListeners();
  }
}
