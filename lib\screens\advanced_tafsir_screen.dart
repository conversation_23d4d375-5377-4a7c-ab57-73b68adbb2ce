import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/tafsir_api_provider.dart';
import '../providers/theme_provider.dart';
import '../models/religious_book_models.dart';

class AdvancedTafsirScreen extends StatefulWidget {
  final int? initialSurah;
  final int? initialAyah;

  const AdvancedTafsirScreen({
    super.key,
    this.initialSurah,
    this.initialAyah,
  });

  @override
  State<AdvancedTafsirScreen> createState() => _AdvancedTafsirScreenState();
}

class _AdvancedTafsirScreenState extends State<AdvancedTafsirScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _surahController = TextEditingController();
  final TextEditingController _ayahController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<TafsirApiProvider>();
      provider.loadAvailableTafsirs();
      
      if (widget.initialSurah != null && widget.initialAyah != null) {
        provider.loadTafsirForVerse(widget.initialSurah!, widget.initialAyah!);
      } else {
        provider.loadTafsirForVerse(1, 1); // الفاتحة الآية الأولى
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _surahController.dispose();
    _ayahController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final tafsirProvider = context.watch<TafsirApiProvider>();

    return Scaffold(
      backgroundColor: themeProvider.isDarkMode ? Colors.black87 : Colors.grey[50],
      appBar: AppBar(
        title: const Text('التفاسير المتقدمة'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'goto_verse':
                  _showGoToVerseDialog(context);
                  break;
                case 'change_tafsir':
                  _showTafsirSelectionDialog(context);
                  break;
                case 'favorites':
                  _showFavorites(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'goto_verse',
                child: Row(
                  children: [
                    Icon(Icons.navigation),
                    SizedBox(width: 8),
                    Text('الانتقال لآية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'change_tafsir',
                child: Row(
                  children: [
                    Icon(Icons.book),
                    SizedBox(width: 8),
                    Text('تغيير التفسير'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'favorites',
                child: Row(
                  children: [
                    Icon(Icons.favorite),
                    SizedBox(width: 8),
                    Text('المفضلة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: tafsirProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات التفسير الحالي
                  _buildCurrentTafsirInfo(context, tafsirProvider),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // التفسير
                  if (tafsirProvider.currentVerse != null)
                    _buildTafsirContent(context, tafsirProvider.currentVerse!),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // أزرار التنقل
                  _buildNavigationButtons(context, tafsirProvider),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // إحصائيات التفسير
                  _buildTafsirStats(context, tafsirProvider),
                ],
              ),
            ),
    );
  }

  /// بناء معلومات التفسير الحالي
  Widget _buildCurrentTafsirInfo(BuildContext context, TafsirApiProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade400, Colors.purple.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            provider.getTafsirArabicName(provider.selectedTafsir),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'السورة ${provider.currentSurah} - الآية ${provider.currentAyah}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التفسير
  Widget _buildTafsirContent(BuildContext context, TafsirVerse verse) {
    final themeProvider = context.watch<ThemeProvider>();
    final tafsirProvider = context.watch<TafsirApiProvider>();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الآية القرآنية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: themeProvider.isDarkMode 
                    ? Colors.grey.shade800 
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: Colors.purple.shade300, width: 2),
              ),
              child: Text(
                verse.arabicText,
                style: const TextStyle(
                  fontSize: 24,
                  height: 1.8,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // عنوان التفسير
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'التفسير:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple.shade600,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        tafsirProvider.isFavorite(verse) ? Icons.favorite : Icons.favorite_border,
                        color: tafsirProvider.isFavorite(verse) ? Colors.red : Colors.grey,
                      ),
                      onPressed: () => tafsirProvider.toggleFavorite(verse),
                    ),
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () => _shareTafsir(verse),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            // نص التفسير
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Text(
                verse.tafsirTextArabic,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.6,
                ),
                textAlign: TextAlign.justify,
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // معلومات إضافية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getTafsirTypeColor(verse.type),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getTafsirTypeText(verse.type),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Text(
                  verse.authorNameArabic,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            
            // الكلمات المفتاحية
            if (verse.keywords.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: verse.keywords.map((keyword) => Chip(
                  label: Text(
                    keyword,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: Colors.purple.shade100,
                )).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أزرار التنقل
  Widget _buildNavigationButtons(BuildContext context, TafsirApiProvider provider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: provider.getPreviousVerse() != null
              ? () => provider.goToPreviousVerse()
              : null,
          icon: const Icon(Icons.arrow_back),
          label: const Text('السابق'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
        ),
        ElevatedButton.icon(
          onPressed: () => _showGoToVerseDialog(context),
          icon: const Icon(Icons.navigation),
          label: const Text('انتقال'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
        ElevatedButton.icon(
          onPressed: provider.getNextVerse() != null
              ? () => provider.goToNextVerse()
              : null,
          icon: const Icon(Icons.arrow_forward),
          label: const Text('التالي'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات التفسير
  Widget _buildTafsirStats(BuildContext context, TafsirApiProvider provider) {
    final stats = provider.tafsirStats;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات التفسير',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatColumn('المجموع', stats['total'].toString()),
              _buildStatColumn('الكلاسيكي', stats['classical'].toString()),
              _buildStatColumn('المعاصر', stats['modern'].toString()),
              _buildStatColumn('المفضلة', stats['favorites'].toString()),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عمود الإحصائية
  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple.shade600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في التفسير'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'ابحث في نص التفسير...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<TafsirApiProvider>().searchTafsir(_searchController.text);
              Navigator.pop(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الانتقال لآية
  void _showGoToVerseDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الانتقال لآية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _surahController,
              decoration: const InputDecoration(
                labelText: 'رقم السورة',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _ayahController,
              decoration: const InputDecoration(
                labelText: 'رقم الآية',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final surah = int.tryParse(_surahController.text);
              final ayah = int.tryParse(_ayahController.text);
              
              if (surah != null && ayah != null) {
                context.read<TafsirApiProvider>().loadTafsirForVerse(surah, ayah);
                Navigator.pop(context);
              }
            },
            child: const Text('انتقال'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار اختيار التفسير
  void _showTafsirSelectionDialog(BuildContext context) {
    final provider = context.read<TafsirApiProvider>();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار التفسير'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.tafsirOptions.length,
            itemBuilder: (context, index) {
              final option = provider.tafsirOptions[index];
              final isSelected = provider.selectedTafsir == option['id'];
              
              return RadioListTile<String>(
                title: Text(option['name']!),
                value: option['id']!,
                groupValue: provider.selectedTafsir,
                onChanged: (value) {
                  if (value != null) {
                    provider.setSelectedTafsir(value);
                    Navigator.pop(context);
                  }
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض المفضلة
  void _showFavorites(BuildContext context) {
    // يمكن تطوير هذا لاحقاً لعرض المفضلة في شاشة منفصلة
  }

  /// مشاركة التفسير
  void _shareTafsir(TafsirVerse verse) {
    // يمكن تطوير هذا لاحقاً لمشاركة التفسير
  }

  /// الحصول على لون نوع التفسير
  Color _getTafsirTypeColor(TafsirType type) {
    switch (type) {
      case TafsirType.classical:
        return Colors.brown;
      case TafsirType.modern:
        return Colors.blue;
      case TafsirType.jurisprudential:
        return Colors.green;
      case TafsirType.linguistic:
        return Colors.orange;
      case TafsirType.mystical:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على نص نوع التفسير
  String _getTafsirTypeText(TafsirType type) {
    switch (type) {
      case TafsirType.classical:
        return 'كلاسيكي';
      case TafsirType.modern:
        return 'معاصر';
      case TafsirType.jurisprudential:
        return 'فقهي';
      case TafsirType.linguistic:
        return 'لغوي';
      case TafsirType.mystical:
        return 'صوفي';
      default:
        return 'عام';
    }
  }
}
