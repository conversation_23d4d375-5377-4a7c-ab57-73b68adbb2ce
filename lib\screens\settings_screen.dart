import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/audio_provider.dart';
import '../models/reciter.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final audioProvider = context.watch<AudioProvider>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        children: [
          // Theme Settings
          _buildSectionHeader(context, 'المظهر والعرض'),
          _buildThemeSettings(context, themeProvider),
          const SizedBox(height: AppConstants.paddingLarge),

          // Font Settings
          _buildSectionHeader(context, 'إعدادات الخط'),
          _buildFontSettings(context, themeProvider),
          const SizedBox(height: AppConstants.paddingLarge),

          // Audio Settings
          _buildSectionHeader(context, 'إعدادات الصوت'),
          _buildAudioSettings(context, audioProvider),
          const SizedBox(height: AppConstants.paddingLarge),

          // App Info
          _buildSectionHeader(context, 'معلومات التطبيق'),
          _buildAppInfo(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildThemeSettings(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(themeProvider.themeModeIcon),
            title: const Text('المظهر'),
            subtitle: Text(themeProvider.themeModeDisplayName),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showThemeDialog(context, themeProvider),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.dark_mode),
            title: const Text('الوضع الليلي التلقائي'),
            subtitle: const Text('تفعيل الوضع الليلي حسب وقت النظام'),
            value: themeProvider.isSystemMode,
            onChanged: (value) {
              if (value) {
                themeProvider.setThemeMode(ThemeMode.system);
              } else {
                themeProvider.setThemeMode(ThemeMode.light);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFontSettings(BuildContext context, ThemeProvider themeProvider) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.text_fields),
            title: const Text('حجم الخط'),
            subtitle: Text('${themeProvider.fontSize.toInt()}'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.remove),
                  onPressed: themeProvider.decreaseFontSize,
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: themeProvider.increaseFontSize,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('إعادة تعيين حجم الخط'),
            subtitle: const Text('العودة للحجم الافتراضي'),
            onTap: themeProvider.resetFontSize,
          ),
        ],
      ),
    );
  }

  Widget _buildAudioSettings(BuildContext context, AudioProvider audioProvider) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('القارئ المفضل'),
            subtitle: Text(audioProvider.selectedReciter?.name ?? 'لم يتم الاختيار'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showReciterDialog(context, audioProvider),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.speed),
            title: const Text('سرعة التشغيل'),
            subtitle: Text('${audioProvider.playbackSpeed}x'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.remove),
                  onPressed: () {
                    final newSpeed = (audioProvider.playbackSpeed - 0.25)
                        .clamp(AppConstants.minPlaybackSpeed, AppConstants.maxPlaybackSpeed);
                    audioProvider.setPlaybackSpeed(newSpeed);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () {
                    final newSpeed = (audioProvider.playbackSpeed + 0.25)
                        .clamp(AppConstants.minPlaybackSpeed, AppConstants.maxPlaybackSpeed);
                    audioProvider.setPlaybackSpeed(newSpeed);
                  },
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.autorenew),
            title: const Text('التشغيل التلقائي'),
            subtitle: const Text('تشغيل السورة التالية تلقائياً'),
            value: audioProvider.isAutoPlay,
            onChanged: audioProvider.setAutoPlay,
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(audioProvider.repeatModeIcon),
            title: const Text('وضع التكرار'),
            subtitle: Text(audioProvider.repeatModeDisplayName),
            onTap: audioProvider.toggleRepeatMode,
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfo(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('إصدار التطبيق'),
            subtitle: Text(AppConstants.appVersion),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.description),
            title: const Text('حول التطبيق'),
            subtitle: const Text(AppConstants.appDescription),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.star),
            title: const Text('تقييم التطبيق'),
            subtitle: const Text('ساعدنا بتقييمك للتطبيق'),
            onTap: () {
              // TODO: Implement app rating
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('مشاركة التطبيق'),
            subtitle: const Text('شارك التطبيق مع الأصدقاء'),
            onTap: () {
              // TODO: Implement app sharing
            },
          ),
        ],
      ),
    );
  }

  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('فاتح'),
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('داكن'),
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('تلقائي'),
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showReciterDialog(BuildContext context, AudioProvider audioProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار القارئ'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: audioProvider.availableReciters.length,
            itemBuilder: (context, index) {
              final reciter = audioProvider.availableReciters[index];
              final isSelected = audioProvider.selectedReciter?.id == reciter.id;
              
              return RadioListTile<Reciter>(
                title: Text(reciter.name),
                subtitle: Text('${reciter.country} - ${reciter.styleDescription}'),
                value: reciter,
                groupValue: audioProvider.selectedReciter,
                onChanged: (value) {
                  if (value != null) {
                    audioProvider.selectReciter(value);
                    Navigator.pop(context);
                  }
                },
                selected: isSelected,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
