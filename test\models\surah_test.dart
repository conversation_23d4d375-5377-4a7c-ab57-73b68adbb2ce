import 'package:flutter_test/flutter_test.dart';
import 'package:noor_quran/models/surah.dart';

void main() {
  group('Surah Model Tests', () {
    test('should create Surah from JSON correctly', () {
      // Arrange
      final json = {
        'number': 1,
        'name': 'الفاتحة',
        'englishName': 'Al-Fatihah',
        'englishNameTranslation': 'The Opening',
        'numberOfAyahs': 7,
        'revelationType': 'Meccan',
        'juz': 1,
        'hizb': 1,
        'rukus': 1,
      };

      // Act
      final surah = Surah.fromJson(json);

      // Assert
      expect(surah.number, 1);
      expect(surah.name, 'الفاتحة');
      expect(surah.englishName, 'Al-Fatihah');
      expect(surah.englishNameTranslation, 'The Opening');
      expect(surah.numberOfAyahs, 7);
      expect(surah.revelationType, 'Meccan');
      expect(surah.juz, 1);
      expect(surah.hizb, 1);
      expect(surah.rukus, 1);
      expect(surah.isMakki, true);
      expect(surah.isMadani, false);
    });

    test('should create Surah from Map correctly', () {
      // Arrange
      final map = {
        'number': 2,
        'name': 'البقرة',
        'englishName': 'Al-Baqarah',
        'englishNameTranslation': 'The Cow',
        'numberOfAyahs': 286,
        'revelationType': 'Medinan',
        'juz': 1,
        'hizb': 1,
        'rukus': 40,
      };

      // Act
      final surah = Surah.fromMap(map);

      // Assert
      expect(surah.number, 2);
      expect(surah.name, 'البقرة');
      expect(surah.isMakki, false);
      expect(surah.isMadani, true);
      expect(surah.revelationTypeArabic, 'مدنية');
    });

    test('should convert Surah to JSON correctly', () {
      // Arrange
      final surah = Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'Meccan',
        juz: 1,
        hizb: 1,
        rukus: 1,
      );

      // Act
      final json = surah.toJson();

      // Assert
      expect(json['number'], 1);
      expect(json['name'], 'الفاتحة');
      expect(json['englishName'], 'Al-Fatihah');
      expect(json['revelationType'], 'Meccan');
    });

    test('should check equality correctly', () {
      // Arrange
      final surah1 = Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'Meccan',
        juz: 1,
        hizb: 1,
        rukus: 1,
      );

      final surah2 = Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'Meccan',
        juz: 1,
        hizb: 1,
        rukus: 1,
      );

      final surah3 = Surah(
        number: 2,
        name: 'البقرة',
        englishName: 'Al-Baqarah',
        englishNameTranslation: 'The Cow',
        numberOfAyahs: 286,
        revelationType: 'Medinan',
        juz: 1,
        hizb: 1,
        rukus: 40,
      );

      // Act & Assert
      expect(surah1, equals(surah2));
      expect(surah1, isNot(equals(surah3)));
      expect(surah1.hashCode, equals(surah2.hashCode));
    });

    test('should format name correctly', () {
      // Arrange
      final surah = Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'Meccan',
        juz: 1,
        hizb: 1,
        rukus: 1,
      );

      // Act & Assert
      expect(surah.formattedName, '1. الفاتحة');
      expect(surah.shortDescription, 'الفاتحة - 7 آية - مكية');
    });
  });

  group('Ayah Model Tests', () {
    test('should create Ayah from JSON correctly', () {
      // Arrange
      final json = {
        'number': 1,
        'numberInSurah': 1,
        'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        'surahNumber': 1,
        'juz': 1,
        'manzil': 1,
        'page': 1,
        'ruku': 1,
        'hizbQuarter': 1,
        'sajda': false,
      };

      // Act
      final ayah = Ayah.fromJson(json);

      // Assert
      expect(ayah.number, 1);
      expect(ayah.numberInSurah, 1);
      expect(ayah.text, 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ');
      expect(ayah.surahNumber, 1);
      expect(ayah.sajda, false);
      expect(ayah.hasSajda, false);
    });

    test('should format ayah text correctly', () {
      // Arrange
      final ayah = Ayah(
        number: 1,
        numberInSurah: 1,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        surahNumber: 1,
        juz: 1,
        manzil: 1,
        page: 1,
        ruku: 1,
        hizbQuarter: 1,
      );

      // Act & Assert
      expect(ayah.formattedText, 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ ﴿1﴾');
      expect(ayah.ayahReference, '1:1');
    });

    test('should handle sajda correctly', () {
      // Arrange
      final ayah = Ayah(
        number: 206,
        numberInSurah: 206,
        text: 'إِنَّ الَّذِينَ عِندَ رَبِّكَ لَا يَسْتَكْبِرُونَ عَنْ عِبَادَتِهِ وَيُسَبِّحُونَهُ وَلَهُ يَسْجُدُونَ',
        surahNumber: 7,
        juz: 9,
        manzil: 3,
        page: 171,
        ruku: 24,
        hizbQuarter: 35,
        sajda: true,
      );

      // Act & Assert
      expect(ayah.sajda, true);
      expect(ayah.hasSajda, true);
    });

    test('should check equality correctly', () {
      // Arrange
      final ayah1 = Ayah(
        number: 1,
        numberInSurah: 1,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        surahNumber: 1,
        juz: 1,
        manzil: 1,
        page: 1,
        ruku: 1,
        hizbQuarter: 1,
      );

      final ayah2 = Ayah(
        number: 1,
        numberInSurah: 1,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        surahNumber: 1,
        juz: 1,
        manzil: 1,
        page: 1,
        ruku: 1,
        hizbQuarter: 1,
      );

      final ayah3 = Ayah(
        number: 2,
        numberInSurah: 2,
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        surahNumber: 1,
        juz: 1,
        manzil: 1,
        page: 1,
        ruku: 1,
        hizbQuarter: 1,
      );

      // Act & Assert
      expect(ayah1, equals(ayah2));
      expect(ayah1, isNot(equals(ayah3)));
      expect(ayah1.hashCode, equals(ayah2.hashCode));
    });
  });
}
