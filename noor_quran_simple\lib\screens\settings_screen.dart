import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/audio_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        children: [
          _buildThemeSection(context),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildAudioSection(context),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildReadingSection(context),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildAboutSection(context),
        ],
      ),
    );
  }

  Widget _buildThemeSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المظهر',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(themeProvider.themeModeIcon),
                      title: const Text('وضع المظهر'),
                      subtitle: Text(themeProvider.themeModeString),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showThemeDialog(context, themeProvider);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.text_fields),
                      title: const Text('حجم الخط'),
                      subtitle: Text('${themeProvider.fontSize.toInt()}'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showFontSizeDialog(context, themeProvider);
                      },
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الصوت',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                return Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.person),
                      title: const Text('القارئ'),
                      subtitle: Text(_getReciterName(audioProvider.selectedReciter)),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showReciterDialog(context, audioProvider);
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.autorenew),
                      title: const Text('التشغيل التلقائي'),
                      subtitle: const Text('تشغيل السورة التالية تلقائياً'),
                      value: audioProvider.autoPlay,
                      onChanged: (value) {
                        audioProvider.toggleAutoPlay();
                      },
                    ),
                    SwitchListTile(
                      secondary: const Icon(Icons.repeat),
                      title: const Text('وضع التكرار'),
                      subtitle: const Text('تكرار السورة الحالية'),
                      value: audioProvider.repeatMode,
                      onChanged: (value) {
                        audioProvider.toggleRepeatMode();
                      },
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'القراءة',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('المفضلة'),
              subtitle: const Text('إدارة الآيات المحفوظة'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to bookmarks
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('سجل القراءة'),
              subtitle: const Text('عرض تاريخ القراءة'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to reading history
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حول التطبيق',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات التطبيق'),
              subtitle: Text('الإصدار ${AppConstants.appVersion}'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                _showAboutDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('تقييم التطبيق'),
              subtitle: const Text('ساعدنا بتقييمك'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Open app store for rating
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة التطبيق'),
              subtitle: const Text('شارك التطبيق مع الأصدقاء'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Share app
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر وضع المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('فاتح'),
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('داكن'),
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('النظام'),
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'حجم الخط: ${themeProvider.fontSize.toInt()}',
                  style: TextStyle(fontSize: themeProvider.fontSize),
                ),
                Slider(
                  value: themeProvider.fontSize,
                  min: 12.0,
                  max: 32.0,
                  divisions: 20,
                  onChanged: (value) {
                    themeProvider.setFontSize(value);
                    setState(() {});
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showReciterDialog(BuildContext context, AudioProvider audioProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر القارئ'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: AppConstants.popularReciters.length,
            itemBuilder: (context, index) {
              final reciter = AppConstants.popularReciters[index];
              return RadioListTile<String>(
                title: Text(reciter['name']!),
                value: reciter['identifier']!,
                groupValue: audioProvider.selectedReciter,
                onChanged: (value) {
                  if (value != null) {
                    audioProvider.setSelectedReciter(value);
                    Navigator.pop(context);
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: const Icon(Icons.menu_book, size: 50),
      children: [
        const Text(AppConstants.appDescription),
      ],
    );
  }

  String _getReciterName(String identifier) {
    final reciter = AppConstants.popularReciters.firstWhere(
      (r) => r['identifier'] == identifier,
      orElse: () => {'name': 'غير معروف'},
    );
    return reciter['name'] ?? 'غير معروف';
  }
}
