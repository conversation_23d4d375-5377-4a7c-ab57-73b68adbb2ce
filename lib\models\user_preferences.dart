/// نموذج تفضيلات المستخدم - لمسة شخصية لتخصيص التجربة
class UserPreferences {
  final double fontSize;
  final String themeMode;
  final String preferredReciter;
  final String language;
  final bool autoPlay;
  final bool showTranslation;
  final bool enableVibration;
  final bool enableSoundEffects;
  final double playbackSpeed;
  final int reminderFrequency; // بالدقائق
  final List<String> favoriteCategories;
  final Map<String, dynamic> customSettings;

  const UserPreferences({
    this.fontSize = 16.0,
    this.themeMode = 'system',
    this.preferredReciter = '',
    this.language = 'ar',
    this.autoPlay = false,
    this.showTranslation = false,
    this.enableVibration = true,
    this.enableSoundEffects = true,
    this.playbackSpeed = 1.0,
    this.reminderFrequency = 60,
    this.favoriteCategories = const [],
    this.customSettings = const {},
  });

  /// إنشاء نسخة محدثة من التفضيلات
  UserPreferences copyWith({
    double? fontSize,
    String? themeMode,
    String? preferredReciter,
    String? language,
    bool? autoPlay,
    bool? showTranslation,
    bool? enableVibration,
    bool? enableSoundEffects,
    double? playbackSpeed,
    int? reminderFrequency,
    List<String>? favoriteCategories,
    Map<String, dynamic>? customSettings,
  }) {
    return UserPreferences(
      fontSize: fontSize ?? this.fontSize,
      themeMode: themeMode ?? this.themeMode,
      preferredReciter: preferredReciter ?? this.preferredReciter,
      language: language ?? this.language,
      autoPlay: autoPlay ?? this.autoPlay,
      showTranslation: showTranslation ?? this.showTranslation,
      enableVibration: enableVibration ?? this.enableVibration,
      enableSoundEffects: enableSoundEffects ?? this.enableSoundEffects,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      reminderFrequency: reminderFrequency ?? this.reminderFrequency,
      favoriteCategories: favoriteCategories ?? this.favoriteCategories,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'fontSize': fontSize,
      'themeMode': themeMode,
      'preferredReciter': preferredReciter,
      'language': language,
      'autoPlay': autoPlay,
      'showTranslation': showTranslation,
      'enableVibration': enableVibration,
      'enableSoundEffects': enableSoundEffects,
      'playbackSpeed': playbackSpeed,
      'reminderFrequency': reminderFrequency,
      'favoriteCategories': favoriteCategories,
      'customSettings': customSettings,
    };
  }

  /// إنشاء من Map
  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      themeMode: map['themeMode'] ?? 'system',
      preferredReciter: map['preferredReciter'] ?? '',
      language: map['language'] ?? 'ar',
      autoPlay: map['autoPlay'] ?? false,
      showTranslation: map['showTranslation'] ?? false,
      enableVibration: map['enableVibration'] ?? true,
      enableSoundEffects: map['enableSoundEffects'] ?? true,
      playbackSpeed: map['playbackSpeed']?.toDouble() ?? 1.0,
      reminderFrequency: map['reminderFrequency'] ?? 60,
      favoriteCategories: List<String>.from(map['favoriteCategories'] ?? []),
      customSettings: Map<String, dynamic>.from(map['customSettings'] ?? {}),
    );
  }

  /// التحقق من صحة التفضيلات
  bool isValid() {
    return fontSize >= 12.0 && 
           fontSize <= 32.0 &&
           playbackSpeed >= 0.5 && 
           playbackSpeed <= 2.0 &&
           reminderFrequency >= 15 &&
           reminderFrequency <= 1440; // 24 ساعة
  }

  /// الحصول على حجم الخط المناسب للعنوان
  double get titleFontSize => fontSize + 4;

  /// الحصول على حجم الخط المناسب للنص الفرعي
  double get subtitleFontSize => fontSize - 2;

  /// التحقق من تفعيل الوضع المظلم
  bool get isDarkMode => themeMode == 'dark';

  /// التحقق من تفعيل الوضع التلقائي
  bool get isSystemMode => themeMode == 'system';

  @override
  String toString() {
    return 'UserPreferences(fontSize: $fontSize, themeMode: $themeMode, language: $language)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserPreferences &&
      other.fontSize == fontSize &&
      other.themeMode == themeMode &&
      other.preferredReciter == preferredReciter &&
      other.language == language &&
      other.autoPlay == autoPlay &&
      other.showTranslation == showTranslation &&
      other.enableVibration == enableVibration &&
      other.enableSoundEffects == enableSoundEffects &&
      other.playbackSpeed == playbackSpeed &&
      other.reminderFrequency == reminderFrequency;
  }

  @override
  int get hashCode {
    return fontSize.hashCode ^
      themeMode.hashCode ^
      preferredReciter.hashCode ^
      language.hashCode ^
      autoPlay.hashCode ^
      showTranslation.hashCode ^
      enableVibration.hashCode ^
      enableSoundEffects.hashCode ^
      playbackSpeed.hashCode ^
      reminderFrequency.hashCode;
  }
}

/// إعدادات القراءة المتقدمة
class ReadingPreferences {
  final bool highlightCurrentAyah;
  final bool showAyahNumbers;
  final bool enableWordByWord;
  final String translationLanguage;
  final bool showTafsir;
  final String preferredTafsir;
  final bool enableBookmarks;
  final bool autoScroll;
  final double scrollSpeed;

  const ReadingPreferences({
    this.highlightCurrentAyah = true,
    this.showAyahNumbers = true,
    this.enableWordByWord = false,
    this.translationLanguage = 'ar',
    this.showTafsir = false,
    this.preferredTafsir = '',
    this.enableBookmarks = true,
    this.autoScroll = false,
    this.scrollSpeed = 1.0,
  });

  ReadingPreferences copyWith({
    bool? highlightCurrentAyah,
    bool? showAyahNumbers,
    bool? enableWordByWord,
    String? translationLanguage,
    bool? showTafsir,
    String? preferredTafsir,
    bool? enableBookmarks,
    bool? autoScroll,
    double? scrollSpeed,
  }) {
    return ReadingPreferences(
      highlightCurrentAyah: highlightCurrentAyah ?? this.highlightCurrentAyah,
      showAyahNumbers: showAyahNumbers ?? this.showAyahNumbers,
      enableWordByWord: enableWordByWord ?? this.enableWordByWord,
      translationLanguage: translationLanguage ?? this.translationLanguage,
      showTafsir: showTafsir ?? this.showTafsir,
      preferredTafsir: preferredTafsir ?? this.preferredTafsir,
      enableBookmarks: enableBookmarks ?? this.enableBookmarks,
      autoScroll: autoScroll ?? this.autoScroll,
      scrollSpeed: scrollSpeed ?? this.scrollSpeed,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'highlightCurrentAyah': highlightCurrentAyah,
      'showAyahNumbers': showAyahNumbers,
      'enableWordByWord': enableWordByWord,
      'translationLanguage': translationLanguage,
      'showTafsir': showTafsir,
      'preferredTafsir': preferredTafsir,
      'enableBookmarks': enableBookmarks,
      'autoScroll': autoScroll,
      'scrollSpeed': scrollSpeed,
    };
  }

  factory ReadingPreferences.fromMap(Map<String, dynamic> map) {
    return ReadingPreferences(
      highlightCurrentAyah: map['highlightCurrentAyah'] ?? true,
      showAyahNumbers: map['showAyahNumbers'] ?? true,
      enableWordByWord: map['enableWordByWord'] ?? false,
      translationLanguage: map['translationLanguage'] ?? 'ar',
      showTafsir: map['showTafsir'] ?? false,
      preferredTafsir: map['preferredTafsir'] ?? '',
      enableBookmarks: map['enableBookmarks'] ?? true,
      autoScroll: map['autoScroll'] ?? false,
      scrollSpeed: map['scrollSpeed']?.toDouble() ?? 1.0,
    );
  }
}
