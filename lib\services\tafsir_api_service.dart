import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';

/// خدمة التفسير مع API حقيقي
class TafsirApiService {
  static const String _baseUrl = 'https://api.quran.com/v4';
  
  // معرفات التفاسير المتاحة
  static const Map<String, int> _tafsirIds = {
    'tafsir_muyassar': 131, // التفسير الميسر
    'ibn_kathir': 169, // تفسير ابن كثير
    'jalalayn': 164, // تفسير الجلالين
    'qurtubi': 156, // تفسير القرطبي
    'tabari': 168, // تفسير الطبري
    'saadi': 206, // تفسير السعدي
  };

  /// الحصول على قائمة التفاسير المتاحة
  static Future<List<Map<String, dynamic>>> getAvailableTafsirs() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/resources/tafsirs'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['tafsirs'] ?? []);
      } else {
        return _getLocalTafsirList();
      }
    } catch (e) {
      debugPrint('خطأ في جلب قائمة التفاسير: $e');
      return _getLocalTafsirList();
    }
  }

  /// الحصول على تفسير آية محددة
  static Future<TafsirVerse?> getTafsirForVerse({
    required int surahNumber,
    required int ayahNumber,
    required String tafsirName,
  }) async {
    try {
      final tafsirId = _tafsirIds[tafsirName] ?? 131; // التفسير الميسر كافتراضي
      final verseKey = '$surahNumber:$ayahNumber';
      
      final response = await http.get(
        Uri.parse('$_baseUrl/quran/tafsirs/$tafsirId?verse_key=$verseKey'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tafsirs = data['tafsirs'] as List?;
        
        if (tafsirs != null && tafsirs.isNotEmpty) {
          final tafsirData = tafsirs.first;
          return TafsirVerse(
            id: '${tafsirName}_${verseKey}',
            surahNumber: surahNumber,
            ayahNumber: ayahNumber,
            verseKey: verseKey,
            arabicText: tafsirData['verse_text'] ?? '',
            tafsirText: tafsirData['text'] ?? '',
            tafsirTextArabic: tafsirData['text'] ?? '',
            tafsirName: tafsirName,
            tafsirNameArabic: _getTafsirArabicName(tafsirName),
            authorName: tafsirData['resource_name'] ?? '',
            authorNameArabic: _getAuthorArabicName(tafsirName),
            type: _getTafsirType(tafsirName),
            keywords: _extractKeywords(tafsirData['text'] ?? ''),
          );
        }
      }
      
      return _getLocalTafsir(surahNumber, ayahNumber, tafsirName);
    } catch (e) {
      debugPrint('خطأ في جلب التفسير: $e');
      return _getLocalTafsir(surahNumber, ayahNumber, tafsirName);
    }
  }

  /// الحصول على تفسير سورة كاملة
  static Future<List<TafsirVerse>> getTafsirForSurah({
    required int surahNumber,
    required String tafsirName,
  }) async {
    try {
      final tafsirId = _tafsirIds[tafsirName] ?? 131;
      
      final response = await http.get(
        Uri.parse('$_baseUrl/quran/tafsirs/$tafsirId?chapter_number=$surahNumber'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tafsirs = data['tafsirs'] as List? ?? [];
        
        return tafsirs.map((tafsirData) {
          final verseKey = tafsirData['verse_key'] ?? '';
          final parts = verseKey.split(':');
          final ayahNumber = parts.length > 1 ? int.tryParse(parts[1]) ?? 1 : 1;
          
          return TafsirVerse(
            id: '${tafsirName}_${verseKey}',
            surahNumber: surahNumber,
            ayahNumber: ayahNumber,
            verseKey: verseKey,
            arabicText: tafsirData['verse_text'] ?? '',
            tafsirText: tafsirData['text'] ?? '',
            tafsirTextArabic: tafsirData['text'] ?? '',
            tafsirName: tafsirName,
            tafsirNameArabic: _getTafsirArabicName(tafsirName),
            authorName: tafsirData['resource_name'] ?? '',
            authorNameArabic: _getAuthorArabicName(tafsirName),
            type: _getTafsirType(tafsirName),
            keywords: _extractKeywords(tafsirData['text'] ?? ''),
          );
        }).toList();
      }
      
      return _getLocalSurahTafsir(surahNumber, tafsirName);
    } catch (e) {
      debugPrint('خطأ في جلب تفسير السورة: $e');
      return _getLocalSurahTafsir(surahNumber, tafsirName);
    }
  }

  /// البحث في التفاسير
  static Future<List<TafsirVerse>> searchTafsir({
    required String query,
    String tafsirName = 'tafsir_muyassar',
  }) async {
    try {
      // البحث في البيانات المحلية (يمكن تطوير API للبحث لاحقاً)
      return _searchLocalTafsir(query, tafsirName);
    } catch (e) {
      debugPrint('خطأ في البحث في التفسير: $e');
      return [];
    }
  }

  /// الحصول على الاسم العربي للتفسير
  static String _getTafsirArabicName(String tafsirName) {
    switch (tafsirName) {
      case 'tafsir_muyassar':
        return 'التفسير الميسر';
      case 'ibn_kathir':
        return 'تفسير ابن كثير';
      case 'jalalayn':
        return 'تفسير الجلالين';
      case 'qurtubi':
        return 'تفسير القرطبي';
      case 'tabari':
        return 'تفسير الطبري';
      case 'saadi':
        return 'تفسير السعدي';
      default:
        return 'التفسير الميسر';
    }
  }

  /// الحصول على اسم المؤلف بالعربية
  static String _getAuthorArabicName(String tafsirName) {
    switch (tafsirName) {
      case 'tafsir_muyassar':
        return 'مجمع الملك فهد';
      case 'ibn_kathir':
        return 'ابن كثير';
      case 'jalalayn':
        return 'الجلالين';
      case 'qurtubi':
        return 'القرطبي';
      case 'tabari':
        return 'الطبري';
      case 'saadi':
        return 'السعدي';
      default:
        return 'مجمع الملك فهد';
    }
  }

  /// تحديد نوع التفسير
  static TafsirType _getTafsirType(String tafsirName) {
    switch (tafsirName) {
      case 'tafsir_muyassar':
        return TafsirType.modern;
      case 'ibn_kathir':
        return TafsirType.classical;
      case 'jalalayn':
        return TafsirType.classical;
      case 'qurtubi':
        return TafsirType.jurisprudential;
      case 'tabari':
        return TafsirType.classical;
      case 'saadi':
        return TafsirType.modern;
      default:
        return TafsirType.modern;
    }
  }

  /// استخراج الكلمات المفتاحية من النص
  static List<String> _extractKeywords(String text) {
    // تنفيذ بسيط لاستخراج الكلمات المفتاحية
    final words = text.split(' ');
    final keywords = <String>[];
    
    for (final word in words) {
      if (word.length > 3 && !keywords.contains(word)) {
        keywords.add(word);
        if (keywords.length >= 5) break; // حد أقصى 5 كلمات
      }
    }
    
    return keywords;
  }

  /// قائمة التفاسير المحلية
  static List<Map<String, dynamic>> _getLocalTafsirList() {
    return [
      {
        'id': 131,
        'name': 'tafsir_muyassar',
        'author_name': 'King Fahd Complex',
        'name_arabic': 'التفسير الميسر',
        'author_name_arabic': 'مجمع الملك فهد',
      },
      {
        'id': 169,
        'name': 'ibn_kathir',
        'author_name': 'Ibn Kathir',
        'name_arabic': 'تفسير ابن كثير',
        'author_name_arabic': 'ابن كثير',
      },
      {
        'id': 164,
        'name': 'jalalayn',
        'author_name': 'Al-Jalalayn',
        'name_arabic': 'تفسير الجلالين',
        'author_name_arabic': 'الجلالين',
      },
    ];
  }

  /// تفسير محلي للاختبار
  static TafsirVerse? _getLocalTafsir(int surahNumber, int ayahNumber, String tafsirName) {
    if (surahNumber == 1 && ayahNumber == 1) {
      return TafsirVerse(
        id: '${tafsirName}_1:1',
        surahNumber: 1,
        ayahNumber: 1,
        verseKey: '1:1',
        arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        tafsirText: 'أبتدئ قراءتي باسم الله مستعيناً به، والله هو المعبود بحق دون سواه، وهو الرحمن الذي وسعت رحمته جميع الخلق، الرحيم بالمؤمنين.',
        tafsirTextArabic: 'أبتدئ قراءتي باسم الله مستعيناً به، والله هو المعبود بحق دون سواه، وهو الرحمن الذي وسعت رحمته جميع الخلق، الرحيم بالمؤمنين.',
        tafsirName: tafsirName,
        tafsirNameArabic: _getTafsirArabicName(tafsirName),
        authorName: 'Local',
        authorNameArabic: _getAuthorArabicName(tafsirName),
        type: _getTafsirType(tafsirName),
        keywords: ['بسملة', 'الله', 'رحمة'],
      );
    }
    return null;
  }

  /// تفسير سورة محلي
  static List<TafsirVerse> _getLocalSurahTafsir(int surahNumber, String tafsirName) {
    final result = <TafsirVerse>[];
    final localTafsir = _getLocalTafsir(surahNumber, 1, tafsirName);
    if (localTafsir != null) {
      result.add(localTafsir);
    }
    return result;
  }

  /// البحث المحلي في التفسير
  static List<TafsirVerse> _searchLocalTafsir(String query, String tafsirName) {
    final results = <TafsirVerse>[];
    
    // بحث بسيط في البيانات المحلية
    if (query.contains('بسم') || query.contains('الله')) {
      final localTafsir = _getLocalTafsir(1, 1, tafsirName);
      if (localTafsir != null) {
        results.add(localTafsir);
      }
    }
    
    return results;
  }
}
