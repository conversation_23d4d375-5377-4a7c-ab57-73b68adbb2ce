import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/tafsir_provider.dart';
import '../models/tafsir.dart';
import '../constants/app_constants.dart';

class TafsirSettingsScreen extends StatefulWidget {
  const TafsirSettingsScreen({super.key});

  @override
  State<TafsirSettingsScreen> createState() => _TafsirSettingsScreenState();
}

class _TafsirSettingsScreenState extends State<TafsirSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TafsirProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التفسير والترجمة'),
        elevation: 0,
      ),
      body: Consumer<TafsirProvider>(
        builder: (context, tafsirProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tafsir Section
                _buildTafsirSection(context, tafsirProvider, theme),
                const SizedBox(height: AppConstants.paddingLarge),

                // Translation Section
                _buildTranslationSection(context, tafsirProvider, theme),
                const SizedBox(height: AppConstants.paddingLarge),

                // Cache Section
                _buildCacheSection(context, tafsirProvider, theme),
                const SizedBox(height: AppConstants.paddingLarge),

                // Info Section
                _buildInfoSection(context, tafsirProvider, theme),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTafsirSection(BuildContext context, TafsirProvider provider, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.book, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'التفسير',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: provider.isTafsirEnabled,
                  onChanged: (_) => provider.toggleTafsirEnabled(),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            if (provider.isTafsirEnabled) ...[
              Text(
                'اختر التفسير المفضل:',
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              
              ...provider.availableTafsirs.map((tafsir) {
                final isSelected = provider.selectedTafsir?.id == tafsir.id;
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  color: isSelected ? theme.primaryColor.withOpacity(0.1) : null,
                  child: ListTile(
                    leading: Icon(
                      isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: isSelected ? theme.primaryColor : null,
                    ),
                    title: Text(
                      tafsir.name,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text(tafsir.authorName),
                    trailing: tafsir.isPopular
                        ? Chip(
                            label: const Text('مشهور'),
                            backgroundColor: Colors.orange.withOpacity(0.2),
                            labelStyle: const TextStyle(fontSize: 12),
                          )
                        : null,
                    onTap: () => provider.setSelectedTafsir(tafsir),
                  ),
                );
              }).toList(),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: theme.dividerColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: theme.hintColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'قم بتفعيل التفسير لعرض شرح الآيات',
                        style: TextStyle(color: theme.hintColor),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationSection(BuildContext context, TafsirProvider provider, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.translate, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'الترجمة',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: provider.isTranslationEnabled,
                  onChanged: (_) => provider.toggleTranslationEnabled(),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            if (provider.isTranslationEnabled) ...[
              Text(
                'اختر الترجمة المفضلة:',
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              
              ...provider.availableTranslations.map((translation) {
                final isSelected = provider.selectedTranslation?.id == translation.id;
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  color: isSelected ? theme.primaryColor.withOpacity(0.1) : null,
                  child: ListTile(
                    leading: Icon(
                      isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: isSelected ? theme.primaryColor : null,
                    ),
                    title: Text(
                      translation.name,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    subtitle: Text('${translation.language} - ${translation.authorName}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (translation.isPopular)
                          Chip(
                            label: const Text('مشهور'),
                            backgroundColor: Colors.green.withOpacity(0.2),
                            labelStyle: const TextStyle(fontSize: 12),
                          ),
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            translation.languageCode.toUpperCase(),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: theme.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    onTap: () => provider.setSelectedTranslation(translation),
                  ),
                );
              }).toList(),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: theme.dividerColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: theme.hintColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'قم بتفعيل الترجمة لعرض معاني الآيات بلغات مختلفة',
                        style: TextStyle(color: theme.hintColor),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCacheSection(BuildContext context, TafsirProvider provider, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'إدارة التخزين المؤقت',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            Text(
              provider.getCacheInfo(),
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            ElevatedButton.icon(
              onPressed: () {
                provider.clearCache();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم مسح التخزين المؤقت')),
                );
              },
              icon: const Icon(Icons.clear_all),
              label: const Text('مسح التخزين المؤقت'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context, TafsirProvider provider, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'معلومات',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            Text(
              provider.getContentSummary(),
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            
            Text(
              'ملاحظة: يتم تحميل التفسير والترجمة من الإنترنت ويتم حفظها مؤقتاً لتسريع الوصول.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
