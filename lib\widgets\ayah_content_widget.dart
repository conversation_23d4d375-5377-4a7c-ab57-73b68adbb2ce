import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/surah.dart';
import '../providers/tafsir_provider.dart';
import '../providers/theme_provider.dart';
import '../constants/app_constants.dart';

class AyahContentWidget extends StatefulWidget {
  final Ayah ayah;
  final bool showTafsir;
  final bool showTranslation;

  const AyahContentWidget({
    super.key,
    required this.ayah,
    this.showTafsir = true,
    this.showTranslation = true,
  });

  @override
  State<AyahContentWidget> createState() => _AyahContentWidgetState();
}

class _AyahContentWidgetState extends State<AyahContentWidget> {
  bool _isExpanded = false;
  bool _hasLoadedContent = false;

  @override
  void initState() {
    super.initState();
    _loadContentIfNeeded();
  }

  void _loadContentIfNeeded() {
    final tafsirProvider = context.read<TafsirProvider>();
    if (!_hasLoadedContent && 
        (tafsirProvider.isTafsirEnabled || tafsirProvider.isTranslationEnabled)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        tafsirProvider.loadContentForAyah(widget.ayah.surahNumber, widget.ayah.numberInSurah);
        _hasLoadedContent = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();

    return Consumer<TafsirProvider>(
      builder: (context, tafsirProvider, child) {
        final hasAdditionalContent = tafsirProvider.isTafsirEnabled || 
                                   tafsirProvider.isTranslationEnabled;

        return Card(
          margin: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Main Ayah Text
                _buildAyahText(theme, themeProvider),
                
                if (hasAdditionalContent) ...[
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildExpandButton(theme, tafsirProvider),
                  
                  if (_isExpanded) ...[
                    const SizedBox(height: AppConstants.paddingMedium),
                    _buildAdditionalContent(theme, tafsirProvider),
                  ],
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAyahText(ThemeData theme, ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Ayah number badge
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    widget.ayah.numberInSurah.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const Spacer(),
              if (widget.ayah.sajda) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Colors.orange.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'سجدة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Arabic text
          Text(
            widget.ayah.text,
            style: themeProvider.getQuranTextStyle(context),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget _buildExpandButton(ThemeData theme, TafsirProvider tafsirProvider) {
    final hasContent = (tafsirProvider.isTafsirEnabled && tafsirProvider.currentTafsirText != null) ||
                     (tafsirProvider.isTranslationEnabled && tafsirProvider.currentTranslationText != null);
    
    final isLoading = tafsirProvider.isLoadingTafsir || tafsirProvider.isLoadingTranslation;

    return InkWell(
      onTap: hasContent || isLoading ? () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
        if (!_hasLoadedContent) {
          _loadContentIfNeeded();
        }
      } : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: theme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.primaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
              color: theme.primaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isLoading 
                    ? 'جاري التحميل...'
                    : hasContent 
                        ? _isExpanded ? 'إخفاء التفسير والترجمة' : 'عرض التفسير والترجمة'
                        : 'لا توجد محتويات إضافية',
                style: TextStyle(
                  color: theme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (isLoading) ...[
              const SizedBox(width: 8),
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalContent(ThemeData theme, TafsirProvider tafsirProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Translation
        if (tafsirProvider.isTranslationEnabled) ...[
          _buildTranslationSection(theme, tafsirProvider),
          const SizedBox(height: AppConstants.paddingMedium),
        ],
        
        // Tafsir
        if (tafsirProvider.isTafsirEnabled) ...[
          _buildTafsirSection(theme, tafsirProvider),
        ],
      ],
    );
  }

  Widget _buildTranslationSection(ThemeData theme, TafsirProvider tafsirProvider) {
    final translation = tafsirProvider.currentTranslationText;
    final error = tafsirProvider.translationError;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Icon(Icons.translate, color: Colors.blue.shade600, size: 20),
              const SizedBox(width: 8),
              Text(
                'الترجمة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade600,
                ),
              ),
              if (translation != null) ...[
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    translation.languageCode.toUpperCase(),
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          if (error != null) ...[
            Text(
              error,
              style: TextStyle(
                color: Colors.red.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ] else if (translation != null) ...[
            Text(
              translation.text,
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.6,
              ),
              textDirection: translation.languageCode == 'ar' 
                  ? TextDirection.rtl 
                  : TextDirection.ltr,
            ),
          ] else ...[
            Text(
              'لا توجد ترجمة متاحة',
              style: TextStyle(
                color: theme.hintColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTafsirSection(ThemeData theme, TafsirProvider tafsirProvider) {
    final tafsir = tafsirProvider.currentTafsirText;
    final error = tafsirProvider.tafsirError;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.green.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Icon(Icons.book, color: Colors.green.shade600, size: 20),
              const SizedBox(width: 8),
              Text(
                'التفسير',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade600,
                ),
              ),
              if (tafsir != null) ...[
                const Spacer(),
                Text(
                  tafsir.tafsirName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          if (error != null) ...[
            Text(
              error,
              style: TextStyle(
                color: Colors.red.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ] else if (tafsir != null) ...[
            Text(
              tafsir.text,
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.8,
              ),
              textDirection: TextDirection.rtl,
            ),
          ] else ...[
            Text(
              'لا يوجد تفسير متاح',
              style: TextStyle(
                color: theme.hintColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
