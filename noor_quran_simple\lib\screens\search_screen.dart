import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../models/quran_models.dart';
import '../models/search_result.dart';
import 'search_history_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في القرآن'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SearchHistoryScreen(),
                ),
              );
            },
            tooltip: 'سجل البحث',
          ),
        ],
      ),
      body: Consumer<QuranProvider>(
        builder: (context, quranProvider, child) {
          return Column(
            children: [
              _buildSearchBar(quranProvider),
              Expanded(
                child: quranProvider.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : quranProvider.searchResults.isEmpty
                    ? _buildEmptyState()
                    : _buildSearchResults(quranProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(QuranProvider quranProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'ابحث في القرآن الكريم...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    quranProvider.clearSearch();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              AppConstants.borderRadiusMedium,
            ),
          ),
        ),
        onChanged: (value) {
          if (value.isNotEmpty) {
            quranProvider.searchQuran(value);
          } else {
            quranProvider.clearSearch();
          }
        },
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            _saveSearchQuery(value);
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 80, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'ابحث في القرآن الكريم',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'اكتب كلمة أو جملة للبحث عنها',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(QuranProvider quranProvider) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: quranProvider.searchResults.length,
      itemBuilder: (context, index) {
        final result = quranProvider.searchResults[index];
        return _buildResultCard(result);
      },
    );
  }

  Widget _buildResultCard(SearchResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'سورة ${result.surahName}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'آية ${result.ayahNumber}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              result.ayahText,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                height: 1.8,
              ),
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () {
                    // Navigate to ayah
                  },
                  icon: const Icon(Icons.visibility),
                  label: const Text('عرض'),
                ),
                TextButton.icon(
                  onPressed: () {
                    // Play ayah audio
                  },
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('استماع'),
                ),
                TextButton.icon(
                  onPressed: () {
                    // Add to bookmarks
                  },
                  icon: const Icon(Icons.bookmark_add),
                  label: const Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _saveSearchQuery(String query) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> history = prefs.getStringList('search_history') ?? [];

      // Remove if already exists to avoid duplicates
      history.remove(query);

      // Add to beginning
      history.insert(0, query);

      // Keep only last 50 searches
      if (history.length > 50) {
        history = history.take(50).toList();
      }

      await prefs.setStringList('search_history', history);
    } catch (e) {
      // Handle error silently
    }
  }
}
