import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/quran_models.dart';
import '../providers/audio_provider.dart';
import '../providers/quran_provider.dart';
import 'surah_reading_screen.dart';

class SurahListScreen extends StatelessWidget {
  const SurahListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة السور'),
        actions: [
          IconButton(
            icon: const Icon(Icons.grid_view),
            onPressed: () {
              // Toggle between list and grid view
            },
          ),
        ],
      ),
      body: Consumer<QuranProvider>(
        builder: (context, quranProvider, child) {
          if (quranProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (quranProvider.surahs.isEmpty) {
            return const Center(child: Text('لا توجد سور متاحة'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: quranProvider.surahs.length,
            itemBuilder: (context, index) {
              final surah = quranProvider.surahs[index];
              return _buildSurahCard(context, surah);
            },
          );
        },
      ),
    );
  }

  Widget _buildSurahCard(BuildContext context, Surah surah) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              '${surah.number}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ),
        ),
        title: Text(
          surah.name,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              surah.englishName,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: surah.revelationType == 'Meccan'
                        ? AppConstants.accentColor.withValues(alpha: 0.2)
                        : AppConstants.secondaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: surah.revelationType == 'Meccan'
                          ? AppConstants.accentColor
                          : AppConstants.secondaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${surah.numberOfAyahs} آية',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                return IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () {
                    final audioUrl =
                        'https://server8.mp3quran.net/afs/${surah.number.toString().padLeft(3, '0')}.mp3';
                    audioProvider.playAudio(audioUrl);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تشغيل سورة ${surah.name}'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                );
              },
            ),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahReadingScreen(
                surahNumber: surah.number,
                surahName: surah.name,
              ),
            ),
          );
        },
      ),
    );
  }
}
