import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/library_models.dart';

/// خدمة الكتب الفقهية
class FiqhBooksService {
  static List<Book>? _cachedBooks;

  /// جلب جميع الكتب الفقهية
  static Future<List<Book>> getAllFiqhBooks() async {
    if (_cachedBooks != null) {
      return _cachedBooks!;
    }

    try {
      debugPrint('📚 جلب الكتب الفقهية...');

      // محاولة جلب من ملف محلي أولاً
      final books = await _loadLocalBooks();
      _cachedBooks = books;

      return books;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الكتب الفقهية: $e');
      return _getDefaultBooks();
    }
  }

  /// جلب كتاب محدد
  static Future<Book?> getBookById(String bookId) async {
    final books = await getAllFiqhBooks();
    try {
      return books.firstWhere((book) => book.id == bookId);
    } catch (e) {
      debugPrint('❌ لم يتم العثور على الكتاب: $bookId');
      return null;
    }
  }

  /// البحث في الكتب
  static Future<List<SearchResult>> searchInBooks(String query) async {
    try {
      debugPrint('🔍 البحث في الكتب: $query');

      final books = await getAllFiqhBooks();
      final results = <SearchResult>[];

      for (final book in books) {
        for (final chapter in book.chapters) {
          for (final section in chapter.sections) {
            if (section.content.contains(query) ||
                section.title.contains(query) ||
                (section.keywords?.any((keyword) => keyword.contains(query)) ??
                    false)) {
              results.add(
                SearchResult(
                  id: '${book.id}_${chapter.id}_${section.id}',
                  title: section.title,
                  content: _truncateText(section.content, 200),
                  source: '${book.title} - ${chapter.title}',
                  type: SearchResultType.fiqh,
                  metadata: {
                    'bookId': book.id,
                    'chapterId': chapter.id,
                    'sectionId': section.id,
                    'bookTitle': book.title,
                    'chapterTitle': chapter.title,
                  },
                ),
              );
            }
          }
        }
      }

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في البحث: $e');
      return [];
    }
  }

  /// جلب فصل محدد
  static Future<Chapter?> getChapter(String bookId, String chapterId) async {
    final book = await getBookById(bookId);
    if (book == null) return null;

    try {
      return book.chapters.firstWhere((chapter) => chapter.id == chapterId);
    } catch (e) {
      debugPrint('❌ لم يتم العثور على الفصل: $chapterId');
      return null;
    }
  }

  /// جلب قسم محدد
  static Future<Section?> getSection(
    String bookId,
    String chapterId,
    String sectionId,
  ) async {
    final chapter = await getChapter(bookId, chapterId);
    if (chapter == null) return null;

    try {
      return chapter.sections.firstWhere((section) => section.id == sectionId);
    } catch (e) {
      debugPrint('❌ لم يتم العثور على القسم: $sectionId');
      return null;
    }
  }

  /// تحميل الكتب من ملف محلي
  static Future<List<Book>> _loadLocalBooks() async {
    try {
      List<Book> allBooks = [];

      // تحميل الكتب الفقهية
      try {
        final fiqhJsonString = await rootBundle.loadString(
          'assets/data/fiqh_books.json',
        );
        final fiqhData = json.decode(fiqhJsonString) as Map<String, dynamic>;
        final fiqhBooksList = fiqhData['books'] as List<dynamic>;

        allBooks.addAll(
          fiqhBooksList
              .map(
                (bookJson) => Book.fromJson(bookJson as Map<String, dynamic>),
              )
              .toList(),
        );
      } catch (e) {
        debugPrint('⚠️ خطأ في تحميل الكتب الفقهية: $e');
      }

      // تحميل كتب التفسير
      try {
        final tafsirJsonString = await rootBundle.loadString(
          'assets/data/tafsir_books.json',
        );
        final tafsirData =
            json.decode(tafsirJsonString) as Map<String, dynamic>;
        final tafsirBooksList = tafsirData['books'] as List<dynamic>;

        allBooks.addAll(
          tafsirBooksList
              .map(
                (bookJson) => Book.fromJson(bookJson as Map<String, dynamic>),
              )
              .toList(),
        );
      } catch (e) {
        debugPrint('⚠️ خطأ في تحميل كتب التفسير: $e');
      }

      // تحميل الكتب الدينية الأخرى
      try {
        final religiousJsonString = await rootBundle.loadString(
          'assets/data/religious_books.json',
        );
        final religiousData =
            json.decode(religiousJsonString) as Map<String, dynamic>;
        final religiousBooksList = religiousData['books'] as List<dynamic>;

        allBooks.addAll(
          religiousBooksList
              .map(
                (bookJson) => Book.fromJson(bookJson as Map<String, dynamic>),
              )
              .toList(),
        );
      } catch (e) {
        debugPrint('⚠️ خطأ في تحميل الكتب الدينية: $e');
      }

      // تحميل الكتب الشعرية
      try {
        final poetryJsonString = await rootBundle.loadString(
          'assets/data/poetry_books.json',
        );
        final poetryData =
            json.decode(poetryJsonString) as Map<String, dynamic>;
        final poetryBooksList = poetryData['books'] as List<dynamic>;

        allBooks.addAll(
          poetryBooksList
              .map(
                (bookJson) => Book.fromJson(bookJson as Map<String, dynamic>),
              )
              .toList(),
        );
      } catch (e) {
        debugPrint('⚠️ خطأ في تحميل الكتب الشعرية: $e');
      }

      // تحميل كتب الحديث
      try {
        final hadithBooksJsonString = await rootBundle.loadString(
          'assets/data/hadith_books.json',
        );
        final hadithBooksData =
            json.decode(hadithBooksJsonString) as Map<String, dynamic>;
        final hadithBooksList = hadithBooksData['books'] as List<dynamic>;

        allBooks.addAll(
          hadithBooksList
              .map(
                (bookJson) => Book.fromJson(bookJson as Map<String, dynamic>),
              )
              .toList(),
        );
      } catch (e) {
        debugPrint('⚠️ خطأ في تحميل كتب الحديث: $e');
      }

      if (allBooks.isEmpty) {
        debugPrint('⚠️ لم يتم تحميل أي كتب، استخدام البيانات الافتراضية');
        return _getDefaultBooks();
      }

      debugPrint('✅ تم تحميل ${allBooks.length} كتاب بنجاح');
      return allBooks;
    } catch (e) {
      debugPrint('⚠️ خطأ عام في تحميل الكتب: $e');
      return _getDefaultBooks();
    }
  }

  /// الكتب الافتراضية
  static List<Book> _getDefaultBooks() {
    return [
      Book(
        id: 'akhdariyya',
        title: 'الأخضرية',
        author: 'عبد الرحمن الأخضري',
        description: 'متن مختصر في الفقه المالكي، يتناول أهم أبواب العبادات',
        type: BookType.fiqh,
        chapters: [
          Chapter(
            id: 'tahara',
            title: 'كتاب الطهارة',
            order: 1,
            sections: [
              const Section(
                id: 'wudu',
                title: 'باب الوضوء',
                content: '''فصل في الوضوء:
والوضوء له فرائض وسنن ومستحبات.

فرائض الوضوء ستة:
1. النية
2. غسل الوجه
3. غسل اليدين إلى المرفقين
4. مسح الرأس
5. غسل الرجلين إلى الكعبين
6. الترتيب

وسننه:
- التسمية
- غسل الكفين ثلاثاً
- المضمضة والاستنشاق
- تخليل اللحية والأصابع
- التيامن في غسل الأعضاء''',
                order: 1,
                keywords: ['الوضوء', 'الطهارة', 'الفرائض', 'السنن'],
                commentary: 'هذا الباب يتناول أحكام الوضوء وشروطه وآدابه',
              ),
              const Section(
                id: 'ghusl',
                title: 'باب الغسل',
                content: '''فصل في الغسل:
الغسل واجب من:
1. الجنابة
2. الحيض
3. النفاس
4. الموت (غسل الميت)

وفرائض الغسل ثلاثة:
1. النية
2. إزالة النجاسة إن وجدت
3. تعميم البدن بالماء

وسننه:
- الوضوء قبل الغسل
- البدء بغسل الرأس
- تخليل الشعر
- الدلك''',
                order: 2,
                keywords: ['الغسل', 'الجنابة', 'الحيض', 'النفاس'],
                commentary: 'باب يتناول أحكام الغسل وموجباته وكيفيته',
              ),
            ],
            summary: 'يتناول هذا الكتاب أحكام الطهارة من وضوء وغسل وتيمم',
          ),
          Chapter(
            id: 'salah',
            title: 'كتاب الصلاة',
            order: 2,
            sections: [
              const Section(
                id: 'conditions',
                title: 'شروط الصلاة',
                content: '''شروط الصلاة:
1. الطهارة من الحدث
2. الطهارة من النجس
3. ستر العورة
4. استقبال القبلة
5. دخول الوقت
6. العقل
7. التمييز
8. الإسلام

وهذه الشروط لا تصح الصلاة إلا بها، فمن فقد شرطاً منها لم تصح صلاته.''',
                order: 1,
                keywords: ['شروط الصلاة', 'الطهارة', 'القبلة', 'الوقت'],
                commentary: 'الشروط التي يجب توفرها قبل الدخول في الصلاة',
              ),
              const Section(
                id: 'pillars',
                title: 'أركان الصلاة',
                content: '''أركان الصلاة أربعة عشر ركناً:
1. النية
2. تكبيرة الإحرام
3. القيام في الفرض للقادر
4. قراءة الفاتحة
5. الركوع
6. الرفع من الركوع
7. السجود على الأعضاء السبعة
8. الرفع من السجود
9. الجلوس بين السجدتين
10. السجود الثاني
11. الجلوس للتشهد الأخير
12. التشهد الأخير
13. الصلاة على النبي في التشهد الأخير
14. التسليم

ومن ترك ركناً عمداً أو سهواً بطلت صلاته.''',
                order: 2,
                keywords: [
                  'أركان الصلاة',
                  'النية',
                  'الركوع',
                  'السجود',
                  'التشهد',
                ],
                commentary: 'الأركان التي تتكون منها الصلاة ولا تصح بدونها',
              ),
            ],
            summary: 'يتناول أحكام الصلاة وشروطها وأركانها وسننها',
          ),
        ],
      ),
      Book(
        id: 'ibn_ashir',
        title: 'متن ابن عاشر',
        author: 'عبد الواحد ابن عاشر',
        description: 'منظومة في العقيدة والفقه والتصوف على المذهب المالكي',
        type: BookType.fiqh,
        chapters: [
          Chapter(
            id: 'aqeedah',
            title: 'العقيدة',
            order: 1,
            sections: [
              const Section(
                id: 'tawheed',
                title: 'التوحيد',
                content: '''في التوحيد:
يجب على كل مكلف أن يعلم أن الله واحد في ذاته وصفاته وأفعاله.

وحدانية الذات: أنه تعالى ليس له شريك في الذات
وحدانية الصفات: أنه ليس له مثيل في الصفات  
وحدانية الأفعال: أنه ليس له شريك في الخلق والتدبير

والدليل على وجود الله تعالى: حدوث العالم، فكل حادث لا بد له من محدث.''',
                order: 1,
                keywords: ['التوحيد', 'العقيدة', 'الله', 'الوحدانية'],
                commentary: 'أساس العقيدة الإسلامية وهو توحيد الله تعالى',
              ),
            ],
            summary: 'يتناول أصول العقيدة الإسلامية',
          ),
        ],
      ),
    ];
  }

  /// اقتطاع النص
  static String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// إحصائيات الكتب
  static Future<Map<String, int>> getBooksStats() async {
    final books = await getAllFiqhBooks();

    int totalChapters = 0;
    int totalSections = 0;

    for (final book in books) {
      totalChapters += book.chapters.length;
      for (final chapter in book.chapters) {
        totalSections += chapter.sections.length;
      }
    }

    return {
      'books': books.length,
      'chapters': totalChapters,
      'sections': totalSections,
    };
  }
}
