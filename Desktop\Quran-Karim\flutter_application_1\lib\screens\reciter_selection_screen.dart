import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/audio_provider.dart';
import '../providers/theme_provider.dart';
import '../models/reciter.dart';

class ReciterSelectionScreen extends StatefulWidget {
  const ReciterSelectionScreen({super.key});

  @override
  State<ReciterSelectionScreen> createState() => _ReciterSelectionScreenState();
}

class _ReciterSelectionScreenState extends State<ReciterSelectionScreen> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final audioProvider = context.watch<AudioProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    final filteredReciters = _searchQuery.isEmpty
        ? audioProvider.availableReciters
        : audioProvider.availableReciters
            .where((reciter) =>
                reciter.name.contains(_searchQuery) ||
                reciter.englishName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                reciter.country.contains(_searchQuery))
            .toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار القارئ'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showReciterInfo(context),
            tooltip: 'معلومات القراء',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'البحث عن قارئ...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Current Selection
          if (audioProvider.selectedReciter != null)
            Container(
              margin: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
              ),
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: themeProvider.getThemeColor(context).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: themeProvider.getThemeColor(context).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: themeProvider.getThemeColor(context),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'القارئ المختار حالياً',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: themeProvider.getThemeColor(context),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          audioProvider.selectedReciter!.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Reciters List
          Expanded(
            child: filteredReciters.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: theme.textTheme.bodyMedium?.color?.withOpacity(0.3),
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        Text(
                          'لا توجد نتائج للبحث',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                    ),
                    itemCount: filteredReciters.length,
                    itemBuilder: (context, index) {
                      final reciter = filteredReciters[index];
                      return _buildReciterCard(reciter, audioProvider, theme, themeProvider);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildReciterCard(
    Reciter reciter,
    AudioProvider audioProvider,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    final isSelected = audioProvider.selectedReciter?.id == reciter.id;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: InkWell(
        onTap: () async {
          await audioProvider.selectReciter(reciter);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم اختيار ${reciter.name}'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Selection Indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? themeProvider.getThemeColor(context)
                        : theme.dividerColor,
                    width: 2,
                  ),
                  color: isSelected
                      ? themeProvider.getThemeColor(context)
                      : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              
              // Reciter Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reciter.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? themeProvider.getThemeColor(context) : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      reciter.englishName,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reciter.country,
                          style: theme.textTheme.bodySmall,
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.music_note,
                          size: 16,
                          color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reciter.styleDescription,
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                    if (reciter.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        reciter.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // Popular Badge
              if (reciter.isPopular)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مشهور',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReciterInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات القراء'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'أنواع القراءة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• مرتل: قراءة بطيئة ومتأنية'),
              Text('• مجود: قراءة بالتجويد والتحسين'),
              Text('• تعليمي: قراءة تعليمية للحفظ'),
              SizedBox(height: 16),
              Text(
                'ملاحظة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'يمكنك تغيير القارئ في أي وقت من الإعدادات أو من هذه الشاشة.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
