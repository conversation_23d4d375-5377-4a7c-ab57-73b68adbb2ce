enum QuizType { memorization, comprehension, tafsir, general }

enum QuestionType { multipleChoice, trueFalse, fillInTheBlank, matching }

enum DifficultyLevel { beginner, intermediate, advanced }

class QuizQuestion {
  final String id;
  final QuestionType type;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String? explanation;
  final int surahNumber;
  final int? ayahNumber;
  final DifficultyLevel difficulty;
  final List<String> tags;
  final int points;

  const QuizQuestion({
    required this.id,
    required this.type,
    required this.question,
    required this.options,
    required this.correctAnswer,
    this.explanation,
    required this.surahNumber,
    this.ayahNumber,
    required this.difficulty,
    this.tags = const [],
    this.points = 10,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      id: json['id'] ?? '',
      type: QuestionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => QuestionType.multipleChoice,
      ),
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctAnswer: json['correct_answer'] ?? '',
      explanation: json['explanation'],
      surahNumber: json['surah_number'] ?? 1,
      ayahNumber: json['ayah_number'],
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => DifficultyLevel.beginner,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      points: json['points'] ?? 10,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'question': question,
      'options': options,
      'correct_answer': correctAnswer,
      'explanation': explanation,
      'surah_number': surahNumber,
      'ayah_number': ayahNumber,
      'difficulty': difficulty.name,
      'tags': tags,
      'points': points,
    };
  }

  String get difficultyDisplayName {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'مبتدئ';
      case DifficultyLevel.intermediate:
        return 'متوسط';
      case DifficultyLevel.advanced:
        return 'متقدم';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case QuestionType.multipleChoice:
        return 'اختيار متعدد';
      case QuestionType.trueFalse:
        return 'صح أم خطأ';
      case QuestionType.fillInTheBlank:
        return 'املأ الفراغ';
      case QuestionType.matching:
        return 'مطابقة';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizQuestion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'QuizQuestion(id: $id, question: $question)';
}

class Quiz {
  final String id;
  final String title;
  final String description;
  final QuizType type;
  final DifficultyLevel difficulty;
  final List<QuizQuestion> questions;
  final int timeLimit; // in minutes
  final int passingScore; // percentage
  final DateTime createdAt;
  final String? imageUrl;
  final List<String> tags;

  const Quiz({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.questions,
    this.timeLimit = 30,
    this.passingScore = 70,
    required this.createdAt,
    this.imageUrl,
    this.tags = const [],
  });

  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: QuizType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => QuizType.general,
      ),
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => DifficultyLevel.beginner,
      ),
      questions:
          (json['questions'] as List<dynamic>?)
              ?.map((q) => QuizQuestion.fromJson(q))
              .toList() ??
          [],
      timeLimit: json['time_limit'] ?? 30,
      passingScore: json['passing_score'] ?? 70,
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      imageUrl: json['image_url'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'difficulty': difficulty.name,
      'questions': questions.map((q) => q.toJson()).toList(),
      'time_limit': timeLimit,
      'passing_score': passingScore,
      'created_at': createdAt.toIso8601String(),
      'image_url': imageUrl,
      'tags': tags,
    };
  }

  int get totalPoints => questions.fold(0, (sum, q) => sum + q.points);
  int get questionCount => questions.length;

  String get typeDisplayName {
    switch (type) {
      case QuizType.memorization:
        return 'حفظ';
      case QuizType.comprehension:
        return 'فهم';
      case QuizType.tafsir:
        return 'تفسير';
      case QuizType.general:
        return 'عام';
    }
  }

  String get difficultyDisplayName {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'مبتدئ';
      case DifficultyLevel.intermediate:
        return 'متوسط';
      case DifficultyLevel.advanced:
        return 'متقدم';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quiz && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() =>
      'Quiz(id: $id, title: $title, questions: ${questions.length})';
}

class QuizAttempt {
  final String id;
  final String quizId;
  final String userId;
  final Map<String, String> answers;
  final int score;
  final int totalPoints;
  final DateTime startedAt;
  final DateTime? completedAt;
  final Duration timeSpent;
  final bool isPassed;

  const QuizAttempt({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.answers,
    required this.score,
    required this.totalPoints,
    required this.startedAt,
    this.completedAt,
    required this.timeSpent,
    required this.isPassed,
  });

  factory QuizAttempt.fromJson(Map<String, dynamic> json) {
    return QuizAttempt(
      id: json['id'] ?? '',
      quizId: json['quiz_id'] ?? '',
      userId: json['user_id'] ?? '',
      answers: Map<String, String>.from(json['answers'] ?? {}),
      score: json['score'] ?? 0,
      totalPoints: json['total_points'] ?? 0,
      startedAt: DateTime.tryParse(json['started_at'] ?? '') ?? DateTime.now(),
      completedAt: json['completed_at'] != null
          ? DateTime.tryParse(json['completed_at'])
          : null,
      timeSpent: Duration(seconds: json['time_spent_seconds'] ?? 0),
      isPassed: json['is_passed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quiz_id': quizId,
      'user_id': userId,
      'answers': answers,
      'score': score,
      'total_points': totalPoints,
      'started_at': startedAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'time_spent_seconds': timeSpent.inSeconds,
      'is_passed': isPassed,
    };
  }

  double get percentage => totalPoints > 0 ? (score / totalPoints) * 100 : 0.0;
  bool get isCompleted => completedAt != null;

  String get scoreDisplayText =>
      '$score / $totalPoints (${percentage.toStringAsFixed(1)}%)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizAttempt && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'QuizAttempt(id: $id, score: $score/$totalPoints)';
}
