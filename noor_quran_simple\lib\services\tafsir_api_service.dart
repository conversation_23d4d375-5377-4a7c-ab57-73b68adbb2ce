import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/library_models.dart';

/// خدمة API للتفاسير
class TafsirApiService {
  static const String _baseUrl = 'https://api.quran.com/v4';
  
  // التفاسير المتاحة
  static const Map<String, String> availableTafsirs = {
    '131': 'التفسير الميسر',
    '169': 'تفسير ابن كثير',
    '164': 'تفسير الجلالين',
    '168': 'تفسير القرطبي',
    '171': 'تفسير الطبري',
    '167': 'تفسير البغوي',
    '165': 'تفسير السعدي',
  };

  /// جلب قائمة التفاسير المتاحة
  static Future<List<Tafsir>> getAvailableTafsirs() async {
    try {
      debugPrint('🔍 جلب قائمة التفاسير المتاحة...');
      
      final url = Uri.parse('$_baseUrl/resources/tafsirs');
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tafsirsList = data['tafsirs'] as List<dynamic>? ?? [];
        
        return tafsirsList
            .map((tafsirJson) => Tafsir.fromJson(tafsirJson as Map<String, dynamic>))
            .toList();
      } else {
        debugPrint('❌ خطأ في جلب التفاسير: ${response.statusCode}');
        return _getLocalTafsirs();
      }
    } catch (e) {
      debugPrint('❌ خطأ في الاتصال بـ API التفاسير: $e');
      return _getLocalTafsirs();
    }
  }

  /// جلب تفسير آية محددة
  static Future<VerseWithTafsir?> getVerseTafsir(
    int surahNumber,
    int verseNumber,
    String tafsirId,
  ) async {
    try {
      debugPrint('🔍 جلب تفسير الآية $surahNumber:$verseNumber...');
      
      final verseKey = '$surahNumber:$verseNumber';
      final url = Uri.parse('$_baseUrl/quran/tafsirs/$tafsirId')
          .replace(queryParameters: {'verse_key': verseKey});

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tafsirs = data['tafsirs'] as List<dynamic>? ?? [];
        
        if (tafsirs.isNotEmpty) {
          final tafsirData = tafsirs.first as Map<String, dynamic>;
          
          return VerseWithTafsir(
            surahNumber: surahNumber,
            verseNumber: verseNumber,
            verseText: tafsirData['verse_text'] as String? ?? '',
            tafsirText: tafsirData['text'] as String? ?? '',
            tafsirName: availableTafsirs[tafsirId] ?? 'تفسير غير معروف',
          );
        }
      } else {
        debugPrint('❌ خطأ في جلب التفسير: ${response.statusCode}');
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في جلب التفسير: $e');
      return null;
    }
  }

  /// جلب تفسير سورة كاملة
  static Future<List<VerseWithTafsir>> getSurahTafsir(
    int surahNumber,
    String tafsirId,
  ) async {
    try {
      debugPrint('🔍 جلب تفسير السورة $surahNumber...');
      
      final url = Uri.parse('$_baseUrl/quran/tafsirs/$tafsirId')
          .replace(queryParameters: {'chapter_number': surahNumber.toString()});

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tafsirs = data['tafsirs'] as List<dynamic>? ?? [];
        
        return tafsirs.map((tafsirData) {
          final tafsirMap = tafsirData as Map<String, dynamic>;
          final verseKey = tafsirMap['verse_key'] as String? ?? '1:1';
          final parts = verseKey.split(':');
          
          return VerseWithTafsir(
            surahNumber: int.tryParse(parts[0]) ?? 1,
            verseNumber: int.tryParse(parts[1]) ?? 1,
            verseText: tafsirMap['verse_text'] as String? ?? '',
            tafsirText: tafsirMap['text'] as String? ?? '',
            tafsirName: availableTafsirs[tafsirId] ?? 'تفسير غير معروف',
          );
        }).toList();
      } else {
        debugPrint('❌ خطأ في جلب تفسير السورة: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب تفسير السورة: $e');
      return [];
    }
  }

  /// البحث في التفاسير
  static Future<List<VerseWithTafsir>> searchInTafsir(
    String query,
    String tafsirId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      debugPrint('🔍 البحث في التفسير: $query');
      
      // نظراً لعدم وجود API بحث مباشر، سنجلب عدة سور ونبحث محلياً
      final results = <VerseWithTafsir>[];
      
      // البحث في أول 10 سور كمثال
      for (int surah = 1; surah <= 10; surah++) {
        final surahTafsir = await getSurahTafsir(surah, tafsirId);
        
        for (final verse in surahTafsir) {
          if (verse.tafsirText.contains(query) || 
              verse.verseText.contains(query)) {
            results.add(verse);
            
            if (results.length >= limit) {
              return results;
            }
          }
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في البحث في التفسير: $e');
      return [];
    }
  }

  /// جلب تفاسير متعددة لآية واحدة
  static Future<List<VerseWithTafsir>> getMultipleTafsirs(
    int surahNumber,
    int verseNumber,
    List<String> tafsirIds,
  ) async {
    try {
      debugPrint('🔍 جلب تفاسير متعددة للآية $surahNumber:$verseNumber...');
      
      final results = <VerseWithTafsir>[];
      
      for (final tafsirId in tafsirIds) {
        final tafsir = await getVerseTafsir(surahNumber, verseNumber, tafsirId);
        if (tafsir != null) {
          results.add(tafsir);
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في جلب التفاسير المتعددة: $e');
      return [];
    }
  }

  /// التفاسير المحلية (للاختبار)
  static List<Tafsir> _getLocalTafsirs() {
    return [
      const Tafsir(
        id: '131',
        name: 'التفسير الميسر',
        author: 'مجمع الملك فهد',
        language: 'العربية',
        description: 'تفسير مبسط وميسر للقرآن الكريم',
      ),
      const Tafsir(
        id: '169',
        name: 'تفسير ابن كثير',
        author: 'ابن كثير',
        language: 'العربية',
        description: 'تفسير القرآن العظيم',
      ),
      const Tafsir(
        id: '164',
        name: 'تفسير الجلالين',
        author: 'الجلال المحلي والسيوطي',
        language: 'العربية',
        description: 'تفسير مختصر ومفيد',
      ),
    ];
  }

  /// جلب تفسير عشوائي
  static Future<VerseWithTafsir?> getRandomVerseTafsir(String tafsirId) async {
    try {
      // اختيار سورة وآية عشوائية
      final randomSurah = (DateTime.now().millisecond % 114) + 1;
      final randomVerse = (DateTime.now().microsecond % 10) + 1;
      
      return await getVerseTafsir(randomSurah, randomVerse, tafsirId);
    } catch (e) {
      debugPrint('❌ خطأ في جلب التفسير العشوائي: $e');
      return null;
    }
  }

  /// اختبار الاتصال بـ API
  static Future<bool> testApiConnection() async {
    try {
      debugPrint('🔍 اختبار الاتصال بـ API التفاسير...');
      
      final url = Uri.parse('$_baseUrl/resources/tafsirs');
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        debugPrint('✅ الاتصال بـ API التفاسير ناجح');
        return true;
      } else {
        debugPrint('❌ فشل الاتصال بـ API التفاسير: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الاتصال: $e');
      return false;
    }
  }

  /// جلب تفسير محلي للاختبار
  static VerseWithTafsir getLocalSampleTafsir() {
    return const VerseWithTafsir(
      surahNumber: 1,
      verseNumber: 1,
      verseText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      tafsirText: 'أبدأ قراءتي باسم الله مستعيناً به، (الله) علم على الرب -تبارك وتعالى- المعبود بحق دون سواه، وهو أخص أسماء الله تعالى ولا يسمى به غيره سبحانه. (الرحمن) ذو الرحمة العامة الذي وسعت رحمته جميع الخلق، (الرحيم) بالمؤمنين، وهما اسمان من أسمائه تعالى.',
      tafsirName: 'التفسير الميسر',
    );
  }
}
