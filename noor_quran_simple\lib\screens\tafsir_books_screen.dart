import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import '../constants/app_constants.dart';
import '../models/library_models.dart';
import 'section_reading_screen.dart';

/// شاشة كتب التفسير
class TafsirBooksScreen extends StatefulWidget {
  const TafsirBooksScreen({super.key});

  @override
  State<TafsirBooksScreen> createState() => _TafsirBooksScreenState();
}

class _TafsirBooksScreenState extends State<TafsirBooksScreen> {
  List<Book> _books = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTafsirBooks();
  }

  Future<void> _loadTafsirBooks() async {
    try {
      final String response = await rootBundle.loadString(
        'assets/data/tafsir_books.json',
      );
      final data = json.decode(response);

      setState(() {
        _books = (data['books'] as List)
            .map((bookJson) => Book.fromJson(bookJson))
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل كتب التفسير: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('كتب التفسير'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _books.isEmpty
          ? const Center(
              child: Text(
                'لا توجد كتب تفسير متاحة',
                style: TextStyle(fontSize: 18),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: _books.length,
              itemBuilder: (context, index) {
                final book = _books[index];
                return _buildBookCard(book);
              },
            ),
    );
  }

  Widget _buildBookCard(Book book) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: InkWell(
        onTap: () => _navigateToBookDetails(book),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 80,
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.book, color: Colors.white, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book.title,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeLarge,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (book.author != null)
                      Text(
                        book.author!,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      book.description,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.menu_book,
                          size: 16,
                          color: AppConstants.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${book.chapters.length} فصل',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppConstants.textSecondaryColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToBookDetails(Book book) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TafsirBookDetailsScreen(book: book),
      ),
    );
  }
}

/// شاشة تفاصيل كتاب التفسير
class TafsirBookDetailsScreen extends StatelessWidget {
  final Book book;

  const TafsirBookDetailsScreen({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(book.title),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBookHeader(),
            const SizedBox(height: 24),
            _buildChaptersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildBookHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 80,
                  height: 100,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.book, color: Colors.white, size: 40),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        book.title,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (book.author != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          book.author!,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                      const SizedBox(height: 12),
                      Text(
                        book.description,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChaptersList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الفصول',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...book.chapters.map((chapter) => _buildChapterCard(chapter)).toList(),
      ],
    );
  }

  Widget _buildChapterCard(Chapter chapter) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor,
          child: Text(
            chapter.order.toString(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(chapter.title),
        subtitle: Text(chapter.summary ?? ''),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _navigateToChapterSections(context, chapter),
      ),
    );
  }

  void _navigateToChapterSections(BuildContext context, Chapter chapter) {
    // التنقل إلى أقسام الفصل
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            TafsirChapterSectionsScreen(book: book, chapter: chapter),
      ),
    );
  }
}

/// شاشة أقسام فصل التفسير
class TafsirChapterSectionsScreen extends StatelessWidget {
  final Book book;
  final Chapter chapter;

  const TafsirChapterSectionsScreen({
    super.key,
    required this.book,
    required this.chapter,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(chapter.title),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: chapter.sections.length,
        itemBuilder: (context, index) {
          final section = chapter.sections[index];
          return _buildSectionCard(context, section);
        },
      ),
    );
  }

  Widget _buildSectionCard(BuildContext context, Section section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor,
          child: Text(
            section.order.toString(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(section.title),
        subtitle: Text(
          section.content.length > 100
              ? '${section.content.substring(0, 100)}...'
              : section.content,
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _navigateToSectionReading(context, section),
      ),
    );
  }

  void _navigateToSectionReading(BuildContext context, Section section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SectionReadingScreen(
          book: book,
          chapter: chapter,
          section: section,
        ),
      ),
    );
  }
}
