import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/library_models.dart';

/// شاشة قراءة القسم
class SectionReadingScreen extends StatefulWidget {
  final Book book;
  final Chapter chapter;
  final Section section;

  const SectionReadingScreen({
    super.key,
    required this.book,
    required this.chapter,
    required this.section,
  });

  @override
  State<SectionReadingScreen> createState() => _SectionReadingScreenState();
}

class _SectionReadingScreenState extends State<SectionReadingScreen> {
  double _fontSize = AppConstants.fontSizeMedium;
  bool _showCommentary = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.section.title),
        backgroundColor: AppConstants.primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.text_fields),
            onPressed: () => _showFontSizeDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'commentary':
                  setState(() {
                    _showCommentary = !_showCommentary;
                  });
                  break;
                case 'bookmark':
                  _addBookmark();
                  break;
                case 'share':
                  _shareSection();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'commentary',
                child: Row(
                  children: [
                    Icon(
                      _showCommentary ? Icons.visibility_off : Icons.visibility,
                    ),
                    const SizedBox(width: 8),
                    Text(_showCommentary ? 'إخفاء الشرح' : 'إظهار الشرح'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'bookmark',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_add),
                    SizedBox(width: 8),
                    Text('إضافة علامة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(),
            const SizedBox(height: 24),
            _buildSectionContent(),
            if (_showCommentary && widget.section.commentary != null) ...[
              const SizedBox(height: 24),
              _buildCommentary(),
            ],
            const SizedBox(height: 24),
            _buildKeywords(),
            const SizedBox(height: 24),
            _buildNavigation(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      widget.section.order.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.section.title,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.book.title} - ${widget.chapter.title}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionContent() {
    // تقسيم النص إلى فقرات
    List<String> paragraphs = widget.section.content
        .split('\n\n')
        .where((paragraph) => paragraph.trim().isNotEmpty)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المحتوى',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...paragraphs.asMap().entries.map((entry) {
          int index = entry.key;
          String paragraph = entry.value;
          return _buildParagraphCard(paragraph, index);
        }).toList(),
      ],
    );
  }

  Widget _buildParagraphCard(String paragraph, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // النص
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                paragraph,
                style: TextStyle(fontSize: _fontSize, height: 1.8),
                textAlign: TextAlign.justify,
              ),
            ),
            const SizedBox(height: 16),
            // أزرار الشرح والاستماع
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        _showParagraphExplanation(paragraph, index),
                    icon: const Icon(Icons.lightbulb_outline, size: 18),
                    label: const Text('الشرح'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor.withValues(
                        alpha: 0.1,
                      ),
                      foregroundColor: AppConstants.primaryColor,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _playParagraphAudio(paragraph, index),
                    icon: const Icon(Icons.play_arrow, size: 18),
                    label: const Text('الاستماع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.withValues(alpha: 0.1),
                      foregroundColor: Colors.green,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentary() {
    if (widget.section.commentary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: AppConstants.accentColor),
                const SizedBox(width: 8),
                const Text(
                  'الشرح والتوضيح',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppConstants.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.section.commentary!,
                style: TextStyle(fontSize: _fontSize - 2, height: 1.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeywords() {
    if (widget.section.keywords == null || widget.section.keywords!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الكلمات المفتاحية',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.section.keywords!.map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    keyword,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigation() {
    final currentIndex = widget.chapter.sections.indexOf(widget.section);
    final hasPrevious = currentIndex > 0;
    final hasNext = currentIndex < widget.chapter.sections.length - 1;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: hasPrevious ? () => _navigateToSection(-1) : null,
                icon: const Icon(Icons.arrow_back),
                label: const Text('السابق'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: hasPrevious
                      ? AppConstants.primaryColor
                      : Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${currentIndex + 1} من ${widget.chapter.sections.length}',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: hasNext ? () => _navigateToSection(1) : null,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('التالي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: hasNext
                      ? AppConstants.primaryColor
                      : Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('حجم الخط'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'نص تجريبي بحجم الخط الحالي',
                    style: TextStyle(fontSize: _fontSize),
                  ),
                  const SizedBox(height: 16),
                  Slider(
                    value: _fontSize,
                    min: 12.0,
                    max: 24.0,
                    divisions: 12,
                    label: _fontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _fontSize = value;
                      });
                      this.setState(() {});
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إغلاق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToSection(int direction) {
    final currentIndex = widget.chapter.sections.indexOf(widget.section);
    final newIndex = currentIndex + direction;

    if (newIndex >= 0 && newIndex < widget.chapter.sections.length) {
      final newSection = widget.chapter.sections[newIndex];
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => SectionReadingScreen(
            book: widget.book,
            chapter: widget.chapter,
            section: newSection,
          ),
        ),
      );
    }
  }

  void _addBookmark() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تمت إضافة علامة مرجعية'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareSection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ المحتوى للمشاركة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showParagraphExplanation(String paragraph, int index) {
    String explanation =
        widget.section.explanation ?? 'لا يوجد شرح متاح لهذه الفقرة حالياً';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('شرح الفقرة ${index + 1}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  paragraph,
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(explanation, style: const TextStyle(fontSize: 16)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _playParagraphAudio(String paragraph, int index) {
    // هنا يمكن إضافة منطق تشغيل الصوت
    // يمكن استخدام مكتبة مثل audioplayers أو just_audio

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تشغيل الصوت للفقرة ${index + 1}'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'إيقاف',
          onPressed: () {
            // إيقاف الصوت
          },
        ),
      ),
    );
  }
}
