import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/reciter.dart';
import '../models/surah.dart';
import '../constants/app_constants.dart';
import '../services/html_audio_service.dart';

enum RepeatMode { none, one, all }

enum PlayerState { stopped, loading, playing, paused, error }

class AudioProvider extends ChangeNotifier {
  final AudioPlayer? _audioPlayer = kIsWeb ? null : AudioPlayer();
  final HtmlAudioService? _htmlAudioService = kIsWeb
      ? HtmlAudioService()
      : null;

  Reciter? _selectedReciter;
  Surah? _currentSurah;
  PlayerState _playerState = PlayerState.stopped;
  RepeatMode _repeatMode = RepeatMode.none;

  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _playbackSpeed = 1.0;
  bool _isAutoPlay = false;
  String? _errorMessage;

  List<Reciter> _availableReciters = [];

  // Getters
  Reciter? get selectedReciter => _selectedReciter;
  Surah? get currentSurah => _currentSurah;
  PlayerState get playerState => _playerState;
  RepeatMode get repeatMode => _repeatMode;

  Duration get duration => _duration;
  Duration get position => _position;
  double get playbackSpeed => _playbackSpeed;
  bool get isAutoPlay => _isAutoPlay;
  String? get errorMessage => _errorMessage;

  List<Reciter> get availableReciters => _availableReciters;

  bool get isPlaying => _playerState == PlayerState.playing;
  bool get isPaused => _playerState == PlayerState.paused;
  bool get isLoading => _playerState == PlayerState.loading;
  bool get isStopped => _playerState == PlayerState.stopped;
  bool get hasError => _playerState == PlayerState.error;

  double get progress => _duration.inMilliseconds > 0
      ? _position.inMilliseconds / _duration.inMilliseconds
      : 0.0;

  AudioProvider() {
    _initializeAudio();
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      _loadSettings();
      _loadAvailableReciters();
    });
  }

  void _initializeAudio() {
    if (kIsWeb) {
      _initializeWebAudio();
    } else {
      _initializeMobileAudio();
    }
  }

  void _initializeWebAudio() {
    if (_htmlAudioService == null) return;

    _htmlAudioService.addPlayingListener((isPlaying) {
      _playerState = _htmlAudioService.isLoading
          ? PlayerState.loading
          : isPlaying
          ? PlayerState.playing
          : PlayerState.paused;
      notifyListeners();
    });

    _htmlAudioService.addPositionListener((position) {
      _position = position;
      notifyListeners();
    });

    _htmlAudioService.addDurationListener((duration) {
      _duration = duration;
      notifyListeners();
    });
  }

  void _initializeMobileAudio() {
    if (_audioPlayer == null) return;

    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      switch (state.processingState) {
        case ProcessingState.idle:
          _playerState = PlayerState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          _playerState = PlayerState.loading;
          break;
        case ProcessingState.ready:
          _playerState = state.playing
              ? PlayerState.playing
              : PlayerState.paused;
          break;
        case ProcessingState.completed:
          _onPlaybackCompleted();
          break;
      }
      notifyListeners();
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });

    // Listen to errors
    _audioPlayer.playbackEventStream.listen(
      (event) {},
      onError: (error) {
        _setError('خطأ في تشغيل الصوت: ${error.toString()}');
      },
    );
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load selected reciter
      final reciterId = prefs.getString(AppConstants.keySelectedReciter);
      if (reciterId != null) {
        _selectedReciter = PopularReciters.getById(reciterId);
      }

      // Load auto play setting
      _isAutoPlay = prefs.getBool(AppConstants.keyAutoPlay) ?? false;

      // Load repeat mode
      final repeatModeIndex = prefs.getInt(AppConstants.keyRepeatMode) ?? 0;
      _repeatMode = RepeatMode.values[repeatModeIndex];

      // Load playback speed
      _playbackSpeed = prefs.getDouble('playback_speed') ?? 1.0;
    } catch (e) {
      debugPrint('Error loading audio settings: $e');
    }
  }

  void _loadAvailableReciters() {
    _availableReciters = PopularReciters.all;

    // Set default reciter if none selected
    if (_selectedReciter == null && _availableReciters.isNotEmpty) {
      _selectedReciter = _availableReciters.first;
    }

    notifyListeners();
  }

  Future<void> selectReciter(Reciter reciter) async {
    if (_selectedReciter?.id == reciter.id) return;

    _selectedReciter = reciter;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter.id);
    } catch (e) {
      debugPrint('Error saving selected reciter: $e');
    }

    // If currently playing, switch to new reciter
    if (_currentSurah != null && isPlaying) {
      await playSurah(_currentSurah!);
    }
  }

  Future<void> playSurah(Surah surah) async {
    if (_selectedReciter == null) {
      _setError('لم يتم اختيار قارئ');
      return;
    }

    _currentSurah = surah;
    _clearError();
    _playerState = PlayerState.loading;
    notifyListeners();

    try {
      // Try to get audio URL from surah's first ayah if available
      String? audioUrl;
      if (surah.ayahs.isNotEmpty && surah.ayahs.first.audioUrl != null) {
        // Use the first ayah's audio URL (this will be the full surah audio from the API)
        audioUrl = surah.ayahs.first.audioUrl!;
      } else {
        // Fallback to reciter's URL
        audioUrl = _selectedReciter!.getAudioUrl(surah.number);
      }

      debugPrint('Playing audio URL: $audioUrl');

      if (kIsWeb) {
        await _htmlAudioService!.setUrl(audioUrl);
        await _htmlAudioService.setPlaybackRate(_playbackSpeed);
        await _htmlAudioService.play();
      } else {
        await _audioPlayer!.setUrl(audioUrl);
        await _audioPlayer.setSpeed(_playbackSpeed);
        await _audioPlayer.play();
      }
    } catch (e) {
      _setError('فشل في تشغيل السورة: ${e.toString()}');
      _playerState = PlayerState.error;
      notifyListeners();
    }
  }

  Future<void> play() async {
    if (_currentSurah == null) return;

    try {
      if (kIsWeb) {
        await _htmlAudioService!.play();
      } else {
        await _audioPlayer!.play();
      }
    } catch (e) {
      _setError('فشل في التشغيل: ${e.toString()}');
    }
  }

  Future<void> pause() async {
    try {
      if (kIsWeb) {
        await _htmlAudioService!.pause();
      } else {
        await _audioPlayer!.pause();
      }
    } catch (e) {
      _setError('فشل في الإيقاف المؤقت: ${e.toString()}');
    }
  }

  Future<void> stop() async {
    try {
      if (kIsWeb) {
        await _htmlAudioService!.stop();
      } else {
        await _audioPlayer!.stop();
      }
      _position = Duration.zero;
      _playerState = PlayerState.stopped;
      notifyListeners();
    } catch (e) {
      _setError('فشل في الإيقاف: ${e.toString()}');
    }
  }

  Future<void> seek(Duration position) async {
    try {
      if (kIsWeb) {
        await _htmlAudioService!.seek(position);
      } else {
        await _audioPlayer!.seek(position);
      }
    } catch (e) {
      _setError('فشل في التنقل: ${e.toString()}');
    }
  }

  Future<void> setPlaybackSpeed(double speed) async {
    if (speed < AppConstants.minPlaybackSpeed ||
        speed > AppConstants.maxPlaybackSpeed) {
      return;
    }

    _playbackSpeed = speed;
    notifyListeners();

    try {
      if (kIsWeb) {
        await _htmlAudioService!.setPlaybackRate(speed);
      } else {
        await _audioPlayer!.setSpeed(speed);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('playback_speed', speed);
    } catch (e) {
      debugPrint('Error setting playback speed: $e');
    }
  }

  Future<void> setRepeatMode(RepeatMode mode) async {
    _repeatMode = mode;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(AppConstants.keyRepeatMode, mode.index);
    } catch (e) {
      debugPrint('Error saving repeat mode: $e');
    }
  }

  Future<void> toggleRepeatMode() async {
    switch (_repeatMode) {
      case RepeatMode.none:
        await setRepeatMode(RepeatMode.one);
        break;
      case RepeatMode.one:
        await setRepeatMode(RepeatMode.all);
        break;
      case RepeatMode.all:
        await setRepeatMode(RepeatMode.none);
        break;
    }
  }

  Future<void> setAutoPlay(bool autoPlay) async {
    _isAutoPlay = autoPlay;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyAutoPlay, autoPlay);
    } catch (e) {
      debugPrint('Error saving auto play setting: $e');
    }
  }

  void _onPlaybackCompleted() {
    switch (_repeatMode) {
      case RepeatMode.one:
        // Repeat current surah
        if (_currentSurah != null) {
          playSurah(_currentSurah!);
        }
        break;
      case RepeatMode.all:
        // Play next surah (implementation depends on surah list)
        // This would need to be coordinated with QuranProvider
        break;
      case RepeatMode.none:
        _playerState = PlayerState.stopped;
        _position = Duration.zero;
        notifyListeners();
        break;
    }
  }

  void _setError(String error) {
    _errorMessage = error;
    _playerState = PlayerState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Helper methods
  String get repeatModeDisplayName {
    switch (_repeatMode) {
      case RepeatMode.none:
        return 'بدون تكرار';
      case RepeatMode.one:
        return 'تكرار السورة';
      case RepeatMode.all:
        return 'تكرار الكل';
    }
  }

  IconData get repeatModeIcon {
    switch (_repeatMode) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
      case RepeatMode.all:
        return Icons.repeat;
    }
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    if (kIsWeb) {
      _htmlAudioService?.dispose();
    } else {
      _audioPlayer?.dispose();
    }
    super.dispose();
  }
}
