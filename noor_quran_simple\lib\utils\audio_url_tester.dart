import 'package:flutter/foundation.dart';
import '../services/real_audio_service.dart';

class AudioUrlTester {
  /// اختبار جميع روابط القراء
  static void testAllReciters() {
    debugPrint('=== اختبار روابط الصوت ===');

    final reciters = RealAudioService.getAllReciters();

    debugPrint('عدد القراء المتاحين: ${reciters.length}');

    for (final entry in reciters.entries) {
      final reciterKey = entry.key;
      final reciterData = entry.value;
      final url = RealAudioService.getAudioUrl(reciterKey, 1);

      debugPrint('${reciterData['name']}: $url');
    }
  }

  /// اختبار قارئ محدد مع عدة سور
  static void testReciterUrls(String reciterKey, {int maxSurahs = 5}) {
    debugPrint('=== اختبار روابط القارئ $reciterKey ===');

    final reciterInfo = RealAudioService.getReciterInfo(reciterKey);
    if (reciterInfo != null) {
      debugPrint('القارئ: ${reciterInfo['name']}');
      debugPrint('البلد: ${reciterInfo['country']}');
      debugPrint('مختبر: ${reciterInfo['tested']}');
      debugPrint('');
    }

    for (int i = 1; i <= maxSurahs; i++) {
      final url = RealAudioService.getAudioUrl(reciterKey, i);
      debugPrint('السورة $i: $url');
    }
  }

  /// مقارنة روابط قارئين مختلفين
  static void compareReciters(
    String reciter1,
    String reciter2,
    int surahNumber,
  ) {
    debugPrint('=== مقارنة القراء للسورة $surahNumber ===');

    final url1 = RealAudioService.getAudioUrl(reciter1, surahNumber);
    final url2 = RealAudioService.getAudioUrl(reciter2, surahNumber);

    final info1 = RealAudioService.getReciterInfo(reciter1);
    final info2 = RealAudioService.getReciterInfo(reciter2);

    debugPrint('${info1?['name'] ?? reciter1}: $url1');
    debugPrint('${info2?['name'] ?? reciter2}: $url2');

    if (url1 == url2) {
      debugPrint('⚠️ تحذير: نفس الرابط للقارئين!');
    } else {
      debugPrint('✅ روابط مختلفة - جيد!');
    }
  }

  /// اختبار شامل
  static void runFullTest() {
    debugPrint('🔍 بدء الاختبار الشامل للصوت...');

    // التحقق من تفرد الروابط أولاً
    RealAudioService.validateUniqueUrls();

    debugPrint('\n');

    // اختبار جميع القراء
    testAllReciters();

    debugPrint('\n');

    // اختبار مقارنات
    compareReciters('mishari_alafasy', 'abdulbasit_abdulsamad', 1);
    compareReciters('mishari_alafasy', 'maher_almuaiqly', 1);
    compareReciters('abdulbasit_abdulsamad', 'saad_alghamdi', 1);
    compareReciters('nasser_alqatami', 'khalid_aljalil', 1);

    debugPrint('\n');

    // اختبار القراء المختبرين
    final testedReciters = RealAudioService.getTestedReciters();
    debugPrint('القراء المختبرين (${testedReciters.length}):');
    for (final entry in testedReciters.entries) {
      debugPrint('- ${entry.value['name']} (${entry.value['country']})');
    }

    debugPrint('\n✅ انتهى الاختبار الشامل');
  }
}
