import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/audio_provider.dart';
import '../providers/library_provider.dart';
import '../providers/tafsir_provider.dart';
import '../models/surah.dart';
import '../widgets/audio_player_widget.dart';
import 'reciter_selection_screen.dart';
import 'tafsir_settings_screen.dart';

class QuranReaderScreen extends StatefulWidget {
  final Surah surah;

  const QuranReaderScreen({super.key, required this.surah});

  @override
  State<QuranReaderScreen> createState() => _QuranReaderScreenState();
}

class _QuranReaderScreenState extends State<QuranReaderScreen> {
  late ScrollController _scrollController;
  bool _showAppBar = true;
  bool _showAudioPlayer = false;
  double _fontSize = 24.0;
  bool _showTranslation = false;
  bool _nightMode = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _loadSurahData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSurahData() async {
    final quranProvider = context.read<QuranProvider>();
    if (widget.surah.ayahs.isEmpty) {
      await quranProvider.loadSurahWithAyahs(widget.surah.number);
    }
    await quranProvider.setCurrentSurah(widget.surah);
  }

  /// تشغيل السورة بصوت القارئ المختار
  void _playWithSelectedReciter(Surah surah) {
    final libraryProvider = context.read<LibraryProvider>();
    final audioProvider = context.read<AudioProvider>();

    if (libraryProvider.selectedReciter != null) {
      // تشغيل بصوت القارئ المختار من المكتبة
      audioProvider.playSurahWithLibraryReciter(
          surah, libraryProvider.selectedReciter!);

      setState(() {
        _showAudioPlayer = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.volume_up, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                    'تشغيل ${surah.name} بصوت ${libraryProvider.selectedReciter!.nameArabic}'),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'تغيير القارئ',
            textColor: Colors.white,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ReciterSelectionScreen(),
                ),
              );
            },
          ),
        ),
      );
    } else {
      // لا يوجد قارئ مختار، استخدم القارئ الافتراضي
      audioProvider.playSurah(surah);
      setState(() {
        _showAudioPlayer = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.info, color: Colors.white),
              SizedBox(width: 8),
              Expanded(
                child:
                    Text('لم يتم اختيار قارئ. يتم استخدام القارئ الافتراضي.'),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'اختيار قارئ',
            textColor: Colors.white,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ReciterSelectionScreen(),
                ),
              );
            },
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final quranProvider = context.watch<QuranProvider>();

    final currentSurah = quranProvider.currentSurah ?? widget.surah;

    return Scaffold(
      backgroundColor: _nightMode ? Colors.black87 : null,
      appBar: _showAppBar
          ? AppBar(
              title: Text(currentSurah.name),
              actions: [
                IconButton(
                  icon: const Icon(Icons.text_increase),
                  onPressed: themeProvider.increaseFontSize,
                  tooltip: 'تكبير الخط',
                ),
                IconButton(
                  icon: const Icon(Icons.text_decrease),
                  onPressed: themeProvider.decreaseFontSize,
                  tooltip: 'تصغير الخط',
                ),
                IconButton(
                  icon: const Icon(Icons.book_outlined),
                  tooltip: 'إعدادات التفسير والترجمة',
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TafsirSettingsScreen(),
                      ),
                    );
                  },
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'bookmark':
                        _bookmarkSurah();
                        break;
                      case 'share':
                        _shareSurah();
                        break;
                      case 'font_size':
                        _showFontSizeDialog();
                        break;
                      case 'night_mode':
                        setState(() {
                          _nightMode = !_nightMode;
                        });
                        break;
                      case 'audio':
                        setState(() {
                          _showAudioPlayer = !_showAudioPlayer;
                        });
                        break;
                      case 'reciter':
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const ReciterSelectionScreen(),
                          ),
                        );
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'bookmark',
                      child: Row(
                        children: [
                          Icon(Icons.bookmark_add),
                          SizedBox(width: 8),
                          Text('إضافة للمفضلة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'font_size',
                      child: Row(
                        children: [
                          Icon(Icons.text_fields),
                          SizedBox(width: 8),
                          Text('حجم الخط'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'night_mode',
                      child: Row(
                        children: [
                          Icon(_nightMode ? Icons.light_mode : Icons.dark_mode),
                          const SizedBox(width: 8),
                          Text(_nightMode ? 'الوضع النهاري' : 'الوضع الليلي'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('مشاركة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'audio',
                      child: Row(
                        children: [
                          Icon(Icons.play_arrow),
                          SizedBox(width: 8),
                          Text('تشغيل صوتي'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reciter',
                      child: Row(
                        children: [
                          Icon(Icons.person),
                          SizedBox(width: 8),
                          Text('اختيار القارئ'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : null,
      body: quranProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : currentSurah.ayahs.isEmpty
              ? const Center(child: Text('لا توجد آيات متاحة'))
              : Column(
                  children: [
                    // Surah Header
                    _buildSurahHeader(currentSurah, theme, themeProvider),
                    // Ayahs List
                    Expanded(
                      child: ListView.builder(
                        controller: _scrollController,
                        padding:
                            const EdgeInsets.all(AppConstants.paddingMedium),
                        itemCount: currentSurah.ayahs.length,
                        itemBuilder: (context, index) {
                          final ayah = currentSurah.ayahs[index];
                          return _buildAyahCard(
                            ayah,
                            theme,
                            themeProvider,
                            quranProvider,
                          );
                        },
                      ),
                    ),
                  ],
                ),
      bottomSheet: _showAudioPlayer
          ? AudioPlayerWidget(
              surah: currentSurah,
              isExpanded: false,
              onToggleExpanded: () {
                // Could implement expanded audio player here
              },
            )
          : null,
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!_showAudioPlayer)
            FloatingActionButton(
              heroTag: "audio",
              onPressed: () => _playWithSelectedReciter(currentSurah),
              child: const Icon(Icons.play_arrow),
            ),
          if (!_showAudioPlayer) const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "fullscreen",
            onPressed: () {
              setState(() {
                _showAppBar = !_showAppBar;
              });
            },
            child: Icon(_showAppBar ? Icons.fullscreen : Icons.fullscreen_exit),
          ),
        ],
      ),
    );
  }

  Widget _buildSurahHeader(
    Surah surah,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: themeProvider.getGradientColors(context),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Text(
            surah.name,
            style: theme.textTheme.headlineLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            surah.englishNameTranslation,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildInfoChip(
                '${surah.numberOfAyahs} آية',
                Icons.format_list_numbered,
              ),
              _buildInfoChip(surah.revelationTypeArabic, Icons.location_on),
              _buildInfoChip('الجزء ${surah.juz}', Icons.book),
            ],
          ),
          if (surah.number != 1 && surah.number != 9) ...[
            const SizedBox(height: 16),
            Text(
              'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
              style: themeProvider.getQuranTextStyle(context).copyWith(
                    color: Colors.white,
                    fontSize: themeProvider.fontSize + 4,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAyahCard(
    Ayah ayah,
    ThemeData theme,
    ThemeProvider themeProvider,
    QuranProvider quranProvider,
  ) {
    final isBookmarked = quranProvider.isAyahBookmarked(ayah);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Ayah Number and Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${ayah.numberInSurah}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                        color: isBookmarked ? theme.primaryColor : null,
                      ),
                      onPressed: () => quranProvider.toggleBookmark(ayah),
                      tooltip:
                          isBookmarked ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
                    ),
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () {
                        // TODO: Implement share ayah
                      },
                      tooltip: 'مشاركة الآية',
                    ),
                    IconButton(
                      icon: const Icon(Icons.play_arrow),
                      onPressed: () {
                        // TODO: Implement play ayah
                      },
                      tooltip: 'تشغيل الآية',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            // Ayah Text
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: _nightMode
                    ? Colors.grey[800]?.withValues(alpha: 0.3)
                    : Colors.grey[50],
                borderRadius:
                    BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: theme.primaryColor.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: SelectableText(
                ayah.text,
                style: themeProvider.getQuranTextStyle(context).copyWith(
                      fontSize: _fontSize,
                      color: _nightMode ? Colors.white : Colors.black87,
                    ),
                textAlign: TextAlign.justify,
                textDirection: TextDirection.rtl,
                onTap: () {
                  quranProvider.setCurrentAyah(ayah);
                },
              ),
            ),
            if (ayah.sajda) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.orange,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'سجدة',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.orange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// إضافة السورة للمفضلة
  void _bookmarkSurah() {
    final quranProvider = context.read<QuranProvider>();
    // إضافة السورة كاملة للمفضلة
    for (final ayah in widget.surah.ayahs) {
      quranProvider.toggleBookmark(ayah);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة سورة ${widget.surah.name} للمفضلة'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// مشاركة السورة
  void _shareSurah() {
    // TODO: تنفيذ مشاركة السورة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة قيد التطوير'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// عرض حوار تغيير حجم الخط
  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'مثال على النص',
                style: TextStyle(fontSize: _fontSize),
              ),
              const SizedBox(height: 16),
              Slider(
                value: _fontSize,
                min: 16.0,
                max: 36.0,
                divisions: 10,
                label: _fontSize.round().toString(),
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                  this.setState(() {});
                },
              ),
              Text('${_fontSize.round()}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
