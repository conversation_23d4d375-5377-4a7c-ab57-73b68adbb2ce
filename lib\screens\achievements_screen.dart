import 'package:flutter/material.dart';
import '../models/achievement.dart';
import '../services/achievement_service.dart';
import '../widgets/achievement_card.dart';
import '../widgets/challenge_card.dart';

/// شاشة الإنجازات والتحديات - لمسة تحفيزية خاصة
class AchievementsScreen extends StatefulWidget {
  const AchievementsScreen({super.key});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Achievement> _achievements = [];
  List<Challenge> _challenges = [];
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await AchievementService.initialize();
    setState(() {
      _achievements = AchievementService.getAllAchievements();
      _stats = AchievementService.getAchievementStats();
      // _challenges = AchievementService.getActiveChallenges();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإنجازات والتحديات'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.emoji_events), text: 'الإنجازات'),
            Tab(icon: Icon(Icons.flag), text: 'التحديات'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAchievementsTab(),
          _buildChallengesTab(),
          _buildStatsTab(),
        ],
      ),
    );
  }

  /// تبويب الإنجازات
  Widget _buildAchievementsTab() {
    final unlockedAchievements = _achievements.where((a) => a.isUnlocked).toList();
    final lockedAchievements = _achievements.where((a) => !a.isUnlocked).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildQuickStats(),
          const SizedBox(height: 20),

          // الإنجازات المفتوحة
          if (unlockedAchievements.isNotEmpty) ...[
            Row(
              children: [
                const Icon(Icons.emoji_events, color: Colors.amber),
                const SizedBox(width: 8),
                Text(
                  'الإنجازات المحققة (${unlockedAchievements.length})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...unlockedAchievements.map((achievement) => 
                AchievementCard(achievement: achievement, isUnlocked: true)),
            const SizedBox(height: 20),
          ],

          // الإنجازات المقفلة
          if (lockedAchievements.isNotEmpty) ...[
            Row(
              children: [
                const Icon(Icons.lock, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'الإنجازات قيد التقدم (${lockedAchievements.length})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...lockedAchievements.map((achievement) => 
                AchievementCard(achievement: achievement, isUnlocked: false)),
          ],
        ],
      ),
    );
  }

  /// تبويب التحديات
  Widget _buildChallengesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تحدي اليوم
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.today, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'تحدي اليوم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text('اقرأ 10 آيات اليوم'),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: 0.3,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                  const SizedBox(height: 8),
                  const Text('3 / 10 آيات'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // التحديات النشطة
          const Text(
            'التحديات النشطة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // قائمة التحديات (مؤقتة)
          _buildChallengeCard(
            'تحدي الأسبوع',
            'اقرأ 100 آية هذا الأسبوع',
            0.6,
            '60 / 100 آيات',
            Icons.calendar_today,
            Colors.green,
          ),
          _buildChallengeCard(
            'تحدي الشهر',
            'اقرأ 5 سور كاملة',
            0.4,
            '2 / 5 سور',
            Icons.calendar_month,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإحصائيات العامة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow('إجمالي الإنجازات', '${_stats['total_achievements'] ?? 0}'),
                  _buildStatRow('الإنجازات المحققة', '${_stats['unlocked_achievements'] ?? 0}'),
                  _buildStatRow('النقاط المكتسبة', '${_stats['total_points'] ?? 0}'),
                  _buildStatRow('نسبة الإنجاز', '${_stats['completion_percentage'] ?? 0}%'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // إنجازات خاصة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإنجازات الخاصة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow('الإنجازات الخاصة', '${_stats['special_achievements'] ?? 0}'),
                  const SizedBox(height: 8),
                  const Text(
                    'الإنجازات الخاصة هي إنجازات نادرة ومميزة تتطلب جهداً إضافياً',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildQuickStatItem(
              '${_stats['unlocked_achievements'] ?? 0}',
              'محققة',
              Icons.emoji_events,
              Colors.amber,
            ),
            _buildQuickStatItem(
              '${_stats['total_points'] ?? 0}',
              'نقطة',
              Icons.stars,
              Colors.blue,
            ),
            _buildQuickStatItem(
              '${_stats['completion_percentage'] ?? 0}%',
              'مكتمل',
              Icons.trending_up,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatItem(String value, String label, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildChallengeCard(
    String title,
    String description,
    double progress,
    String progressText,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
            const SizedBox(height: 8),
            Text(progressText),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
