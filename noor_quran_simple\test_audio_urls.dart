import 'lib/services/real_audio_service.dart';

void main() {
  print('=== اختبار روابط الصوت ===\n');
  
  // طباعة معلومات جميع القراء
  RealAudioService.printAllReciters();
  
  print('\n=== اختبار روابط السورة الأولى ===');
  
  final reciters = RealAudioService.getAllReciters();
  
  for (final entry in reciters.entries) {
    final reciterKey = entry.key;
    final reciterData = entry.value;
    final url = RealAudioService.getAudioUrl(reciterKey, 1);
    
    print('${reciterData['name']}: $url');
  }
  
  print('\n=== اختبار روابط مختلفة لمشاري العفاسي ===');
  
  for (int i = 1; i <= 5; i++) {
    final url = RealAudioService.getAudioUrl('mishari_alafasy', i);
    print('السورة $i: $url');
  }
  
  print('\n=== اختبار القراء المختبرين ===');
  
  final testedReciters = RealAudioService.getTestedReciters();
  print('عدد القراء المختبرين: ${testedReciters.length}');
  
  for (final entry in testedReciters.entries) {
    print('- ${entry.value['name']} (${entry.value['country']})');
  }
}
