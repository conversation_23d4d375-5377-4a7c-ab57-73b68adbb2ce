import 'package:flutter/material.dart';
import 'services/local_quran_service.dart';

class TestDataLoadingScreen extends StatefulWidget {
  const TestDataLoadingScreen({super.key});

  @override
  State<TestDataLoadingScreen> createState() => _TestDataLoadingScreenState();
}

class _TestDataLoadingScreenState extends State<TestDataLoadingScreen> {
  String _status = 'جاري التحميل...';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _testDataLoading();
  }

  Future<void> _testDataLoading() async {
    try {
      setState(() {
        _status = 'بدء تحميل البيانات...';
      });

      final surahs = await LocalQuranService.getLocalQuran();
      
      setState(() {
        _status = 'تم تحميل ${surahs.length} سورة بنجاح!\n';
        
        if (surahs.isNotEmpty) {
          final firstSurah = surahs.first;
          _status += 'السورة الأولى: ${firstSurah.name}\n';
          _status += 'عدد الآيات: ${firstSurah.ayahs.length}\n';
          
          if (firstSurah.ayahs.isNotEmpty) {
            _status += 'الآية الأولى: ${firstSurah.ayahs.first.text}';
          }
        }
        
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار تحميل البيانات'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isLoading)
              const Center(child: CircularProgressIndicator()),
            const SizedBox(height: 20),
            Text(
              _status,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            if (!_isLoading)
              ElevatedButton(
                onPressed: _testDataLoading,
                child: const Text('إعادة الاختبار'),
              ),
          ],
        ),
      ),
    );
  }
}
