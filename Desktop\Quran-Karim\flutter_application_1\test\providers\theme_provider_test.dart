import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noor_quran/providers/theme_provider.dart';
import 'package:noor_quran/constants/app_constants.dart';

void main() {
  group('ThemeProvider Tests', () {
    late ThemeProvider themeProvider;

    setUp(() {
      // Initialize SharedPreferences with empty values
      SharedPreferences.setMockInitialValues({});
      themeProvider = ThemeProvider();
    });

    group('Theme Mode', () {
      test('should start with system theme mode', () {
        expect(themeProvider.themeMode, ThemeMode.system);
        expect(themeProvider.isSystemMode, true);
        expect(themeProvider.isDarkMode, false);
        expect(themeProvider.isLightMode, false);
      });

      test('should set theme mode correctly', () async {
        // Act
        await themeProvider.setThemeMode(ThemeMode.dark);
        
        // Assert
        expect(themeProvider.themeMode, ThemeMode.dark);
        expect(themeProvider.isDarkMode, true);
        expect(themeProvider.isSystemMode, false);
        expect(themeProvider.isLightMode, false);
      });

      test('should toggle theme correctly', () async {
        // Start with light mode
        await themeProvider.setThemeMode(ThemeMode.light);
        expect(themeProvider.isLightMode, true);
        
        // Toggle to dark
        await themeProvider.toggleTheme();
        expect(themeProvider.isDarkMode, true);
        
        // Toggle to system
        await themeProvider.toggleTheme();
        expect(themeProvider.isSystemMode, true);
        
        // Toggle back to light
        await themeProvider.toggleTheme();
        expect(themeProvider.isLightMode, true);
      });

      test('should return correct display names', () {
        expect(themeProvider.themeModeDisplayName, 'تلقائي');
        
        themeProvider.setThemeMode(ThemeMode.light);
        expect(themeProvider.themeModeDisplayName, 'فاتح');
        
        themeProvider.setThemeMode(ThemeMode.dark);
        expect(themeProvider.themeModeDisplayName, 'داكن');
      });

      test('should return correct icons', () {
        expect(themeProvider.themeModeIcon, Icons.brightness_auto);
        
        themeProvider.setThemeMode(ThemeMode.light);
        expect(themeProvider.themeModeIcon, Icons.light_mode);
        
        themeProvider.setThemeMode(ThemeMode.dark);
        expect(themeProvider.themeModeIcon, Icons.dark_mode);
      });
    });

    group('Font Size', () {
      test('should start with default font size', () {
        expect(themeProvider.fontSize, AppConstants.fontSizeMedium);
      });

      test('should set font size correctly', () async {
        // Arrange
        const newSize = 20.0;
        
        // Act
        await themeProvider.setFontSize(newSize);
        
        // Assert
        expect(themeProvider.fontSize, newSize);
      });

      test('should increase font size correctly', () async {
        // Arrange
        final initialSize = themeProvider.fontSize;
        
        // Act
        await themeProvider.increaseFontSize();
        
        // Assert
        expect(themeProvider.fontSize, initialSize + 2.0);
      });

      test('should decrease font size correctly', () async {
        // Arrange
        final initialSize = themeProvider.fontSize;
        
        // Act
        await themeProvider.decreaseFontSize();
        
        // Assert
        expect(themeProvider.fontSize, initialSize - 2.0);
      });

      test('should not increase font size beyond maximum', () async {
        // Arrange
        await themeProvider.setFontSize(32.0); // Set to max
        
        // Act
        await themeProvider.increaseFontSize();
        
        // Assert
        expect(themeProvider.fontSize, 32.0); // Should remain at max
      });

      test('should not decrease font size below minimum', () async {
        // Arrange
        await themeProvider.setFontSize(12.0); // Set to min
        
        // Act
        await themeProvider.decreaseFontSize();
        
        // Assert
        expect(themeProvider.fontSize, 12.0); // Should remain at min
      });

      test('should reset font size to default', () async {
        // Arrange
        await themeProvider.setFontSize(24.0);
        
        // Act
        await themeProvider.resetFontSize();
        
        // Assert
        expect(themeProvider.fontSize, AppConstants.fontSizeMedium);
      });
    });

    group('Text Styles', () {
      testWidgets('should return correct Quran text style', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                // Act
                final style = themeProvider.getQuranTextStyle(context);
                
                // Assert
                expect(style.fontSize, themeProvider.fontSize);
                expect(style.height, 1.8);
                expect(style.fontWeight, FontWeight.normal);
                
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should return correct translation text style', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                // Act
                final style = themeProvider.getTranslationTextStyle(context);
                
                // Assert
                expect(style.fontSize, themeProvider.fontSize - 2);
                expect(style.height, 1.6);
                expect(style.fontWeight, FontWeight.normal);
                
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should return correct surah name text style', (tester) async {
        // Arrange
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                // Act
                final style = themeProvider.getSurahNameTextStyle(context);
                
                // Assert
                expect(style.fontSize, themeProvider.fontSize + 4);
                expect(style.fontWeight, FontWeight.bold);
                
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Animation Durations', () {
      test('should return correct animation durations', () {
        expect(themeProvider.shortAnimationDuration, const Duration(milliseconds: 200));
        expect(themeProvider.mediumAnimationDuration, const Duration(milliseconds: 300));
        expect(themeProvider.longAnimationDuration, const Duration(milliseconds: 500));
      });
    });

    group('State Colors', () {
      testWidgets('should return correct state colors', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                // Test disabled state
                final disabledColor = themeProvider.getStateColor(
                  context,
                  isSelected: false,
                  isPressed: false,
                  isDisabled: true,
                );
                expect(disabledColor, Theme.of(context).disabledColor);

                // Test pressed state
                final pressedColor = themeProvider.getStateColor(
                  context,
                  isSelected: false,
                  isPressed: true,
                  isDisabled: false,
                );
                expect(pressedColor, isA<Color>());

                // Test selected state
                final selectedColor = themeProvider.getStateColor(
                  context,
                  isSelected: true,
                  isPressed: false,
                  isDisabled: false,
                );
                expect(selectedColor, themeProvider.getThemeColor(context));

                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Gradient Colors', () {
      testWidgets('should return correct gradient colors for light theme', (tester) async {
        // Arrange
        await themeProvider.setThemeMode(ThemeMode.light);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.light(),
            home: Builder(
              builder: (context) {
                // Act
                final gradientColors = themeProvider.getGradientColors(context);
                
                // Assert
                expect(gradientColors, isA<List<Color>>());
                expect(gradientColors.length, 2);
                expect(gradientColors[0], AppConstants.primaryColor);
                expect(gradientColors[1], AppConstants.secondaryColor);
                
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should return correct gradient colors for dark theme', (tester) async {
        // Arrange
        await themeProvider.setThemeMode(ThemeMode.dark);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.dark(),
            home: Builder(
              builder: (context) {
                // Act
                final gradientColors = themeProvider.getGradientColors(context);
                
                // Assert
                expect(gradientColors, isA<List<Color>>());
                expect(gradientColors.length, 2);
                expect(gradientColors[0], AppConstants.darkPrimaryColor);
                expect(gradientColors[1], AppConstants.darkSecondaryColor);
                
                return Container();
              },
            ),
          ),
        );
      });
    });
  });
}
