import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../providers/theme_provider.dart';
import '../models/surah.dart';
import 'quran_reader_screen.dart';

class BookmarksScreen extends StatelessWidget {
  const BookmarksScreen({super.key});

  String _getSurahNameByNumber(int number) {
    const surahNames = {
      1: 'الفاتحة',
      2: 'البقرة',
      3: 'آل عمران',
      4: 'النساء',
      5: 'المائدة',
      6: 'الأنعام',
      7: 'الأعراف',
      8: 'الأنفال',
      9: 'التوبة',
      10: 'يونس',
      11: 'هود',
      12: 'يوسف',
      13: 'الرعد',
      14: 'إبراهيم',
      15: 'الحجر',
      16: 'النحل',
      17: 'الإسراء',
      18: 'الكهف',
      19: 'مريم',
      20: 'طه',
      21: 'الأنبياء',
      22: 'الحج',
      23: 'المؤمنون',
      24: 'النور',
      25: 'الفرقان',
      26: 'الشعراء',
      27: 'النمل',
      28: 'القصص',
      29: 'العنكبوت',
      30: 'الروم',
      31: 'لقمان',
      32: 'السجدة',
      33: 'الأحزاب',
      34: 'سبأ',
      35: 'فاطر',
      36: 'يس',
      37: 'الصافات',
      38: 'ص',
      39: 'الزمر',
      40: 'غافر',
      41: 'فصلت',
      42: 'الشورى',
      43: 'الزخرف',
      44: 'الدخان',
      45: 'الجاثية',
      46: 'الأحقاف',
      47: 'محمد',
      48: 'الفتح',
      49: 'الحجرات',
      50: 'ق',
      51: 'الذاريات',
      52: 'الطور',
      53: 'النجم',
      54: 'القمر',
      55: 'الرحمن',
      56: 'الواقعة',
      57: 'الحديد',
      58: 'المجادلة',
      59: 'الحشر',
      60: 'الممتحنة',
      61: 'الصف',
      62: 'الجمعة',
      63: 'المنافقون',
      64: 'التغابن',
      65: 'الطلاق',
      66: 'التحريم',
      67: 'الملك',
      68: 'القلم',
      69: 'الحاقة',
      70: 'المعارج',
      71: 'نوح',
      72: 'الجن',
      73: 'المزمل',
      74: 'المدثر',
      75: 'القيامة',
      76: 'الإنسان',
      77: 'المرسلات',
      78: 'النبأ',
      79: 'النازعات',
      80: 'عبس',
      81: 'التكوير',
      82: 'الانفطار',
      83: 'المطففين',
      84: 'الانشقاق',
      85: 'البروج',
      86: 'الطارق',
      87: 'الأعلى',
      88: 'الغاشية',
      89: 'الفجر',
      90: 'البلد',
      91: 'الشمس',
      92: 'الليل',
      93: 'الضحى',
      94: 'الشرح',
      95: 'التين',
      96: 'العلق',
      97: 'القدر',
      98: 'البينة',
      99: 'الزلزلة',
      100: 'العاديات',
      101: 'القارعة',
      102: 'التكاثر',
      103: 'العصر',
      104: 'الهمزة',
      105: 'الفيل',
      106: 'قريش',
      107: 'الماعون',
      108: 'الكوثر',
      109: 'الكافرون',
      110: 'النصر',
      111: 'المسد',
      112: 'الإخلاص',
      113: 'الفلق',
      114: 'الناس',
    };

    return surahNames[number] ?? 'سورة رقم $number';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final quranProvider = context.watch<QuranProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: [
          // زر إصلاح البيانات
          IconButton(
            icon: const Icon(Icons.build),
            onPressed: () => _fixBookmarksData(context, quranProvider),
            tooltip: 'إصلاح البيانات',
          ),
          if (quranProvider.hasBookmarks)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () => _showClearAllDialog(context, quranProvider),
              tooltip: 'مسح الكل',
            ),
        ],
      ),
      body: quranProvider.hasBookmarks
          ? ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: quranProvider.bookmarkedAyahs.length,
              itemBuilder: (context, index) {
                final ayah = quranProvider.bookmarkedAyahs[index];
                final surah = quranProvider.getSurahByNumber(ayah.surahNumber);

                // إذا لم نجد السورة، نحاول تحميلها
                if (surah == null) {
                  quranProvider.loadSurahWithAyahs(ayah.surahNumber);
                }

                return _buildBookmarkCard(
                  context,
                  ayah,
                  surah,
                  theme,
                  themeProvider,
                  quranProvider,
                );
              },
            )
          : _buildEmptyState(context, theme),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddBookmarkDialog(context, quranProvider),
        backgroundColor: Colors.amber,
        tooltip: 'إضافة مفضلة جديدة',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBookmarkCard(
    BuildContext context,
    Ayah ayah,
    Surah? surah,
    ThemeData theme,
    ThemeProvider themeProvider,
    QuranProvider quranProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          if (surah != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuranReaderScreen(surah: surah),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // أيقونة المفضلة الذهبية
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  color: Colors.amber,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.bookmark,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const SizedBox(width: AppConstants.paddingMedium),

              // معلومات السورة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      surah?.name ?? _getSurahNameByNumber(ayah.surahNumber),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.textTheme.titleMedium?.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${ayah.numberInSurah} آية',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color
                            ?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار التحكم
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.play_arrow),
                    onPressed: () {
                      if (surah != null) {
                        _playSurah(context, surah);
                      }
                    },
                    tooltip: 'تشغيل',
                    color: theme.primaryColor,
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _showRemoveBookmarkDialog(
                      context,
                      ayah,
                      quranProvider,
                    ),
                    tooltip: 'حذف',
                    color: Colors.red,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تشغيل السورة
  void _playSurah(BuildContext context, Surah surah) {
    // الانتقال إلى شاشة القراءة مع تشغيل السورة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuranReaderScreen(surah: surah),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.3),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'لا توجد آيات مفضلة',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'ابدأ بإضافة آيات للمفضلة من خلال قراءة القرآن',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to surah list
            },
            icon: const Icon(Icons.menu_book),
            label: const Text('ابدأ القراءة'),
          ),
        ],
      ),
    );
  }

  void _showRemoveBookmarkDialog(
    BuildContext context,
    Ayah ayah,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة من المفضلة'),
        content: const Text('هل تريد إزالة هذه الآية من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              quranProvider.removeBookmark(ayah);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إزالة الآية من المفضلة'),
                ),
              );
            },
            child: const Text('إزالة'),
          ),
        ],
      ),
    );
  }

  void _fixBookmarksData(BuildContext context, QuranProvider provider) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إصلاح البيانات'),
        content: const Text(
            'هل تريد إصلاح بيانات المفضلة لعرض أسماء السور الصحيحة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();

              // عرض مؤشر التحميل
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('جاري إصلاح البيانات...'),
                    ],
                  ),
                ),
              );

              await provider.fixBookmarkedAyahs();

              navigator.pop(); // إغلاق مؤشر التحميل

              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم إصلاح البيانات بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إصلاح'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(
    BuildContext context,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text(
            'هل تريد مسح جميع الآيات المفضلة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              quranProvider.clearAllBookmarks();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المفضلة'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار إضافة مفضلة جديدة
  void _showAddBookmarkDialog(
      BuildContext context, QuranProvider quranProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مفضلة جديدة'),
        content: const Text(
            'لإضافة آية للمفضلة، انتقل إلى شاشة قراءة القرآن واضغط على أيقونة المفضلة بجانب الآية المطلوبة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
