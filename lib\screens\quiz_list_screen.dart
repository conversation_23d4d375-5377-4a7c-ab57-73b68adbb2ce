import 'package:flutter/material.dart';
import '../models/quiz.dart';
import '../services/quiz_service.dart';
import '../constants/app_constants.dart';
import 'quiz_screen.dart';

class QuizListScreen extends StatefulWidget {
  const QuizListScreen({super.key});

  @override
  State<QuizListScreen> createState() => _QuizListScreenState();
}

class _QuizListScreenState extends State<QuizListScreen>
    with TickerProviderStateMixin {
  final QuizService _quizService = QuizService();

  List<Quiz> _quizzes = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;

  late TabController _tabController;
  final String _userId = 'default_user'; // In a real app, get from auth

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final quizzes = await _quizService.getAvailableQuizzes();
      final statistics = await _quizService.getQuizStatistics(_userId);

      setState(() {
        _quizzes = quizzes;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الاختبارات التفاعلية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.quiz), text: 'الاختبارات'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.auto_awesome), text: 'مخصص'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildQuizzesTab(theme),
                _buildStatisticsTab(theme),
                _buildPersonalizedTab(theme),
              ],
            ),
    );
  }

  Widget _buildQuizzesTab(ThemeData theme) {
    if (_quizzes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.quiz, size: 64, color: theme.hintColor),
            const SizedBox(height: 16),
            Text('لا توجد اختبارات متاحة', style: theme.textTheme.titleMedium),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _quizzes.length,
        itemBuilder: (context, index) {
          final quiz = _quizzes[index];
          return _buildQuizCard(quiz, theme);
        },
      ),
    );
  }

  Widget _buildStatisticsTab(ThemeData theme) {
    if (_statistics.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics, size: 64, color: theme.hintColor),
            const SizedBox(height: 16),
            Text('لا توجد إحصائيات متاحة', style: theme.textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(
              'ابدأ بحل الاختبارات لترى إحصائياتك',
              style: TextStyle(color: theme.hintColor),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          _buildStatCard(
            'إجمالي المحاولات',
            _statistics['total_attempts']?.toString() ?? '0',
            Icons.quiz,
            Colors.blue,
            theme,
          ),
          _buildStatCard(
            'المحاولات المكتملة',
            _statistics['completed_attempts']?.toString() ?? '0',
            Icons.check_circle,
            Colors.green,
            theme,
          ),
          _buildStatCard(
            'المحاولات الناجحة',
            _statistics['passed_attempts']?.toString() ?? '0',
            Icons.star,
            Colors.orange,
            theme,
          ),
          _buildStatCard(
            'متوسط النتيجة',
            '${(_statistics['average_score'] ?? 0.0).toStringAsFixed(1)}%',
            Icons.trending_up,
            Colors.purple,
            theme,
          ),
          _buildStatCard(
            'أفضل نتيجة',
            '${(_statistics['best_score'] ?? 0.0).toStringAsFixed(1)}%',
            Icons.emoji_events,
            Colors.amber,
            theme,
          ),
          _buildStatCard(
            'الوقت المستغرق',
            '${_statistics['total_time_spent'] ?? 0} دقيقة',
            Icons.access_time,
            Colors.teal,
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalizedTab(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إنشاء اختبار مخصص',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Text(
            'اختر نوع الاختبار والصعوبة لإنشاء اختبار مخصص بناءً على تفضيلاتك',
            style: theme.textTheme.bodyMedium?.copyWith(color: theme.hintColor),
          ),
          const SizedBox(height: AppConstants.paddingLarge),

          // Quiz type selection
          _buildPersonalizedOption(
            'حفظ',
            'اختبر حفظك للآيات والسور',
            Icons.memory,
            Colors.blue,
            () => _createPersonalizedQuiz(QuizType.memorization),
            theme,
          ),

          _buildPersonalizedOption(
            'فهم',
            'اختبر فهمك لمعاني القرآن',
            Icons.psychology,
            Colors.green,
            () => _createPersonalizedQuiz(QuizType.comprehension),
            theme,
          ),

          _buildPersonalizedOption(
            'تفسير',
            'اختبر معرفتك بالتفسير',
            Icons.book,
            Colors.orange,
            () => _createPersonalizedQuiz(QuizType.tafsir),
            theme,
          ),

          _buildPersonalizedOption(
            'عام',
            'معلومات عامة عن القرآن',
            Icons.quiz,
            Colors.purple,
            () => _createPersonalizedQuiz(QuizType.general),
            theme,
          ),
        ],
      ),
    );
  }

  Widget _buildQuizCard(Quiz quiz, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _getQuizTypeIcon(quiz.type, theme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        quiz.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildQuizChip(
                            quiz.typeDisplayName,
                            theme.primaryColor,
                            theme,
                          ),
                          const SizedBox(width: 8),
                          _buildQuizChip(
                            quiz.difficultyDisplayName,
                            Colors.orange,
                            theme,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Text(
              quiz.description,
              style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Icon(Icons.quiz, size: 16, color: theme.hintColor),
                const SizedBox(width: 4),
                Text(
                  '${quiz.questionCount} سؤال',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.timer, size: 16, color: theme.hintColor),
                const SizedBox(width: 4),
                Text(
                  '${quiz.timeLimit} دقيقة',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.star, size: 16, color: theme.hintColor),
                const SizedBox(width: 4),
                Text(
                  '${quiz.passingScore}% للنجاح',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _startQuiz(quiz),
                icon: const Icon(Icons.play_arrow),
                label: const Text('بدء الاختبار'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalizedOption(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
    ThemeData theme,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: theme.hintColor, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getQuizTypeIcon(QuizType type, ThemeData theme) {
    IconData iconData;
    Color color;

    switch (type) {
      case QuizType.memorization:
        iconData = Icons.memory;
        color = Colors.blue;
        break;
      case QuizType.comprehension:
        iconData = Icons.psychology;
        color = Colors.green;
        break;
      case QuizType.tafsir:
        iconData = Icons.book;
        color = Colors.orange;
        break;
      case QuizType.general:
        iconData = Icons.quiz;
        color = Colors.purple;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(iconData, color: color, size: 20),
    );
  }

  Widget _buildQuizChip(String label, Color color, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  void _startQuiz(Quiz quiz) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuizScreen(quiz: quiz, userId: _userId),
      ),
    ).then((_) => _loadData()); // Refresh data when returning
  }

  Future<void> _createPersonalizedQuiz(QuizType type) async {
    try {
      final personalizedQuiz = await _quizService.generatePersonalizedQuiz(
        _userId,
        preferredType: type,
        questionCount: 5,
      );

      _startQuiz(personalizedQuiz);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إنشاء الاختبار المخصص: $e')),
        );
      }
    }
  }
}
