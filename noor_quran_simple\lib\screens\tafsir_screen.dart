import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/library_provider.dart';
import '../models/library_models.dart';
import '../services/tafsir_api_service.dart';

/// شاشة التفاسير
class TafsirScreen extends StatefulWidget {
  const TafsirScreen({super.key});

  @override
  State<TafsirScreen> createState() => _TafsirScreenState();
}

class _TafsirScreenState extends State<TafsirScreen> {
  final TextEditingController _surahController = TextEditingController();
  final TextEditingController _verseController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LibraryProvider>().loadAvailableTafsirs();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التفاسير'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: Consumer<LibraryProvider>(
        builder: (context, libraryProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTafsirSelector(libraryProvider),
                const SizedBox(height: 24),
                _buildVerseInput(libraryProvider),
                const SizedBox(height: 24),
                _buildCurrentTafsirs(libraryProvider),
                const SizedBox(height: 24),
                _buildQuickAccess(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTafsirSelector(LibraryProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر التفسير',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (provider.availableTafsirs.isEmpty)
              const Text('جاري تحميل التفاسير...')
            else
              DropdownButtonFormField<String>(
                value: provider.selectedTafsirId,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                items: provider.availableTafsirs.map((tafsir) {
                  return DropdownMenuItem<String>(
                    value: tafsir.id,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tafsir.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          tafsir.author,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    provider.setSelectedTafsir(value);
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerseInput(LibraryProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر الآية للتفسير',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _surahController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'رقم السورة (1-114)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _verseController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'رقم الآية',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: provider.isLoading ? null : () => _getTafsir(provider),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: provider.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('جلب التفسير'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentTafsirs(LibraryProvider provider) {
    if (provider.currentTafsirs.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(
                Icons.description,
                size: 64,
                color: AppConstants.textSecondaryColor,
              ),
              const SizedBox(height: 16),
              const Text(
                'لم يتم اختيار آية للتفسير بعد',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'اختر رقم السورة والآية أعلاه للحصول على التفسير',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التفسير',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...provider.currentTafsirs.map((tafsir) => _buildTafsirCard(tafsir)),
      ],
    );
  }

  Widget _buildTafsirCard(VerseWithTafsir tafsir) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${tafsir.tafsirName} - سورة ${tafsir.surahNumber} آية ${tafsir.verseNumber}',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Text(
                tafsir.verseText,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  height: 2.0,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'التفسير:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              tafsir.tafsirText,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                height: 1.8,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAccess() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'وصول سريع',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessCard(
                'الفاتحة',
                'سورة 1',
                () => _getQuickTafsir(1, 1),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildQuickAccessCard(
                'آية الكرسي',
                'البقرة 255',
                () => _getQuickTafsir(2, 255),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessCard(
                'الإخلاص',
                'سورة 112',
                () => _getQuickTafsir(112, 1),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildQuickAccessCard(
                'تفسير عشوائي',
                'آية عشوائية',
                () => _getRandomTafsir(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard(String title, String subtitle, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              const Icon(
                Icons.flash_on,
                color: AppConstants.accentColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _getTafsir(LibraryProvider provider) {
    final surahText = _surahController.text.trim();
    final verseText = _verseController.text.trim();

    if (surahText.isEmpty || verseText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رقم السورة والآية'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final surahNumber = int.tryParse(surahText);
    final verseNumber = int.tryParse(verseText);

    if (surahNumber == null || verseNumber == null ||
        surahNumber < 1 || surahNumber > 114 || verseNumber < 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال أرقام صحيحة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    provider.getVerseTafsir(surahNumber, verseNumber);
  }

  void _getQuickTafsir(int surah, int verse) {
    _surahController.text = surah.toString();
    _verseController.text = verse.toString();
    context.read<LibraryProvider>().getVerseTafsir(surah, verse);
  }

  void _getRandomTafsir() {
    final provider = context.read<LibraryProvider>();
    TafsirApiService.getRandomVerseTafsir(provider.selectedTafsirId).then((tafsir) {
      if (tafsir != null) {
        _surahController.text = tafsir.surahNumber.toString();
        _verseController.text = tafsir.verseNumber.toString();
        provider.getVerseTafsir(tafsir.surahNumber, tafsir.verseNumber);
      }
    });
  }

  @override
  void dispose() {
    _surahController.dispose();
    _verseController.dispose();
    super.dispose();
  }
}
