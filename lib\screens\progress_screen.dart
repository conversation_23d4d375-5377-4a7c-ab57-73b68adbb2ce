import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/progress_service.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final ProgressService _progressService = ProgressService();
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await _progressService.getStatistics();
      setState(() {
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إحصائيات القراءة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadStatistics,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Overall Progress
                    _buildOverallProgressCard(theme),
                    const SizedBox(height: AppConstants.paddingLarge),

                    // Reading Streak
                    _buildReadingStreakCard(theme),
                    const SizedBox(height: AppConstants.paddingLarge),

                    // Goals Progress
                    _buildGoalsProgressCard(theme),
                    const SizedBox(height: AppConstants.paddingLarge),

                    // Detailed Statistics
                    _buildDetailedStatsCard(theme),
                    const SizedBox(height: AppConstants.paddingLarge),

                    // Actions
                    _buildActionsCard(theme),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverallProgressCard(ThemeData theme) {
    final completionPercentage = _statistics['completionPercentage'] ?? 0.0;
    final readSurahs = _statistics['readSurahs'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_stories, color: theme.primaryColor, size: 28),
                const SizedBox(width: 12),
                Text(
                  'التقدم العام',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),

            // Progress Circle
            Center(
              child: SizedBox(
                width: 120,
                height: 120,
                child: Stack(
                  children: [
                    CircularProgressIndicator(
                      value: completionPercentage / 100,
                      strokeWidth: 8,
                      backgroundColor: theme.dividerColor.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.primaryColor,
                      ),
                    ),
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${completionPercentage.toStringAsFixed(1)}%',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.primaryColor,
                            ),
                          ),
                          Text('مكتمل', style: theme.textTheme.bodySmall),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            Text(
              'قرأت $readSurahs سورة من أصل 114 سورة',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingStreakCard(ThemeData theme) {
    final streak = _statistics['readingStreak'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.2),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.local_fire_department,
                color: Colors.orange,
                size: 32,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سلسلة القراءة',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$streak ${streak == 1 ? 'يوم' : 'أيام'} متتالية',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'استمر في القراءة يومياً!',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsProgressCard(ThemeData theme) {
    final dailyProgress = _statistics['dailyProgress'] ?? 0.0;
    final weeklyProgress = _statistics['weeklyProgress'] ?? 0.0;
    final monthlyProgress = _statistics['monthlyProgress'] ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقدم الأهداف',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildProgressItem(
              'الهدف اليومي',
              dailyProgress,
              Icons.today,
              theme,
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildProgressItem(
              'الهدف الأسبوعي',
              weeklyProgress,
              Icons.date_range,
              theme,
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            _buildProgressItem(
              'الهدف الشهري',
              monthlyProgress,
              Icons.calendar_month,
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(
    String title,
    double progress,
    IconData icon,
    ThemeData theme,
  ) {
    return Row(
      children: [
        Icon(icon, size: 20, color: theme.primaryColor),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(title, style: theme.textTheme.bodyMedium),
                  Text(
                    '${(progress * 100).toInt()}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: progress.clamp(0.0, 1.0),
                backgroundColor: theme.dividerColor.withOpacity(0.3),
                valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedStatsCard(ThemeData theme) {
    final readAyahs = _statistics['readAyahs'] ?? 0;
    final totalTime = _statistics['totalReadingTime'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات مفصلة',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الآيات المقروءة',
                    readAyahs.toString(),
                    Icons.format_list_numbered,
                    theme,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'وقت القراءة',
                    '$totalTime دقيقة',
                    Icons.access_time,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Icon(icon, color: theme.primaryColor, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.primaryColor,
          ),
        ),
        Text(
          title,
          style: theme.textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('تعديل الأهداف'),
              subtitle: const Text('تخصيص أهدافك اليومية والأسبوعية'),
              onTap: () => _showGoalsDialog(),
            ),

            ListTile(
              leading: const Icon(Icons.refresh, color: Colors.orange),
              title: const Text('إعادة تعيين التقدم'),
              subtitle: const Text('مسح جميع إحصائيات القراءة'),
              onTap: () => _showResetDialog(),
            ),
          ],
        ),
      ),
    );
  }

  void _showGoalsDialog() {
    // TODO: Implement goals setting dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة هذه الميزة قريباً')),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين التقدم'),
        content: const Text(
          'هل أنت متأكد من رغبتك في مسح جميع إحصائيات القراءة؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await _progressService.resetProgress();
              Navigator.pop(context);
              _loadStatistics();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إعادة تعيين التقدم')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
}
