import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/app_state_manager.dart';
import '../models/notification_data.dart';

/// خدمة الإشعارات الذكية - لمسة خاصة لتحفيز المستخدم
class SmartNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = 
      FlutterLocalNotificationsPlugin();
  
  static bool _isInitialized = false;
  static const String _channelId = 'noor_quran_smart';
  static const String _channelName = 'إشعارات نور القرآن الذكية';
  static const String _channelDescription = 'إشعارات ذكية لتحفيز القراءة والتذكير';

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    await _createNotificationChannel();
    _isInitialized = true;
    
    debugPrint('✅ تم تهيئة خدمة الإشعارات الذكية');
  }

  /// إنشاء قناة الإشعارات
  static Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: _channelDescription,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification_sound'),
    );

    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// معالج النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');
    // سيتم تطبيق التنقل المناسب حسب نوع الإشعار
  }

  /// جدولة إشعارات ذكية بناءً على سلوك المستخدم
  static Future<void> scheduleSmartNotifications() async {
    if (!_isInitialized) await initialize();

    final appState = AppStateManager();
    if (!appState.smartNotificationsEnabled) return;

    // إلغاء الإشعارات السابقة
    await _notifications.cancelAll();

    // جدولة إشعارات مختلفة بناءً على الإحصائيات
    await _scheduleReadingReminders(appState);
    await _scheduleMotivationalNotifications(appState);
    await _scheduleStreakReminders(appState);
    await _scheduleWeeklyReports();
  }

  /// جدولة تذكيرات القراءة
  static Future<void> _scheduleReadingReminders(AppStateManager appState) async {
    final stats = appState.getQuickStats();
    final streak = stats['dailyStreak'] as int;
    
    // تحديد أوقات التذكير بناءً على السلوك
    List<int> reminderHours;
    if (streak >= 7) {
      // مستخدم منتظم - تذكيرات أقل
      reminderHours = [9, 15, 20];
    } else if (streak >= 3) {
      // مستخدم متوسط - تذكيرات معتدلة
      reminderHours = [8, 13, 17, 21];
    } else {
      // مستخدم جديد - تذكيرات أكثر
      reminderHours = [7, 11, 15, 18, 22];
    }

    for (int i = 0; i < reminderHours.length; i++) {
      await _scheduleNotification(
        id: 100 + i,
        title: _getReadingReminderTitle(streak),
        body: _getReadingReminderBody(streak),
        scheduledTime: _getNextScheduledTime(reminderHours[i]),
        payload: 'reading_reminder',
      );
    }
  }

  /// جدولة إشعارات تحفيزية
  static Future<void> _scheduleMotivationalNotifications(AppStateManager appState) async {
    final stats = appState.getQuickStats();
    final totalAyahs = stats['totalAyahs'] as int;
    
    // إشعارات تحفيزية أسبوعية
    await _scheduleNotification(
      id: 200,
      title: '🌟 إنجاز رائع!',
      body: 'لقد قرأت $totalAyahs آية هذا الأسبوع. استمر في التقدم!',
      scheduledTime: _getNextWeeklyTime(DateTime.sunday, 19),
      payload: 'motivational',
    );

    // إشعار تحدي يومي
    await _scheduleNotification(
      id: 201,
      title: '💪 تحدي اليوم',
      body: 'هل يمكنك قراءة 10 آيات إضافية اليوم؟',
      scheduledTime: _getNextScheduledTime(16),
      payload: 'daily_challenge',
    );
  }

  /// جدولة تذكيرات السلسلة
  static Future<void> _scheduleStreakReminders(AppStateManager appState) async {
    final streak = appState.dailyReadingStreak;
    
    if (streak >= 3) {
      await _scheduleNotification(
        id: 300,
        title: '🔥 لا تكسر السلسلة!',
        body: 'لديك سلسلة $streak أيام. حافظ عليها بقراءة اليوم!',
        scheduledTime: _getNextScheduledTime(21),
        payload: 'streak_reminder',
      );
    }
  }

  /// جدولة التقارير الأسبوعية
  static Future<void> _scheduleWeeklyReports() async {
    await _scheduleNotification(
      id: 400,
      title: '📊 تقرير الأسبوع',
      body: 'اطلع على إنجازاتك وتقدمك في القراءة هذا الأسبوع',
      scheduledTime: _getNextWeeklyTime(DateTime.friday, 18),
      payload: 'weekly_report',
    );
  }

  /// جدولة إشعار محدد
  static Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required tz.TZDateTime scheduledTime,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFF2196F3),
      enableVibration: true,
      playSound: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      scheduledTime,
      details,
      uiLocalNotificationDateInterpretation: 
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
    );
  }

  /// الحصول على الوقت المجدول التالي
  static tz.TZDateTime _getNextScheduledTime(int hour) {
    final now = tz.TZDateTime.now(tz.local);
    var scheduledTime = tz.TZDateTime(tz.local, now.year, now.month, now.day, hour);
    
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }
    
    return scheduledTime;
  }

  /// الحصول على الوقت الأسبوعي التالي
  static tz.TZDateTime _getNextWeeklyTime(int weekday, int hour) {
    final now = tz.TZDateTime.now(tz.local);
    var scheduledTime = tz.TZDateTime(tz.local, now.year, now.month, now.day, hour);
    
    // العثور على اليوم المطلوب في الأسبوع
    while (scheduledTime.weekday != weekday) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }
    
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 7));
    }
    
    return scheduledTime;
  }

  /// الحصول على عنوان تذكير القراءة
  static String _getReadingReminderTitle(int streak) {
    if (streak >= 30) return '👑 قارئ متميز';
    if (streak >= 14) return '🌟 قارئ منتظم';
    if (streak >= 7) return '📖 وقت القراءة';
    if (streak >= 3) return '⏰ تذكير القراءة';
    return '🕌 وقت مع القرآن';
  }

  /// الحصول على نص تذكير القراءة
  static String _getReadingReminderBody(int streak) {
    final messages = [
      'ابدأ يومك بآيات من القرآن الكريم',
      'خذ استراحة واقرأ بعض الآيات',
      'اختتم يومك بتلاوة هادئة',
      'القرآن يجلب السكينة للقلب',
      'كل آية تقرأها نور في حياتك',
    ];
    
    return messages[DateTime.now().day % messages.length];
  }

  /// إرسال إشعار فوري
  static Future<void> showInstantNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();
    const details = NotificationDetails(android: androidDetails, iOS: iosDetails);

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      details,
      payload: payload,
    );
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// التحقق من صلاحيات الإشعارات
  static Future<bool> checkPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      final androidImplementation = _notifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    }
    return true;
  }

  /// طلب صلاحيات الإشعارات
  static Future<bool> requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      final androidImplementation = _notifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.requestNotificationsPermission() ?? false;
    }
    return true;
  }
}
