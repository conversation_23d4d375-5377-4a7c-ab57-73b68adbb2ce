import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/audio_provider.dart';
import '../providers/theme_provider.dart';
import '../models/surah.dart';

class AudioPlayerWidget extends StatelessWidget {
  final Surah? surah;
  final bool isExpanded;
  final VoidCallback? onToggleExpanded;

  const AudioPlayerWidget({
    super.key,
    this.surah,
    this.isExpanded = false,
    this.onToggleExpanded,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final audioProvider = context.watch<AudioProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    if (audioProvider.currentSurah == null && surah == null) {
      return const SizedBox.shrink();
    }

    final currentSurah = audioProvider.currentSurah ?? surah!;

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main Player Controls
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                // Play/Pause Button
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: themeProvider.getThemeColor(context),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () {
                      if (audioProvider.isPlaying) {
                        audioProvider.pause();
                      } else if (audioProvider.currentSurah != null) {
                        audioProvider.play();
                      } else {
                        audioProvider.playSurah(currentSurah);
                      }
                    },
                    icon: Icon(
                      audioProvider.isLoading
                          ? Icons.hourglass_empty
                          : audioProvider.isPlaying
                              ? Icons.pause
                              : Icons.play_arrow,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                
                // Surah Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentSurah.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        audioProvider.selectedReciter?.name ?? 'لم يتم اختيار قارئ',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Additional Controls
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Repeat Mode
                    IconButton(
                      onPressed: audioProvider.toggleRepeatMode,
                      icon: Icon(
                        audioProvider.repeatModeIcon,
                        color: audioProvider.repeatMode != RepeatMode.none
                            ? themeProvider.getThemeColor(context)
                            : theme.iconTheme.color?.withOpacity(0.5),
                      ),
                      tooltip: audioProvider.repeatModeDisplayName,
                    ),
                    
                    // Expand/Collapse
                    if (onToggleExpanded != null)
                      IconButton(
                        onPressed: onToggleExpanded,
                        icon: Icon(
                          isExpanded
                              ? Icons.keyboard_arrow_down
                              : Icons.keyboard_arrow_up,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          // Progress Bar
          if (audioProvider.currentSurah != null)
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
              ),
              child: Column(
                children: [
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      trackHeight: 2,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 6,
                      ),
                      overlayShape: const RoundSliderOverlayShape(
                        overlayRadius: 12,
                      ),
                    ),
                    child: Slider(
                      value: audioProvider.progress.clamp(0.0, 1.0),
                      onChanged: (value) {
                        final position = Duration(
                          milliseconds: (value * audioProvider.duration.inMilliseconds).round(),
                        );
                        audioProvider.seek(position);
                      },
                      activeColor: themeProvider.getThemeColor(context),
                      inactiveColor: theme.dividerColor,
                    ),
                  ),
                  
                  // Time Display
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          audioProvider.formatDuration(audioProvider.position),
                          style: theme.textTheme.bodySmall,
                        ),
                        Text(
                          audioProvider.formatDuration(audioProvider.duration),
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          
          // Expanded Controls
          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                children: [
                  // Playback Speed
                  Row(
                    children: [
                      Icon(
                        Icons.speed,
                        size: 20,
                        color: theme.iconTheme.color?.withOpacity(0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'السرعة',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const Spacer(),
                      Text(
                        '${audioProvider.playbackSpeed}x',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          final newSpeed = (audioProvider.playbackSpeed - 0.25)
                              .clamp(AppConstants.minPlaybackSpeed, AppConstants.maxPlaybackSpeed);
                          audioProvider.setPlaybackSpeed(newSpeed);
                        },
                        icon: const Icon(Icons.remove),
                        iconSize: 20,
                      ),
                      IconButton(
                        onPressed: () {
                          final newSpeed = (audioProvider.playbackSpeed + 0.25)
                              .clamp(AppConstants.minPlaybackSpeed, AppConstants.maxPlaybackSpeed);
                          audioProvider.setPlaybackSpeed(newSpeed);
                        },
                        icon: const Icon(Icons.add),
                        iconSize: 20,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingSmall),
                  
                  // Auto Play Toggle
                  Row(
                    children: [
                      Icon(
                        Icons.autorenew,
                        size: 20,
                        color: theme.iconTheme.color?.withOpacity(0.7),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'التشغيل التلقائي',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const Spacer(),
                      Switch(
                        value: audioProvider.isAutoPlay,
                        onChanged: audioProvider.setAutoPlay,
                        activeColor: themeProvider.getThemeColor(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
          
          // Error Message
          if (audioProvider.hasError)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              color: Colors.red.withOpacity(0.1),
              child: Text(
                audioProvider.errorMessage ?? 'حدث خطأ غير معروف',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}
