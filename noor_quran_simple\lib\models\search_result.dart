class SearchResult {
  final int surahNumber;
  final String surahName;
  final int ayahNumber;
  final String ayahText;
  final String highlightedText;

  SearchResult({
    required this.surahNumber,
    required this.surahName,
    required this.ayahNumber,
    required this.ayahText,
    required this.highlightedText,
  });

  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'surahName': surahName,
      'ayahNumber': ayahNumber,
      'ayahText': ayahText,
      'highlightedText': highlightedText,
    };
  }

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      surahNumber: json['surahNumber'] ?? 0,
      surahName: json['surahName'] ?? '',
      ayahNumber: json['ayahNumber'] ?? 0,
      ayahText: json['ayahText'] ?? '',
      highlightedText: json['highlightedText'] ?? '',
    );
  }

  @override
  String toString() {
    return 'SearchResult{surahNumber: $surahNumber, surahName: $surahName, ayahNumber: $ayahNumber}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchResult &&
          runtimeType == other.runtimeType &&
          surahNumber == other.surahNumber &&
          ayahNumber == other.ayahNumber;

  @override
  int get hashCode => surahNumber.hashCode ^ ayahNumber.hashCode;
}
