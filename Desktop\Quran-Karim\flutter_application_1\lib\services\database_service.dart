import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/surah.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static Database? _database;
  static final DatabaseService _instance = DatabaseService._internal();
  
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create surahs table
    await db.execute('''
      CREATE TABLE surahs (
        number INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        englishName TEXT NOT NULL,
        englishNameTranslation TEXT NOT NULL,
        numberOfAyahs INTEGER NOT NULL,
        revelationType TEXT NOT NULL,
        juz INTEGER NOT NULL,
        hizb INTEGER NOT NULL,
        rukus INTEGER NOT NULL
      )
    ''');

    // Create ayahs table
    await db.execute('''
      CREATE TABLE ayahs (
        number INTEGER PRIMARY KEY,
        numberInSurah INTEGER NOT NULL,
        text TEXT NOT NULL,
        surahNumber INTEGER NOT NULL,
        juz INTEGER NOT NULL,
        manzil INTEGER NOT NULL,
        page INTEGER NOT NULL,
        ruku INTEGER NOT NULL,
        hizbQuarter INTEGER NOT NULL,
        sajda INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (surahNumber) REFERENCES surahs (number)
      )
    ''');

    // Create bookmarks table
    await db.execute('''
      CREATE TABLE bookmarks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ayahNumber INTEGER NOT NULL,
        surahNumber INTEGER NOT NULL,
        createdAt TEXT NOT NULL,
        UNIQUE(ayahNumber, surahNumber)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_ayahs_surah ON ayahs (surahNumber)');
    await db.execute('CREATE INDEX idx_ayahs_juz ON ayahs (juz)');
    await db.execute('CREATE INDEX idx_ayahs_page ON ayahs (page)');
    await db.execute('CREATE INDEX idx_ayahs_text ON ayahs (text)');
    await db.execute('CREATE INDEX idx_bookmarks_ayah ON bookmarks (ayahNumber)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add any new columns or tables for version 2
    }
  }

  // Surah operations
  Future<void> insertSurahs(List<Surah> surahs) async {
    final db = await database;
    final batch = db.batch();

    for (final surah in surahs) {
      batch.insert(
        'surahs',
        surah.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<void> insertSurahWithAyahs(Surah surah) async {
    final db = await database;
    final batch = db.batch();

    // Insert surah
    batch.insert(
      'surahs',
      surah.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert ayahs
    for (final ayah in surah.ayahs) {
      batch.insert(
        'ayahs',
        ayah.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<List<Surah>> getAllSurahs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'surahs',
      orderBy: 'number ASC',
    );

    return List.generate(maps.length, (i) {
      return Surah.fromMap(maps[i]);
    });
  }

  Future<Surah?> getSurahByNumber(int number) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'surahs',
      where: 'number = ?',
      whereArgs: [number],
    );

    if (maps.isNotEmpty) {
      return Surah.fromMap(maps.first);
    }
    return null;
  }

  Future<Surah?> getSurahWithAyahs(int surahNumber) async {
    final db = await database;
    
    // Get surah
    final surahMaps = await db.query(
      'surahs',
      where: 'number = ?',
      whereArgs: [surahNumber],
    );

    if (surahMaps.isEmpty) return null;

    // Get ayahs
    final ayahMaps = await db.query(
      'ayahs',
      where: 'surahNumber = ?',
      whereArgs: [surahNumber],
      orderBy: 'numberInSurah ASC',
    );

    final ayahs = ayahMaps.map((map) => Ayah.fromMap(map)).toList();
    final surah = Surah.fromMap(surahMaps.first);

    return Surah(
      number: surah.number,
      name: surah.name,
      englishName: surah.englishName,
      englishNameTranslation: surah.englishNameTranslation,
      numberOfAyahs: surah.numberOfAyahs,
      revelationType: surah.revelationType,
      juz: surah.juz,
      hizb: surah.hizb,
      rukus: surah.rukus,
      ayahs: ayahs,
    );
  }

  // Ayah operations
  Future<List<Ayah>> searchAyahs(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ayahs',
      where: 'text LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'number ASC',
      limit: 50, // Limit results for performance
    );

    return List.generate(maps.length, (i) {
      return Ayah.fromMap(maps[i]);
    });
  }

  Future<Ayah?> getAyahByNumber(int number) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ayahs',
      where: 'number = ?',
      whereArgs: [number],
    );

    if (maps.isNotEmpty) {
      return Ayah.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Ayah>> getAyahsByJuz(int juzNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ayahs',
      where: 'juz = ?',
      whereArgs: [juzNumber],
      orderBy: 'number ASC',
    );

    return List.generate(maps.length, (i) {
      return Ayah.fromMap(maps[i]);
    });
  }

  Future<List<Ayah>> getAyahsByPage(int pageNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ayahs',
      where: 'page = ?',
      whereArgs: [pageNumber],
      orderBy: 'number ASC',
    );

    return List.generate(maps.length, (i) {
      return Ayah.fromMap(maps[i]);
    });
  }

  // Bookmark operations
  Future<void> saveBookmarkedAyahs(List<Ayah> ayahs) async {
    final db = await database;
    
    // Clear existing bookmarks
    await db.delete('bookmarks');
    
    // Insert new bookmarks
    final batch = db.batch();
    for (final ayah in ayahs) {
      batch.insert('bookmarks', {
        'ayahNumber': ayah.number,
        'surahNumber': ayah.surahNumber,
        'createdAt': DateTime.now().toIso8601String(),
      });
    }
    
    await batch.commit();
  }

  Future<List<Ayah>> getBookmarkedAyahs() async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT a.* FROM ayahs a
      INNER JOIN bookmarks b ON a.number = b.ayahNumber
      ORDER BY b.createdAt DESC
    ''');

    return List.generate(maps.length, (i) {
      return Ayah.fromMap(maps[i]);
    });
  }

  Future<void> addBookmark(Ayah ayah) async {
    final db = await database;
    await db.insert(
      'bookmarks',
      {
        'ayahNumber': ayah.number,
        'surahNumber': ayah.surahNumber,
        'createdAt': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> removeBookmark(Ayah ayah) async {
    final db = await database;
    await db.delete(
      'bookmarks',
      where: 'ayahNumber = ? AND surahNumber = ?',
      whereArgs: [ayah.number, ayah.surahNumber],
    );
  }

  Future<bool> isAyahBookmarked(Ayah ayah) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookmarks',
      where: 'ayahNumber = ? AND surahNumber = ?',
      whereArgs: [ayah.number, ayah.surahNumber],
    );

    return maps.isNotEmpty;
  }

  // Utility methods
  Future<void> initializeDatabase() async {
    await database; // This will create the database if it doesn't exist
  }

  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('bookmarks');
    await db.delete('ayahs');
    await db.delete('surahs');
  }

  Future<void> closeDatabase() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  // Statistics methods
  Future<Map<String, int>> getDatabaseStats() async {
    final db = await database;
    
    final surahCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM surahs'),
    ) ?? 0;
    
    final ayahCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM ayahs'),
    ) ?? 0;
    
    final bookmarkCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM bookmarks'),
    ) ?? 0;

    return {
      'surahs': surahCount,
      'ayahs': ayahCount,
      'bookmarks': bookmarkCount,
    };
  }
}
