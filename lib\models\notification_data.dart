import 'dart:convert';

/// نموذج بيانات الإشعار
class NotificationData {
  final int id;
  final String title;
  final String body;
  final String type;
  final DateTime scheduledTime;
  final Map<String, dynamic> payload;
  final bool isActive;
  final int priority;

  const NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.scheduledTime,
    this.payload = const {},
    this.isActive = true,
    this.priority = 1,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'scheduledTime': scheduledTime.toIso8601String(),
      'payload': payload,
      'isActive': isActive,
      'priority': priority,
    };
  }

  /// إنشاء من Map
  factory NotificationData.fromMap(Map<String, dynamic> map) {
    return NotificationData(
      id: map['id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: map['type'] ?? '',
      scheduledTime: DateTime.parse(map['scheduledTime']),
      payload: Map<String, dynamic>.from(map['payload'] ?? {}),
      isActive: map['isActive'] ?? true,
      priority: map['priority']?.toInt() ?? 1,
    );
  }

  /// تحويل إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء من JSON
  factory NotificationData.fromJson(String source) => 
      NotificationData.fromMap(json.decode(source));

  /// نسخ مع تعديل
  NotificationData copyWith({
    int? id,
    String? title,
    String? body,
    String? type,
    DateTime? scheduledTime,
    Map<String, dynamic>? payload,
    bool? isActive,
    int? priority,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      payload: payload ?? this.payload,
      isActive: isActive ?? this.isActive,
      priority: priority ?? this.priority,
    );
  }

  @override
  String toString() {
    return 'NotificationData(id: $id, title: $title, type: $type, scheduledTime: $scheduledTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is NotificationData &&
      other.id == id &&
      other.title == title &&
      other.body == body &&
      other.type == type &&
      other.scheduledTime == scheduledTime;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      title.hashCode ^
      body.hashCode ^
      type.hashCode ^
      scheduledTime.hashCode;
  }
}

/// أنواع الإشعارات
enum NotificationType {
  readingReminder('reading_reminder', 'تذكير القراءة'),
  motivational('motivational', 'تحفيزي'),
  streakReminder('streak_reminder', 'تذكير السلسلة'),
  weeklyReport('weekly_report', 'تقرير أسبوعي'),
  dailyChallenge('daily_challenge', 'تحدي يومي'),
  achievement('achievement', 'إنجاز'),
  newFeature('new_feature', 'ميزة جديدة');

  const NotificationType(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// قالب الإشعار
class NotificationTemplate {
  final NotificationType type;
  final String titleTemplate;
  final String bodyTemplate;
  final Map<String, dynamic> defaultPayload;
  final int defaultPriority;

  const NotificationTemplate({
    required this.type,
    required this.titleTemplate,
    required this.bodyTemplate,
    this.defaultPayload = const {},
    this.defaultPriority = 1,
  });

  /// إنشاء إشعار من القالب
  NotificationData createNotification({
    required int id,
    required DateTime scheduledTime,
    Map<String, String>? variables,
    Map<String, dynamic>? additionalPayload,
    bool? isActive,
    int? priority,
  }) {
    String title = titleTemplate;
    String body = bodyTemplate;

    // استبدال المتغيرات
    if (variables != null) {
      variables.forEach((key, value) {
        title = title.replaceAll('{$key}', value);
        body = body.replaceAll('{$key}', value);
      });
    }

    return NotificationData(
      id: id,
      title: title,
      body: body,
      type: type.value,
      scheduledTime: scheduledTime,
      payload: {
        ...defaultPayload,
        ...?additionalPayload,
      },
      isActive: isActive ?? true,
      priority: priority ?? defaultPriority,
    );
  }
}

/// مجموعة قوالب الإشعارات
class NotificationTemplates {
  static const readingReminder = NotificationTemplate(
    type: NotificationType.readingReminder,
    titleTemplate: '📖 وقت القراءة',
    bodyTemplate: 'حان وقت قراءة القرآن الكريم. ابدأ بـ {surah_name}',
    defaultPayload: {'action': 'open_reading'},
  );

  static const motivational = NotificationTemplate(
    type: NotificationType.motivational,
    titleTemplate: '🌟 {title}',
    bodyTemplate: '{message}',
    defaultPayload: {'action': 'show_stats'},
    defaultPriority: 2,
  );

  static const streakReminder = NotificationTemplate(
    type: NotificationType.streakReminder,
    titleTemplate: '🔥 لا تكسر السلسلة!',
    bodyTemplate: 'لديك سلسلة {streak} أيام. حافظ عليها!',
    defaultPayload: {'action': 'continue_streak'},
    defaultPriority: 3,
  );

  static const weeklyReport = NotificationTemplate(
    type: NotificationType.weeklyReport,
    titleTemplate: '📊 تقرير الأسبوع',
    bodyTemplate: 'قرأت {ayahs} آية هذا الأسبوع. اطلع على تقريرك الكامل',
    defaultPayload: {'action': 'show_weekly_report'},
  );

  static const dailyChallenge = NotificationTemplate(
    type: NotificationType.dailyChallenge,
    titleTemplate: '💪 تحدي اليوم',
    bodyTemplate: '{challenge_text}',
    defaultPayload: {'action': 'start_challenge'},
    defaultPriority: 2,
  );

  static const achievement = NotificationTemplate(
    type: NotificationType.achievement,
    titleTemplate: '🏆 إنجاز جديد!',
    bodyTemplate: 'تهانينا! لقد حققت: {achievement_name}',
    defaultPayload: {'action': 'show_achievements'},
    defaultPriority: 3,
  );

  static const newFeature = NotificationTemplate(
    type: NotificationType.newFeature,
    titleTemplate: '✨ ميزة جديدة',
    bodyTemplate: 'اكتشف الميزة الجديدة: {feature_name}',
    defaultPayload: {'action': 'show_feature'},
  );

  /// الحصول على جميع القوالب
  static List<NotificationTemplate> getAllTemplates() {
    return [
      readingReminder,
      motivational,
      streakReminder,
      weeklyReport,
      dailyChallenge,
      achievement,
      newFeature,
    ];
  }

  /// الحصول على قالب بالنوع
  static NotificationTemplate? getTemplateByType(NotificationType type) {
    return getAllTemplates().where((template) => template.type == type).firstOrNull;
  }
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool enabled;
  final bool readingReminders;
  final bool motivationalMessages;
  final bool streakReminders;
  final bool weeklyReports;
  final bool achievements;
  final bool newFeatures;
  final List<int> quietHours; // ساعات الصمت
  final bool vibration;
  final bool sound;
  final String soundName;

  const NotificationSettings({
    this.enabled = true,
    this.readingReminders = true,
    this.motivationalMessages = true,
    this.streakReminders = true,
    this.weeklyReports = true,
    this.achievements = true,
    this.newFeatures = true,
    this.quietHours = const [22, 23, 0, 1, 2, 3, 4, 5, 6], // 10 مساءً - 6 صباحاً
    this.vibration = true,
    this.sound = true,
    this.soundName = 'default',
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'enabled': enabled,
      'readingReminders': readingReminders,
      'motivationalMessages': motivationalMessages,
      'streakReminders': streakReminders,
      'weeklyReports': weeklyReports,
      'achievements': achievements,
      'newFeatures': newFeatures,
      'quietHours': quietHours,
      'vibration': vibration,
      'sound': sound,
      'soundName': soundName,
    };
  }

  /// إنشاء من Map
  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      enabled: map['enabled'] ?? true,
      readingReminders: map['readingReminders'] ?? true,
      motivationalMessages: map['motivationalMessages'] ?? true,
      streakReminders: map['streakReminders'] ?? true,
      weeklyReports: map['weeklyReports'] ?? true,
      achievements: map['achievements'] ?? true,
      newFeatures: map['newFeatures'] ?? true,
      quietHours: List<int>.from(map['quietHours'] ?? [22, 23, 0, 1, 2, 3, 4, 5, 6]),
      vibration: map['vibration'] ?? true,
      sound: map['sound'] ?? true,
      soundName: map['soundName'] ?? 'default',
    );
  }

  /// نسخ مع تعديل
  NotificationSettings copyWith({
    bool? enabled,
    bool? readingReminders,
    bool? motivationalMessages,
    bool? streakReminders,
    bool? weeklyReports,
    bool? achievements,
    bool? newFeatures,
    List<int>? quietHours,
    bool? vibration,
    bool? sound,
    String? soundName,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      readingReminders: readingReminders ?? this.readingReminders,
      motivationalMessages: motivationalMessages ?? this.motivationalMessages,
      streakReminders: streakReminders ?? this.streakReminders,
      weeklyReports: weeklyReports ?? this.weeklyReports,
      achievements: achievements ?? this.achievements,
      newFeatures: newFeatures ?? this.newFeatures,
      quietHours: quietHours ?? this.quietHours,
      vibration: vibration ?? this.vibration,
      sound: sound ?? this.sound,
      soundName: soundName ?? this.soundName,
    );
  }

  /// التحقق من وقت الصمت
  bool isQuietTime(DateTime time) {
    return quietHours.contains(time.hour);
  }
}
