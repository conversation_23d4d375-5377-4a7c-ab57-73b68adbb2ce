import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz.dart';

class QuizService {
  static final QuizService _instance = QuizService._internal();
  factory QuizService() => _instance;
  QuizService._internal();

  final Random _random = Random();

  // Cache keys
  static const String _quizzesKey = 'cached_quizzes';
  static const String _attemptsKey = 'quiz_attempts';

  // Get available quizzes
  Future<List<Quiz>> getAvailableQuizzes() async {
    try {
      // For now, return sample quizzes
      final sampleQuizzes = _generateSampleQuizzes();
      await _cacheQuizzes(sampleQuizzes);
      return sampleQuizzes;
    } catch (e) {
      debugPrint('Error getting quizzes: $e');
      return await _getCachedQuizzes();
    }
  }

  // Generate sample quizzes
  List<Quiz> _generateSampleQuizzes() {
    return [
      Quiz(
        id: 'memorization_fatiha',
        title: 'حفظ سورة الفاتحة',
        description: 'اختبر حفظك لسورة الفاتحة',
        type: QuizType.memorization,
        difficulty: DifficultyLevel.beginner,
        timeLimit: 10,
        passingScore: 80,
        createdAt: DateTime.now(),
        tags: ['حفظ', 'الفاتحة'],
        questions: [
          QuizQuestion(
            id: 'q1_fatiha',
            type: QuestionType.fillInTheBlank,
            question: 'أكمل الآية: "بِسْمِ اللَّهِ الرَّحْمَٰنِ ____"',
            options: ['الرَّحِيمِ', 'الْعَظِيمِ', 'الْكَرِيمِ', 'الْحَكِيمِ'],
            correctAnswer: 'الرَّحِيمِ',
            explanation: 'هذه هي البسملة التي تبدأ بها سورة الفاتحة',
            surahNumber: 1,
            ayahNumber: 1,
            difficulty: DifficultyLevel.beginner,
            tags: ['بسملة'],
            points: 10,
          ),
          QuizQuestion(
            id: 'q2_fatiha',
            type: QuestionType.multipleChoice,
            question: 'كم عدد آيات سورة الفاتحة؟',
            options: ['6', '7', '8', '9'],
            correctAnswer: '7',
            explanation: 'سورة الفاتحة تحتوي على 7 آيات',
            surahNumber: 1,
            difficulty: DifficultyLevel.beginner,
            tags: ['عدد الآيات'],
            points: 10,
          ),
        ],
      ),
      Quiz(
        id: 'comprehension_baqarah',
        title: 'فهم سورة البقرة',
        description: 'اختبر فهمك لمعاني سورة البقرة',
        type: QuizType.comprehension,
        difficulty: DifficultyLevel.intermediate,
        timeLimit: 20,
        passingScore: 70,
        createdAt: DateTime.now(),
        tags: ['فهم', 'البقرة'],
        questions: [
          QuizQuestion(
            id: 'q1_baqarah',
            type: QuestionType.multipleChoice,
            question: 'ما هو موضوع سورة البقرة الرئيسي؟',
            options: [
              'قصص الأنبياء',
              'الأحكام والتشريعات',
              'الجنة والنار',
              'الخلق والكون',
            ],
            correctAnswer: 'الأحكام والتشريعات',
            explanation:
                'سورة البقرة تحتوي على العديد من الأحكام والتشريعات الإسلامية',
            surahNumber: 2,
            difficulty: DifficultyLevel.intermediate,
            tags: ['أحكام'],
            points: 15,
          ),
        ],
      ),
      Quiz(
        id: 'general_knowledge',
        title: 'معلومات عامة عن القرآن',
        description: 'اختبر معلوماتك العامة عن القرآن الكريم',
        type: QuizType.general,
        difficulty: DifficultyLevel.beginner,
        timeLimit: 15,
        passingScore: 60,
        createdAt: DateTime.now(),
        tags: ['عام', 'معلومات'],
        questions: [
          QuizQuestion(
            id: 'q1_general',
            type: QuestionType.multipleChoice,
            question: 'كم عدد سور القرآن الكريم؟',
            options: ['112', '113', '114', '115'],
            correctAnswer: '114',
            explanation: 'القرآن الكريم يحتوي على 114 سورة',
            surahNumber: 0,
            difficulty: DifficultyLevel.beginner,
            tags: ['عدد السور'],
            points: 10,
          ),
          QuizQuestion(
            id: 'q2_general',
            type: QuestionType.trueFalse,
            question: 'أطول سورة في القرآن هي سورة البقرة',
            options: ['صحيح', 'خطأ'],
            correctAnswer: 'صحيح',
            explanation: 'سورة البقرة هي أطول سورة في القرآن الكريم',
            surahNumber: 2,
            difficulty: DifficultyLevel.beginner,
            tags: ['أطول سورة'],
            points: 10,
          ),
        ],
      ),
    ];
  }

  // Start a quiz attempt
  Future<QuizAttempt> startQuizAttempt(String quizId, String userId) async {
    final attempt = QuizAttempt(
      id: 'attempt_${DateTime.now().millisecondsSinceEpoch}',
      quizId: quizId,
      userId: userId,
      answers: {},
      score: 0,
      totalPoints: 0,
      startedAt: DateTime.now(),
      timeSpent: Duration.zero,
      isPassed: false,
    );

    await _saveAttempt(attempt);
    return attempt;
  }

  // Submit quiz attempt
  Future<QuizAttempt> submitQuizAttempt(
    QuizAttempt attempt,
    Map<String, String> answers,
  ) async {
    try {
      final quiz = await getQuizById(attempt.quizId);
      if (quiz == null) {
        throw Exception('Quiz not found');
      }

      int score = 0;
      final totalPoints = quiz.totalPoints;

      // Calculate score
      for (final question in quiz.questions) {
        final userAnswer = answers[question.id];
        if (userAnswer != null && userAnswer == question.correctAnswer) {
          score += question.points;
        }
      }

      final percentage = totalPoints > 0 ? (score / totalPoints) * 100 : 0.0;
      final isPassed = percentage >= quiz.passingScore;

      final completedAttempt = QuizAttempt(
        id: attempt.id,
        quizId: attempt.quizId,
        userId: attempt.userId,
        answers: answers,
        score: score,
        totalPoints: totalPoints,
        startedAt: attempt.startedAt,
        completedAt: DateTime.now(),
        timeSpent: DateTime.now().difference(attempt.startedAt),
        isPassed: isPassed,
      );

      await _saveAttempt(completedAttempt);
      return completedAttempt;
    } catch (e) {
      debugPrint('Error submitting quiz attempt: $e');
      rethrow;
    }
  }

  // Get quiz by ID
  Future<Quiz?> getQuizById(String quizId) async {
    final quizzes = await getAvailableQuizzes();
    final matchingQuizzes = quizzes.where((q) => q.id == quizId);
    return matchingQuizzes.isNotEmpty ? matchingQuizzes.first : null;
  }

  // Get user's quiz attempts
  Future<List<QuizAttempt>> getUserAttempts(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attemptsJson = prefs.getString(_attemptsKey);

      if (attemptsJson != null) {
        final List<dynamic> decoded = json.decode(attemptsJson);
        final allAttempts = decoded
            .map((a) => QuizAttempt.fromJson(a))
            .toList();
        return allAttempts.where((a) => a.userId == userId).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Error getting user attempts: $e');
      return [];
    }
  }

  // Get quiz statistics for user
  Future<Map<String, dynamic>> getQuizStatistics(String userId) async {
    try {
      final attempts = await getUserAttempts(userId);
      final completedAttempts = attempts.where((a) => a.isCompleted).toList();

      if (completedAttempts.isEmpty) {
        return {
          'total_attempts': 0,
          'completed_attempts': 0,
          'passed_attempts': 0,
          'average_score': 0.0,
          'total_time_spent': 0,
          'best_score': 0.0,
        };
      }

      final passedAttempts = completedAttempts
          .where((a) => a.isPassed)
          .toList();
      final totalScore = completedAttempts.fold(
        0.0,
        (sum, a) => sum + a.percentage,
      );
      final averageScore = totalScore / completedAttempts.length;
      final bestScore = completedAttempts
          .map((a) => a.percentage)
          .reduce((a, b) => a > b ? a : b);
      final totalTimeSpent = completedAttempts.fold(
        Duration.zero,
        (sum, a) => sum + a.timeSpent,
      );

      return {
        'total_attempts': attempts.length,
        'completed_attempts': completedAttempts.length,
        'passed_attempts': passedAttempts.length,
        'average_score': averageScore,
        'total_time_spent': totalTimeSpent.inMinutes,
        'best_score': bestScore,
      };
    } catch (e) {
      debugPrint('Error getting quiz statistics: $e');
      return {};
    }
  }

  // Generate personalized quiz
  Future<Quiz> generatePersonalizedQuiz(
    String userId, {
    QuizType? preferredType,
    DifficultyLevel? preferredDifficulty,
    int questionCount = 5,
  }) async {
    try {
      final allQuizzes = await getAvailableQuizzes();
      final userAttempts = await getUserAttempts(userId);

      // Filter questions based on user preferences and performance
      final availableQuestions = <QuizQuestion>[];

      for (final quiz in allQuizzes) {
        if (preferredType != null && quiz.type != preferredType) continue;
        if (preferredDifficulty != null &&
            quiz.difficulty != preferredDifficulty)
          continue;

        availableQuestions.addAll(quiz.questions);
      }

      // Shuffle and select questions
      availableQuestions.shuffle(_random);
      final selectedQuestions = availableQuestions.take(questionCount).toList();

      return Quiz(
        id: 'personalized_${DateTime.now().millisecondsSinceEpoch}',
        title: 'اختبار مخصص',
        description: 'اختبار مخصص بناءً على تفضيلاتك وأداءك',
        type: preferredType ?? QuizType.general,
        difficulty: preferredDifficulty ?? DifficultyLevel.beginner,
        questions: selectedQuestions,
        timeLimit: questionCount * 2, // 2 minutes per question
        passingScore: 70,
        createdAt: DateTime.now(),
        tags: ['مخصص'],
      );
    } catch (e) {
      debugPrint('Error generating personalized quiz: $e');
      rethrow;
    }
  }

  // Cache management
  Future<void> _cacheQuizzes(List<Quiz> quizzes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = quizzes.map((q) => q.toJson()).toList();
      await prefs.setString(_quizzesKey, json.encode(quizzesJson));
    } catch (e) {
      debugPrint('Error caching quizzes: $e');
    }
  }

  Future<List<Quiz>> _getCachedQuizzes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = prefs.getString(_quizzesKey);

      if (quizzesJson != null) {
        final List<dynamic> decoded = json.decode(quizzesJson);
        return decoded.map((q) => Quiz.fromJson(q)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Error loading cached quizzes: $e');
      return [];
    }
  }

  Future<void> _saveAttempt(QuizAttempt attempt) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attemptsJson = prefs.getString(_attemptsKey);

      List<QuizAttempt> attempts = [];
      if (attemptsJson != null) {
        final List<dynamic> decoded = json.decode(attemptsJson);
        attempts = decoded.map((a) => QuizAttempt.fromJson(a)).toList();
      }

      // Remove existing attempt with same ID
      attempts.removeWhere((a) => a.id == attempt.id);
      attempts.add(attempt);

      final updatedJson = attempts.map((a) => a.toJson()).toList();
      await prefs.setString(_attemptsKey, json.encode(updatedJson));
    } catch (e) {
      debugPrint('Error saving attempt: $e');
    }
  }

  // Clear all quiz data
  Future<void> clearQuizData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_quizzesKey);
      await prefs.remove(_attemptsKey);
    } catch (e) {
      debugPrint('Error clearing quiz data: $e');
    }
  }
}
