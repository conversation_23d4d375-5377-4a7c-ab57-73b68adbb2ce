import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'lib/demo/enhanced_features_demo.dart';

/// ملف تشغيل سريع للتطبيق المطور
/// 
/// هذا الملف يتيح تشغيل التطبيق مع جميع الميزات الجديدة
/// بدون الحاجة لتعديل الملف الرئيسي
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تعيين اتجاه الشاشة
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تعيين شريط الحالة
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // تشغيل التطبيق المطور
  runApp(const EnhancedFeaturesDemo());
}

/// معلومات التطبيق المطور
class AppInfo {
  static const String name = 'نور القرآن المطور';
  static const String version = '2.0.0';
  static const String description = 'تطبيق قراءة القرآن مع ميزات ذكية ومتقدمة';
  
  static const List<String> newFeatures = [
    'نظام إدارة الحالة المتقدم',
    'إشعارات ذكية ومخصصة', 
    'نظام الإنجازات والتحديات',
    'معرض صور محسن',
    'لوحة تحكم تفاعلية',
    'تحليلات وإحصائيات متقدمة',
    'واجهة مستخدم محسنة',
    'رسوم متحركة سلسة',
  ];
  
  static const List<String> technicalImprovements = [
    'أداء محسن',
    'استهلاك ذاكرة أقل',
    'تحميل أسرع',
    'استقرار أكبر',
    'كود منظم ومرن',
    'اختبارات شاملة',
  ];
}

/// تعليمات التشغيل
/// 
/// لتشغيل التطبيق المطور:
/// 1. تأكد من تثبيت Flutter SDK
/// 2. قم بتشغيل: flutter pub get
/// 3. قم بتشغيل: flutter run run_enhanced_app.dart
/// 
/// أو استخدم الأمر المباشر:
/// dart run_enhanced_app.dart
