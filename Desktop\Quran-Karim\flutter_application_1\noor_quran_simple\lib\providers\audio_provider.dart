import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/quran_api_service.dart';

class AudioProvider extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _playbackSpeed = 1.0;
  String _selectedReciter = 'mishari_alafasy';
  bool _autoPlay = false;
  bool _repeatMode = false;

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;
  Duration get duration => _duration;
  Duration get position => _position;
  double get playbackSpeed => _playbackSpeed;
  String get selectedReciter => _selectedReciter;
  bool get autoPlay => _autoPlay;
  bool get repeatMode => _repeatMode;

  AudioProvider() {
    _initializeAudioPlayer();
    _loadPreferences();
  }

  void _initializeAudioPlayer() {
    _audioPlayer.onDurationChanged.listen((duration) {
      _duration = duration;
      notifyListeners();
    });

    _audioPlayer.onPositionChanged.listen((position) {
      _position = position;
      notifyListeners();
    });

    _audioPlayer.onPlayerStateChanged.listen((state) {
      _isPlaying = state == PlayerState.playing;
      _isLoading = state == PlayerState.playing;
      notifyListeners();
    });
  }

  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedReciter =
          prefs.getString(AppConstants.keySelectedReciter) ?? 'mishari_alafasy';
      _autoPlay = prefs.getBool(AppConstants.keyAutoPlay) ?? false;
      _repeatMode = prefs.getBool(AppConstants.keyRepeatMode) ?? false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading audio preferences: $e');
    }
  }

  Future<void> playAudio(String url) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _audioPlayer.play(UrlSource(url));
    } catch (e) {
      debugPrint('Error playing audio: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> playSurah(int surahNumber) async {
    final audioUrl = QuranApiService.getAudioUrl(_selectedReciter, surahNumber);
    await playAudio(audioUrl);
  }

  Future<void> playAyah(int surahNumber, int ayahNumber) async {
    final audioUrl = QuranApiService.getAyahAudioUrl(
      _selectedReciter,
      surahNumber,
      ayahNumber,
    );
    await playAudio(audioUrl);
  }

  Future<void> setReciter(String reciter) async {
    _selectedReciter = reciter;
    notifyListeners();

    // إيقاف الصوت الحالي عند تغيير القارئ
    if (_isPlaying) {
      await pauseAudio();
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter);
    } catch (e) {
      debugPrint('Error saving reciter: $e');
    }
  }

  Future<void> pauseAudio() async {
    await _audioPlayer.pause();
  }

  Future<void> stopAudio() async {
    await _audioPlayer.stop();
  }

  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed;
    await _audioPlayer.setPlaybackRate(speed);
    notifyListeners();
  }

  Future<void> setSelectedReciter(String reciter) async {
    _selectedReciter = reciter;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter);
    } catch (e) {
      debugPrint('Error saving reciter: $e');
    }
  }

  void toggleAutoPlay() {
    _autoPlay = !_autoPlay;
    notifyListeners();
    _saveAutoPlay();
  }

  void toggleRepeatMode() {
    _repeatMode = !_repeatMode;
    notifyListeners();
    _saveRepeatMode();
  }

  Future<void> _saveAutoPlay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyAutoPlay, _autoPlay);
    } catch (e) {
      debugPrint('Error saving auto play: $e');
    }
  }

  Future<void> _saveRepeatMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyRepeatMode, _repeatMode);
    } catch (e) {
      debugPrint('Error saving repeat mode: $e');
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
