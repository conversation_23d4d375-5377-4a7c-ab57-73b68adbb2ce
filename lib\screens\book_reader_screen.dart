import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/religious_books_provider.dart';
import '../providers/theme_provider.dart';
import '../models/religious_book_models.dart';

class BookReaderScreen extends StatefulWidget {
  final ReligiousBook book;
  final BookChapter? initialChapter;

  const BookReaderScreen({
    super.key,
    required this.book,
    this.initialChapter,
  });

  @override
  State<BookReaderScreen> createState() => _BookReaderScreenState();
}

class _BookReaderScreenState extends State<BookReaderScreen> {
  late ScrollController _scrollController;
  bool _showAppBar = true;
  double _fontSize = 20.0;
  bool _showExplanations = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<ReligiousBooksProvider>();
      provider.setCurrentBook(widget.book);
      if (widget.initialChapter != null) {
        provider.setCurrentChapter(widget.initialChapter!);
      } else if (widget.book.chapters.isNotEmpty) {
        provider.setCurrentChapter(widget.book.chapters.first);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final booksProvider = context.watch<ReligiousBooksProvider>();

    return Scaffold(
      backgroundColor: themeProvider.isDarkMode ? Colors.black87 : Colors.white,
      appBar: _showAppBar
          ? AppBar(
              title: Text(widget.book.titleArabic),
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              actions: [
                IconButton(
                  icon: Icon(_showExplanations ? Icons.visibility_off : Icons.visibility),
                  onPressed: () {
                    setState(() {
                      _showExplanations = !_showExplanations;
                    });
                  },
                  tooltip: _showExplanations ? 'إخفاء الشروح' : 'إظهار الشروح',
                ),
                IconButton(
                  icon: const Icon(Icons.text_fields),
                  onPressed: _showFontSizeDialog,
                  tooltip: 'حجم الخط',
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'chapters':
                        _showChaptersList();
                        break;
                      case 'bookmark':
                        _bookmarkCurrentSection();
                        break;
                      case 'share':
                        _shareCurrentSection();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'chapters',
                      child: Row(
                        children: [
                          Icon(Icons.list),
                          SizedBox(width: 8),
                          Text('قائمة الفصول'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'bookmark',
                      child: Row(
                        children: [
                          Icon(Icons.bookmark_add),
                          SizedBox(width: 8),
                          Text('إضافة علامة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('مشاركة'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            )
          : null,
      body: booksProvider.currentChapter == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // شريط التنقل بين الفصول
                _buildChapterNavigation(context, booksProvider),
                
                // محتوى الفصل
                Expanded(
                  child: _buildChapterContent(context, booksProvider.currentChapter!),
                ),
              ],
            ),
    );
  }

  /// بناء شريط التنقل بين الفصول
  Widget _buildChapterNavigation(BuildContext context, ReligiousBooksProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: Colors.teal.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: provider.getPreviousChapter() != null
                ? () => provider.goToPreviousChapter()
                : null,
            tooltip: 'الفصل السابق',
          ),
          Expanded(
            child: Text(
              provider.currentChapter?.titleArabic ?? '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios),
            onPressed: provider.getNextChapter() != null
                ? () => provider.goToNextChapter()
                : null,
            tooltip: 'الفصل التالي',
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الفصل
  Widget _buildChapterContent(BuildContext context, BookChapter chapter) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: chapter.sections.length,
      itemBuilder: (context, index) {
        final section = chapter.sections[index];
        return _buildSectionCard(context, section);
      },
    );
  }

  /// بناء بطاقة القسم
  Widget _buildSectionCard(BuildContext context, BookSection section) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            if (section.title.isNotEmpty)
              Text(
                section.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
            
            if (section.title.isNotEmpty)
              const SizedBox(height: AppConstants.paddingSmall),
            
            // محتوى القسم
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: themeProvider.isDarkMode 
                    ? Colors.grey.shade800 
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: widget.book.isPoetic ? Colors.amber.shade300 : Colors.grey.shade300,
                  width: widget.book.isPoetic ? 2 : 1,
                ),
              ),
              child: SelectableText(
                section.content,
                style: TextStyle(
                  fontSize: _fontSize,
                  height: widget.book.isPoetic ? 1.8 : 1.6,
                  fontFamily: widget.book.isPoetic ? 'Amiri' : null,
                  color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
                ),
                textAlign: widget.book.isPoetic ? TextAlign.center : TextAlign.justify,
              ),
            ),
            
            // أزرار التحكم
            const SizedBox(height: AppConstants.paddingSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر الشرح
                if (section.explanation != null)
                  ElevatedButton.icon(
                    onPressed: () => _showExplanationDialog(context, section),
                    icon: const Icon(Icons.help_outline, size: 18),
                    label: const Text('الشرح'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                
                // زر الاستماع
                if (section.audioUrl != null)
                  ElevatedButton.icon(
                    onPressed: () => _playAudio(section.audioUrl!),
                    icon: const Icon(Icons.play_arrow, size: 18),
                    label: const Text('استماع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                
                // زر المشاركة
                IconButton(
                  onPressed: () => _shareSection(section),
                  icon: const Icon(Icons.share),
                  tooltip: 'مشاركة',
                ),
              ],
            ),
            
            // عرض الشرح إذا كان مفعلاً
            if (_showExplanations && section.explanation != null) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: Colors.blue.shade600, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'الشرح:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      section.explanation!,
                      style: TextStyle(
                        fontSize: _fontSize - 2,
                        height: 1.6,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عرض حوار الشرح
  void _showExplanationDialog(BuildContext context, BookSection section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lightbulb, color: Colors.blue.shade600),
            const SizedBox(width: 8),
            const Text('الشرح'),
          ],
        ),
        content: SingleChildScrollView(
          child: Text(
            section.explanation!,
            style: const TextStyle(fontSize: 16, height: 1.6),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (section.audioUrl != null)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                _playAudio(section.audioUrl!);
              },
              icon: const Icon(Icons.play_arrow),
              label: const Text('استماع'),
            ),
        ],
      ),
    );
  }

  /// تشغيل الصوت
  void _playAudio(String audioUrl) {
    // TODO: تنفيذ تشغيل الصوت
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تشغيل الصوت: $audioUrl'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// مشاركة القسم
  void _shareSection(BookSection section) {
    // TODO: تنفيذ المشاركة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة قيد التطوير'),
      ),
    );
  }

  /// عرض حوار حجم الخط
  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'مثال على النص',
                style: TextStyle(fontSize: _fontSize),
              ),
              const SizedBox(height: 16),
              Slider(
                value: _fontSize,
                min: 14.0,
                max: 32.0,
                divisions: 9,
                label: _fontSize.round().toString(),
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                  this.setState(() {});
                },
              ),
              Text('${_fontSize.round()}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض قائمة الفصول
  void _showChaptersList() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فصول الكتاب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Expanded(
              child: ListView.builder(
                itemCount: widget.book.chapters.length,
                itemBuilder: (context, index) {
                  final chapter = widget.book.chapters[index];
                  return ListTile(
                    title: Text(chapter.titleArabic),
                    subtitle: Text('${chapter.sections.length} أقسام'),
                    onTap: () {
                      context.read<ReligiousBooksProvider>().setCurrentChapter(chapter);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// إضافة علامة للقسم الحالي
  void _bookmarkCurrentSection() {
    // TODO: تنفيذ إضافة العلامات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إضافة العلامة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// مشاركة القسم الحالي
  void _shareCurrentSection() {
    // TODO: تنفيذ المشاركة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة قيد التطوير'),
      ),
    );
  }
}
