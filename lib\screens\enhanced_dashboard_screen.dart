import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_state_manager.dart';
import '../providers/quran_provider.dart';
import '../providers/audio_provider.dart';
import '../services/achievement_service.dart';
import '../widgets/achievement_card.dart';
import '../widgets/challenge_card.dart';
import '../widgets/reading_progress_widget.dart';
import '../widgets/quick_stats_widget.dart';
import 'achievements_screen.dart';
import 'image_gallery_screen.dart';

/// لوحة التحكم المحسنة - لمسة شخصية لتجربة مميزة
class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() => _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDashboardData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  Future<void> _loadDashboardData() async {
    // تحميل البيانات اللازمة للوحة التحكم
    final appState = context.read<AppStateManager>();
    final quranProvider = context.read<QuranProvider>();
    
    // بدء جلسة قراءة جديدة
    appState.startReadingSession();
    
    // تحميل السور إذا لم تكن محملة
    if (quranProvider.surahs.isEmpty) {
      await quranProvider.loadSurahs();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildWelcomeSection(),
                    const SizedBox(height: 20),
                    _buildQuickStatsSection(),
                    const SizedBox(height: 20),
                    _buildReadingProgressSection(),
                    const SizedBox(height: 20),
                    _buildTodayChallengeSection(),
                    const SizedBox(height: 20),
                    _buildRecentAchievementsSection(),
                    const SizedBox(height: 20),
                    _buildQuickActionsSection(),
                    const SizedBox(height: 20),
                    _buildContinueReadingSection(),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق المخصص
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'نور القرآن',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: const Center(
            child: Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: () => _showNotificationsDialog(),
        ),
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () => _navigateToSettings(),
        ),
      ],
    );
  }

  /// بناء قسم الترحيب
  Widget _buildWelcomeSection() {
    return Consumer<AppStateManager>(
      builder: (context, appState, child) {
        final stats = appState.getQuickStats();
        final streak = stats['dailyStreak'] as int;
        
        return Card(
          elevation: 4,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [Colors.blue[50]!, Colors.blue[100]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.wb_sunny, color: Colors.amber, size: 28),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getGreeting(),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                          if (streak > 0)
                            Text(
                              'سلسلة القراءة: $streak ${streak == 1 ? 'يوم' : 'أيام'} 🔥',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue[700],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  _getMotivationalMessage(streak),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return Consumer<AppStateManager>(
      builder: (context, appState, child) {
        return const QuickStatsWidget();
      },
    );
  }

  /// بناء قسم تقدم القراءة
  Widget _buildReadingProgressSection() {
    return Consumer<QuranProvider>(
      builder: (context, quranProvider, child) {
        return const ReadingProgressWidget();
      },
    );
  }

  /// بناء قسم تحدي اليوم
  Widget _buildTodayChallengeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.flag, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  'تحدي اليوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToAchievements(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اقرأ 10 آيات اليوم',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('ابدأ يومك بقراءة 10 آيات من القرآن الكريم'),
                  const SizedBox(height: 12),
                  LinearProgressIndicator(
                    value: 0.3,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('3 / 10 آيات'),
                      ElevatedButton(
                        onPressed: () => _startReading(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('ابدأ القراءة'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإنجازات الحديثة
  Widget _buildRecentAchievementsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.emoji_events, color: Colors.amber),
                const SizedBox(width: 8),
                const Text(
                  'الإنجازات الحديثة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToAchievements(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 5,
                itemBuilder: (context, index) {
                  return Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.amber[100],
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.amber),
                          ),
                          child: const Icon(Icons.star, color: Colors.amber),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'إنجاز',
                          style: TextStyle(fontSize: 10),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإجراءات السريعة
  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              childAspectRatio: 1.2,
              children: [
                _buildQuickActionTile(
                  'فهرس الصور',
                  Icons.image,
                  Colors.blue,
                  () => _navigateToImageGallery(),
                ),
                _buildQuickActionTile(
                  'الإنجازات',
                  Icons.emoji_events,
                  Colors.amber,
                  () => _navigateToAchievements(),
                ),
                _buildQuickActionTile(
                  'البحث',
                  Icons.search,
                  Colors.green,
                  () => _navigateToSearch(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionTile(String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم متابعة القراءة
  Widget _buildContinueReadingSection() {
    return Consumer<QuranProvider>(
      builder: (context, quranProvider, child) {
        if (quranProvider.lastReadSurahNumber == 0) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'متابعة القراءة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                ListTile(
                  leading: const CircleAvatar(
                    backgroundColor: Colors.green,
                    child: Icon(Icons.play_arrow, color: Colors.white),
                  ),
                  title: Text('سورة رقم ${quranProvider.lastReadSurahNumber}'),
                  subtitle: Text('آية رقم ${quranProvider.lastReadAyahNumber}'),
                  trailing: ElevatedButton(
                    onPressed: () => _continueReading(),
                    child: const Text('متابعة'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // دوال مساعدة
  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  }

  String _getMotivationalMessage(int streak) {
    if (streak == 0) return 'ابدأ رحلتك مع القرآن الكريم اليوم';
    if (streak < 7) return 'استمر في القراءة لبناء عادة يومية مفيدة';
    if (streak < 30) return 'رائع! أنت تبني عادة قوية مع القرآن';
    return 'مبارك! أنت قارئ مثابر ومتميز';
  }

  // دوال التنقل
  void _navigateToImageGallery() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ImageGalleryScreen()),
    );
  }

  void _navigateToAchievements() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AchievementsScreen()),
    );
  }

  void _navigateToSearch() {
    // تنفيذ التنقل للبحث
  }

  void _navigateToSettings() {
    // تنفيذ التنقل للإعدادات
  }

  void _startReading() {
    // تنفيذ بدء القراءة
  }

  void _continueReading() {
    // تنفيذ متابعة القراءة
  }

  void _showNotificationsDialog() {
    // عرض نافذة الإشعارات
  }
}
