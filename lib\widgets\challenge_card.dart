import 'package:flutter/material.dart';
import '../models/achievement.dart';

/// بطاقة عرض التحدي - تصميم محفز للمشاركة
class ChallengeCard extends StatelessWidget {
  final Challenge challenge;
  final VoidCallback? onTap;
  final VoidCallback? onStart;

  const ChallengeCard({
    super.key,
    required this.challenge,
    this.onTap,
    this.onStart,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: challenge.isActive ? 4 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: challenge.isCompleted ? _getCompletedGradient() : null,
            border: challenge.isActive && !challenge.isExpired
                ? Border.all(color: _getChallengeColor(), width: 2)
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس التحدي
              Row(
                children: [
                  // أيقونة نوع التحدي
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getChallengeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getChallengeIcon(),
                      color: _getChallengeColor(),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // عنوان التحدي
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          challenge.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: challenge.isCompleted 
                                ? Colors.white 
                                : theme.textTheme.titleLarge?.color,
                          ),
                        ),
                        Text(
                          challenge.type.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: challenge.isCompleted 
                                ? Colors.white70 
                                : _getChallengeColor(),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // حالة التحدي
                  _buildStatusBadge(),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // وصف التحدي
              Text(
                challenge.description,
                style: TextStyle(
                  fontSize: 14,
                  color: challenge.isCompleted 
                      ? Colors.white70 
                      : theme.textTheme.bodyMedium?.color,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // شريط التقدم
              _buildProgressSection(),
              
              const SizedBox(height: 16),
              
              // معلومات إضافية وأزرار
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // الوقت المتبقي
                  _buildTimeRemaining(),
                  
                  // النقاط والزر
                  Row(
                    children: [
                      // النقاط
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.stars,
                              size: 14,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${challenge.rewardPoints}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.amber,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      
                      // زر العمل
                      if (!challenge.isCompleted && !challenge.isExpired && onStart != null)
                        ElevatedButton(
                          onPressed: onStart,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _getChallengeColor(),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          child: const Text(
                            'ابدأ',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شارة الحالة
  Widget _buildStatusBadge() {
    String text;
    Color color;
    IconData icon;

    if (challenge.isCompleted) {
      text = 'مكتمل';
      color = Colors.green;
      icon = Icons.check_circle;
    } else if (challenge.isExpired) {
      text = 'منتهي';
      color = Colors.red;
      icon = Icons.cancel;
    } else {
      text = 'نشط';
      color = _getChallengeColor();
      icon = Icons.play_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التقدم
  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم',
              style: TextStyle(
                fontSize: 12,
                color: challenge.isCompleted ? Colors.white70 : Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${challenge.currentProgress.toInt()} / ${challenge.targetValue.toInt()}',
              style: TextStyle(
                fontSize: 12,
                color: challenge.isCompleted ? Colors.white : _getChallengeColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: challenge.progressPercentage,
          backgroundColor: challenge.isCompleted 
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            challenge.isCompleted ? Colors.white : _getChallengeColor(),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${(challenge.progressPercentage * 100).toInt()}% مكتمل',
          style: TextStyle(
            fontSize: 10,
            color: challenge.isCompleted ? Colors.white60 : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// بناء الوقت المتبقي
  Widget _buildTimeRemaining() {
    if (challenge.isCompleted || challenge.isExpired) {
      return const SizedBox.shrink();
    }

    final timeRemaining = challenge.timeRemaining;
    String timeText;

    if (timeRemaining.inDays > 0) {
      timeText = '${timeRemaining.inDays} يوم';
    } else if (timeRemaining.inHours > 0) {
      timeText = '${timeRemaining.inHours} ساعة';
    } else {
      timeText = '${timeRemaining.inMinutes} دقيقة';
    }

    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          'متبقي: $timeText',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// الحصول على لون التحدي
  Color _getChallengeColor() {
    switch (challenge.type) {
      case ChallengeType.daily:
        return Colors.blue;
      case ChallengeType.weekly:
        return Colors.green;
      case ChallengeType.monthly:
        return Colors.purple;
      case ChallengeType.special:
        return Colors.amber;
    }
  }

  /// الحصول على أيقونة التحدي
  IconData _getChallengeIcon() {
    switch (challenge.type) {
      case ChallengeType.daily:
        return Icons.today;
      case ChallengeType.weekly:
        return Icons.calendar_today;
      case ChallengeType.monthly:
        return Icons.calendar_month;
      case ChallengeType.special:
        return Icons.star;
    }
  }

  /// الحصول على تدرج التحدي المكتمل
  LinearGradient _getCompletedGradient() {
    return const LinearGradient(
      colors: [Colors.green, Colors.lightGreen],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}

/// بطاقة تحدي مصغرة
class MiniChallengeCard extends StatelessWidget {
  final Challenge challenge;
  final VoidCallback? onTap;

  const MiniChallengeCard({
    super.key,
    required this.challenge,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: challenge.isCompleted 
              ? Colors.green.withValues(alpha: 0.1)
              : Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: challenge.isCompleted ? Colors.green : Colors.blue,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              challenge.isCompleted ? Icons.check_circle : Icons.flag,
              color: challenge.isCompleted ? Colors.green : Colors.blue,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              challenge.title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: challenge.isCompleted ? Colors.green[800] : Colors.blue[800],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: challenge.progressPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                challenge.isCompleted ? Colors.green : Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
