import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/quran_models.dart';
import '../constants/app_constants.dart';

class QuranApiService {
  static const String baseUrl = 'https://api.alquran.cloud/v1';

  // Cache للبيانات
  static List<Surah>? _cachedSurahs;
  static Map<int, Surah> _cachedSurahDetails = {};

  /// جلب قائمة السور
  static Future<List<Surah>> getSurahs() async {
    if (_cachedSurahs != null) {
      return _cachedSurahs!;
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/surah'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> surahsData = data['data'];

        _cachedSurahs = surahsData
            .map((surahJson) => Surah.fromJson(surahJson))
            .toList();
        return _cachedSurahs!;
      } else {
        throw Exception('فشل في جلب قائمة السور');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// جلب سورة محددة مع آياتها
  static Future<Surah> getSurah(int surahNumber) async {
    if (_cachedSurahDetails.containsKey(surahNumber)) {
      return _cachedSurahDetails[surahNumber]!;
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/surah/$surahNumber'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surah = Surah.fromJson(data['data']);

        _cachedSurahDetails[surahNumber] = surah;
        return surah;
      } else {
        throw Exception('فشل في جلب السورة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// جلب آية محددة
  static Future<Ayah> getAyah(int surahNumber, int ayahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/ayah/$surahNumber:$ayahNumber'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Ayah.fromJson(data['data']);
      } else {
        throw Exception('فشل في جلب الآية');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// البحث في القرآن
  static Future<List<SearchResult>> searchQuran(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/search/$query/all/ar'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> matches = data['data']['matches'];

        return matches.map((match) {
          return SearchResult(
            surahNumber: match['surah']['number'],
            surahName: match['surah']['name'],
            ayahNumber: match['numberInSurah'],
            ayahText: match['text'],
            highlightedText: _highlightSearchTerm(match['text'], query),
          );
        }).toList();
      } else {
        throw Exception('فشل في البحث');
      }
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }

  /// جلب القرآن الكامل
  static Future<List<Surah>> getCompleteQuran() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/quran/ar.alafasy'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> surahsData = data['data']['surahs'];

        return surahsData
            .map((surahJson) => Surah.fromJson(surahJson))
            .toList();
      } else {
        throw Exception('فشل في جلب القرآن الكامل');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// جلب رابط الصوت للسورة
  static String getAudioUrl(String reciterIdentifier, int surahNumber) {
    // روابط الصوت للقراء المختلفين مع خوادم مختلفة
    final Map<String, String> reciterUrls = {
      'mishari_alafasy': 'https://server8.mp3quran.net/afs',
      'abdulbasit_abdulsamad': 'https://server7.mp3quran.net/basit',
      'abdurrahman_sudais': 'https://server11.mp3quran.net/sds',
      'ahmed_alajamy': 'https://server10.mp3quran.net/ajm',
      'saad_alghamdi': 'https://server7.mp3quran.net/s_gmd',
      'yasser_aldosari': 'https://server11.mp3quran.net/yasser',
      'maher_almuaiqly': 'https://server12.mp3quran.net/maher',
      'mohamed_siddiq_alminshawi': 'https://server10.mp3quran.net/minsh',
      // القراء الموريتانيون مع خوادم مختلفة
      'mohamed_ould_sidia': 'https://server9.mp3quran.net/afs',
      'hussein_ould_ahmadou': 'https://server10.mp3quran.net/basit',
      'mohamed_ould_mammi': 'https://server11.mp3quran.net/sds',
      'abdullah_ould_abdulmalik': 'https://server12.mp3quran.net/ajm',
      'mohamed_salem_ould_adoud': 'https://server13.mp3quran.net/s_gmd',
      'ahmadou_ould_lmarabit': 'https://server14.mp3quran.net/yasser',
      'mohamed_fadel_ould_mohamed_amin': 'https://server15.mp3quran.net/maher',
    };

    final baseUrl =
        reciterUrls[reciterIdentifier] ?? reciterUrls['mishari_alafasy']!;
    final paddedSurahNumber = surahNumber.toString().padLeft(3, '0');

    return '$baseUrl/$paddedSurahNumber.mp3';
  }

  /// جلب رابط الصوت للآية
  static String getAyahAudioUrl(
    String reciterIdentifier,
    int surahNumber,
    int ayahNumber,
  ) {
    final baseUrl = getAudioUrl(reciterIdentifier, surahNumber);
    return baseUrl.replaceAll('.mp3', '_$ayahNumber.mp3');
  }

  /// تمييز نص البحث
  static String _highlightSearchTerm(String text, String searchTerm) {
    if (searchTerm.isEmpty) return text;

    final regex = RegExp(searchTerm, caseSensitive: false);
    return text.replaceAllMapped(regex, (match) {
      return '<mark>${match.group(0)}</mark>';
    });
  }

  /// جلب معلومات الإصدارات المتاحة
  static Future<List<QuranEdition>> getAvailableEditions() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/edition'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> editionsData = data['data'];

        return editionsData
            .where((edition) => edition['language'] == 'ar')
            .map((editionJson) => QuranEdition.fromJson(editionJson))
            .toList();
      } else {
        throw Exception('فشل في جلب الإصدارات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// مسح الكاش
  static void clearCache() {
    _cachedSurahs = null;
    _cachedSurahDetails.clear();
  }

  /// جلب آيات صفحة معينة
  static Future<List<Ayah>> getPageAyahs(int pageNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/page/$pageNumber/ar.alafasy'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> ayahsData = data['data']['ayahs'];

        return ayahsData.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
      } else {
        throw Exception('فشل في جلب آيات الصفحة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  /// جلب آيات جزء معين
  static Future<List<Ayah>> getJuzAyahs(int juzNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/juz/$juzNumber/ar.alafasy'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> ayahsData = data['data']['ayahs'];

        return ayahsData.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
      } else {
        throw Exception('فشل في جلب آيات الجزء');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}
