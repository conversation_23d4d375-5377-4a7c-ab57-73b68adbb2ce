/// نماذج البيانات للمكتبة الدينية
import 'package:flutter/foundation.dart';

/// نموذج الكتاب
class Book {
  final String id;
  final String title;
  final String author;
  final String description;
  final BookType type;
  final String? imageUrl;
  final List<Chapter> chapters;
  final Map<String, dynamic>? metadata;

  const Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.type,
    this.imageUrl,
    required this.chapters,
    this.metadata,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] as String,
      title: json['title'] as String,
      author: json['author'] as String,
      description: json['description'] as String,
      type: BookType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BookType.fiqh,
      ),
      imageUrl: json['imageUrl'] as String?,
      chapters:
          (json['chapters'] as List<dynamic>?)
              ?.map((e) => Chapter.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'type': type.name,
      'imageUrl': imageUrl,
      'chapters': chapters.map((e) => e.toJson()).toList(),
      'metadata': metadata,
    };
  }
}

/// أنواع الكتب
enum BookType {
  fiqh('فقه'),
  hadith('حديث'),
  tafsir('تفسير'),
  aqeedah('عقيدة'),
  sirah('سيرة'),
  adab('أدب'),
  madh('مدح نبوي'),
  awrad('أوراد'),
  tasawwuf('تصوف'),
  other('أخرى');

  const BookType(this.displayName);
  final String displayName;
}

/// نموذج الفصل
class Chapter {
  final String id;
  final String title;
  final int order;
  final List<Section> sections;
  final String? summary;

  const Chapter({
    required this.id,
    required this.title,
    required this.order,
    required this.sections,
    this.summary,
  });

  factory Chapter.fromJson(Map<String, dynamic> json) {
    return Chapter(
      id: json['id'] as String,
      title: json['title'] as String,
      order: json['order'] as int,
      sections:
          (json['sections'] as List<dynamic>?)
              ?.map((e) => Section.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      summary: json['summary'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'order': order,
      'sections': sections.map((e) => e.toJson()).toList(),
      'summary': summary,
    };
  }
}

/// نموذج القسم
class Section {
  final String id;
  final String title;
  final String content;
  final int order;
  final List<String>? keywords;
  final String? commentary;
  final String? explanation;
  final String? audioUrl;

  const Section({
    required this.id,
    required this.title,
    required this.content,
    required this.order,
    this.keywords,
    this.commentary,
    this.explanation,
    this.audioUrl,
  });

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      order: json['order'] as int,
      keywords: (json['keywords'] as List<dynamic>?)?.cast<String>(),
      commentary: json['commentary'] as String?,
      explanation: json['explanation'] as String?,
      audioUrl: json['audio_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'order': order,
      'keywords': keywords,
      'commentary': commentary,
      'explanation': explanation,
      'audio_url': audioUrl,
    };
  }
}

/// نموذج الحديث
class Hadith {
  final String id;
  final String text;
  final String narrator;
  final String source;
  final String? grade;
  final String? commentary;
  final List<String>? keywords;
  final Map<String, dynamic>? metadata;

  const Hadith({
    required this.id,
    required this.text,
    required this.narrator,
    required this.source,
    this.grade,
    this.commentary,
    this.keywords,
    this.metadata,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id']?.toString() ?? '',
      text: json['text'] as String? ?? '',
      narrator: json['narrator'] as String? ?? '',
      source: json['source'] as String? ?? '',
      grade: json['grade'] as String?,
      commentary: json['commentary'] as String?,
      keywords: (json['keywords'] as List<dynamic>?)?.cast<String>(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'narrator': narrator,
      'source': source,
      'grade': grade,
      'commentary': commentary,
      'keywords': keywords,
      'metadata': metadata,
    };
  }
}

/// نموذج التفسير
class Tafsir {
  final String id;
  final String name;
  final String author;
  final String language;
  final String? description;

  const Tafsir({
    required this.id,
    required this.name,
    required this.author,
    required this.language,
    this.description,
  });

  factory Tafsir.fromJson(Map<String, dynamic> json) {
    return Tafsir(
      id: json['id']?.toString() ?? '',
      name: json['name'] as String? ?? '',
      author: json['author_name'] as String? ?? '',
      language: json['language_name'] as String? ?? '',
      description: json['translated_name']?['name'] as String?,
    );
  }
}

/// نموذج آية مع تفسير
class VerseWithTafsir {
  final int surahNumber;
  final int verseNumber;
  final String verseText;
  final String tafsirText;
  final String tafsirName;

  const VerseWithTafsir({
    required this.surahNumber,
    required this.verseNumber,
    required this.verseText,
    required this.tafsirText,
    required this.tafsirName,
  });

  factory VerseWithTafsir.fromJson(Map<String, dynamic> json) {
    return VerseWithTafsir(
      surahNumber: json['surah_number'] as int? ?? 0,
      verseNumber: json['verse_number'] as int? ?? 0,
      verseText: json['verse_text'] as String? ?? '',
      tafsirText: json['tafsir_text'] as String? ?? '',
      tafsirName: json['tafsir_name'] as String? ?? '',
    );
  }
}

/// نموذج نتيجة البحث
class SearchResult {
  final String id;
  final String title;
  final String content;
  final String source;
  final SearchResultType type;
  final Map<String, dynamic>? metadata;

  const SearchResult({
    required this.id,
    required this.title,
    required this.content,
    required this.source,
    required this.type,
    this.metadata,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      source: json['source'] as String,
      type: SearchResultType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SearchResultType.other,
      ),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// أنواع نتائج البحث
enum SearchResultType {
  verse('آية'),
  hadith('حديث'),
  fiqh('فقه'),
  tafsir('تفسير'),
  other('أخرى');

  const SearchResultType(this.displayName);
  final String displayName;
}
