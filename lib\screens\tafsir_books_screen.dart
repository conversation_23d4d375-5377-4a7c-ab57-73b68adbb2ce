import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// شاشة كتب التفسير
class TafsirBooksScreen extends StatefulWidget {
  const TafsirBooksScreen({super.key});

  @override
  State<TafsirBooksScreen> createState() => _TafsirBooksScreenState();
}

class _TafsirBooksScreenState extends State<TafsirBooksScreen> {
  final List<TafsirBook> _books = [
    TafsirBook(
      id: 'jalalayn',
      title: 'تفسير الجلالين',
      author: 'جلال الدين المحلي وجلال الدين السيوطي',
      description: 'تفسير مختصر للقرآن الكريم، يجمع بين الإيجاز والوضوح',
      chaptersCount: 2,
      type: 'تفسير',
    ),
    TafsirBook(
      id: 'ibn_kathir',
      title: 'تفسير ابن كثير (مختارات)',
      author: 'إسماعيل بن عمر بن كثير',
      description: 'تفسير القرآن العظيم، من أشهر التفاسير بالمأثور',
      chaptersCount: 1,
      type: 'تفسير',
    ),
    TafsirBook(
      id: 'qurtubi',
      title: 'تفسير القرطبي (مختارات)',
      author: 'محمد بن أحمد القرطبي',
      description: 'الجامع لأحكام القرآن، يركز على الأحكام الفقهية المستنبطة من الآيات',
      chaptersCount: 1,
      type: 'تفسير',
    ),
    TafsirBook(
      id: 'kalam_islamiyya',
      title: 'الجواهر الكلامية في العقائد الإسلامية',
      author: 'عبد الله بن فودي',
      description: 'منظومة في العقيدة الإسلامية على مذهب أهل السنة والجماعة',
      chaptersCount: 0,
      type: 'عقيدة',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الكتب الفقهية'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _books.length,
        itemBuilder: (context, index) {
          final book = _books[index];
          return _buildBookCard(book);
        },
      ),
    );
  }

  Widget _buildBookCard(TafsirBook book) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة الكتاب
            Container(
              width: 60,
              height: 80,
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.book,
                size: 32,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            // تفاصيل الكتاب
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    book.author,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    book.description,
                    style: const TextStyle(fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          book.type,
                          style: const TextStyle(
                            fontSize: 10,
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${book.chaptersCount} فصل',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // سهم التنقل
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}

/// نموذج كتاب التفسير
class TafsirBook {
  final String id;
  final String title;
  final String author;
  final String description;
  final int chaptersCount;
  final String type;

  const TafsirBook({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.chaptersCount,
    required this.type,
  });
}
