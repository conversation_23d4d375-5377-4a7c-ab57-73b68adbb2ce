import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/quran_models.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static Database? _database;
  static final DatabaseService _instance = DatabaseService._internal();
  
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // جدول المفضلة
    await db.execute('''
      CREATE TABLE bookmarks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        surah_number INTEGER NOT NULL,
        ayah_number INTEGER NOT NULL,
        surah_name TEXT NOT NULL,
        ayah_text TEXT NOT NULL,
        created_at TEXT NOT NULL,
        UNIQUE(surah_number, ayah_number)
      )
    ''');

    // جدول تاريخ القراءة
    await db.execute('''
      CREATE TABLE reading_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        surah_number INTEGER NOT NULL,
        ayah_number INTEGER NOT NULL,
        surah_name TEXT NOT NULL,
        read_at TEXT NOT NULL
      )
    ''');

    // جدول الإعدادات
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // جدول السور المحفوظة محلياً
    await db.execute('''
      CREATE TABLE cached_surahs (
        number INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        english_name TEXT NOT NULL,
        english_name_translation TEXT NOT NULL,
        revelation_type TEXT NOT NULL,
        number_of_ayahs INTEGER NOT NULL,
        data TEXT NOT NULL,
        cached_at TEXT NOT NULL
      )
    ''');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // إضافة تحديثات قاعدة البيانات هنا عند الحاجة
  }

  // ===== إدارة المفضلة =====

  /// إضافة مفضلة جديدة
  Future<int> addBookmark(Bookmark bookmark) async {
    final db = await database;
    try {
      return await db.insert(
        'bookmarks',
        {
          'surah_number': bookmark.surahNumber,
          'ayah_number': bookmark.ayahNumber,
          'surah_name': bookmark.surahName,
          'ayah_text': bookmark.ayahText,
          'created_at': bookmark.createdAt.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في إضافة المفضلة: $e');
    }
  }

  /// حذف مفضلة
  Future<int> removeBookmark(int surahNumber, int ayahNumber) async {
    final db = await database;
    try {
      return await db.delete(
        'bookmarks',
        where: 'surah_number = ? AND ayah_number = ?',
        whereArgs: [surahNumber, ayahNumber],
      );
    } catch (e) {
      throw Exception('فشل في حذف المفضلة: $e');
    }
  }

  /// جلب جميع المفضلة
  Future<List<Bookmark>> getAllBookmarks() async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'bookmarks',
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return Bookmark(
          id: maps[i]['id'],
          surahNumber: maps[i]['surah_number'],
          ayahNumber: maps[i]['ayah_number'],
          surahName: maps[i]['surah_name'],
          ayahText: maps[i]['ayah_text'],
          createdAt: DateTime.parse(maps[i]['created_at']),
        );
      });
    } catch (e) {
      throw Exception('فشل في جلب المفضلة: $e');
    }
  }

  /// التحقق من وجود مفضلة
  Future<bool> isBookmarked(int surahNumber, int ayahNumber) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'bookmarks',
        where: 'surah_number = ? AND ayah_number = ?',
        whereArgs: [surahNumber, ayahNumber],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// حذف جميع المفضلة
  Future<int> clearAllBookmarks() async {
    final db = await database;
    try {
      return await db.delete('bookmarks');
    } catch (e) {
      throw Exception('فشل في حذف جميع المفضلة: $e');
    }
  }

  // ===== إدارة تاريخ القراءة =====

  /// إضافة سجل قراءة
  Future<int> addReadingHistory(int surahNumber, int ayahNumber, String surahName) async {
    final db = await database;
    try {
      return await db.insert(
        'reading_history',
        {
          'surah_number': surahNumber,
          'ayah_number': ayahNumber,
          'surah_name': surahName,
          'read_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('فشل في إضافة سجل القراءة: $e');
    }
  }

  /// جلب تاريخ القراءة
  Future<List<Map<String, dynamic>>> getReadingHistory({int limit = 50}) async {
    final db = await database;
    try {
      return await db.query(
        'reading_history',
        orderBy: 'read_at DESC',
        limit: limit,
      );
    } catch (e) {
      throw Exception('فشل في جلب تاريخ القراءة: $e');
    }
  }

  /// حذف تاريخ القراءة
  Future<int> clearReadingHistory() async {
    final db = await database;
    try {
      return await db.delete('reading_history');
    } catch (e) {
      throw Exception('فشل في حذف تاريخ القراءة: $e');
    }
  }

  // ===== إدارة الإعدادات =====

  /// حفظ إعداد
  Future<void> saveSetting(String key, String value) async {
    final db = await database;
    try {
      await db.insert(
        'settings',
        {'key': key, 'value': value},
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في حفظ الإعداد: $e');
    }
  }

  /// جلب إعداد
  Future<String?> getSetting(String key) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: [key],
        limit: 1,
      );
      return result.isNotEmpty ? result.first['value'] : null;
    } catch (e) {
      return null;
    }
  }

  /// حذف إعداد
  Future<int> deleteSetting(String key) async {
    final db = await database;
    try {
      return await db.delete(
        'settings',
        where: 'key = ?',
        whereArgs: [key],
      );
    } catch (e) {
      throw Exception('فشل في حذف الإعداد: $e');
    }
  }

  // ===== إدارة الكاش =====

  /// حفظ سورة في الكاش
  Future<void> cacheSurah(Surah surah) async {
    final db = await database;
    try {
      await db.insert(
        'cached_surahs',
        {
          'number': surah.number,
          'name': surah.name,
          'english_name': surah.englishName,
          'english_name_translation': surah.englishNameTranslation,
          'revelation_type': surah.revelationType,
          'number_of_ayahs': surah.numberOfAyahs,
          'data': surah.toJson().toString(),
          'cached_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('فشل في حفظ السورة في الكاش: $e');
    }
  }

  /// جلب سورة من الكاش
  Future<Surah?> getCachedSurah(int surahNumber) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> result = await db.query(
        'cached_surahs',
        where: 'number = ?',
        whereArgs: [surahNumber],
        limit: 1,
      );
      
      if (result.isNotEmpty) {
        // التحقق من عمر الكاش (7 أيام)
        final cachedAt = DateTime.parse(result.first['cached_at']);
        final now = DateTime.now();
        if (now.difference(cachedAt).inDays > 7) {
          // حذف الكاش القديم
          await db.delete('cached_surahs', where: 'number = ?', whereArgs: [surahNumber]);
          return null;
        }
        
        return Surah(
          number: result.first['number'],
          name: result.first['name'],
          englishName: result.first['english_name'],
          englishNameTranslation: result.first['english_name_translation'],
          revelationType: result.first['revelation_type'],
          numberOfAyahs: result.first['number_of_ayahs'],
          ayahs: [], // سيتم جلب الآيات من API
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// مسح الكاش القديم
  Future<int> clearOldCache() async {
    final db = await database;
    try {
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      return await db.delete(
        'cached_surahs',
        where: 'cached_at < ?',
        whereArgs: [sevenDaysAgo.toIso8601String()],
      );
    } catch (e) {
      return 0;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
