import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/audio_provider.dart';
import '../services/real_audio_service.dart';
import '../utils/audio_url_tester.dart';

class AudioTestScreen extends StatefulWidget {
  const AudioTestScreen({super.key});

  @override
  State<AudioTestScreen> createState() => _AudioTestScreenState();
}

class _AudioTestScreenState extends State<AudioTestScreen> {
  String _selectedReciter = 'mishari_alafasy';
  int _selectedSurah = 1;
  String? _currentUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الصوت'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: Consumer<AudioProvider>(
        builder: (context, audioProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildReciterSelector(),
                const SizedBox(height: AppConstants.paddingMedium),
                _buildSurahSelector(),
                const SizedBox(height: AppConstants.paddingMedium),
                _buildUrlDisplay(),
                const SizedBox(height: AppConstants.paddingMedium),
                _buildControlButtons(audioProvider),
                const SizedBox(height: AppConstants.paddingMedium),
                _buildTestButtons(),
                const SizedBox(height: AppConstants.paddingMedium),
                _buildRecitersList(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildReciterSelector() {
    final reciters = RealAudioService.getAllReciters();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر القارئ:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButton<String>(
              value: _selectedReciter,
              isExpanded: true,
              items: reciters.entries.map((entry) {
                return DropdownMenuItem<String>(
                  value: entry.key,
                  child: Text(entry.value['name'] as String),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedReciter = value;
                    _updateUrl();
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSurahSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر السورة:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: _selectedSurah.toDouble(),
                    min: 1,
                    max: 114,
                    divisions: 113,
                    label: 'السورة $_selectedSurah',
                    onChanged: (value) {
                      setState(() {
                        _selectedSurah = value.round();
                        _updateUrl();
                      });
                    },
                  ),
                ),
                Text(
                  'السورة $_selectedSurah',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlDisplay() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'رابط الصوت:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: SelectableText(
                _currentUrl ?? 'لم يتم تحديد رابط',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons(AudioProvider audioProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              onPressed: () => _updateUrl(),
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث الرابط'),
            ),
            ElevatedButton.icon(
              onPressed: _currentUrl != null
                  ? () => _testPlay(audioProvider)
                  : null,
              icon: Icon(
                audioProvider.isPlaying ? Icons.stop : Icons.play_arrow,
              ),
              label: Text(audioProvider.isPlaying ? 'إيقاف' : 'تشغيل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () => _copyUrl(),
              icon: const Icon(Icons.copy),
              label: const Text('نسخ الرابط'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختبارات متقدمة:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => AudioUrlTester.runFullTest(),
                  icon: const Icon(Icons.science),
                  label: const Text('اختبار شامل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () =>
                      AudioUrlTester.testReciterUrls(_selectedReciter),
                  icon: const Icon(Icons.person_search),
                  label: const Text('اختبار القارئ'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _compareWithOthers(),
                  icon: const Icon(Icons.compare),
                  label: const Text('مقارنة'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => RealAudioService.validateUniqueUrls(),
                  icon: const Icon(Icons.check_circle),
                  label: const Text('فحص التفرد'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testQuickSwitch(),
                  icon: const Icon(Icons.swap_horiz),
                  label: const Text('اختبار سريع'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecitersList() {
    final reciters = RealAudioService.getAllReciters();

    return Expanded(
      child: Card(
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Text(
                'قائمة جميع القراء:',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: reciters.length,
                itemBuilder: (context, index) {
                  final entry = reciters.entries.elementAt(index);
                  final reciterKey = entry.key;
                  final reciterData = entry.value;
                  final isSelected = _selectedReciter == reciterKey;

                  return ListTile(
                    leading: Icon(
                      reciterData['tested'] == true
                          ? Icons.verified
                          : Icons.warning,
                      color: reciterData['tested'] == true
                          ? Colors.green
                          : Colors.orange,
                    ),
                    title: Text(
                      reciterData['name'] as String,
                      style: TextStyle(
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected ? AppConstants.primaryColor : null,
                      ),
                    ),
                    subtitle: Text(reciterData['country'] as String),
                    trailing: isSelected
                        ? const Icon(Icons.radio_button_checked)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedReciter = reciterKey;
                        _updateUrl();
                      });
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateUrl() {
    setState(() {
      _currentUrl = RealAudioService.getAudioUrl(
        _selectedReciter,
        _selectedSurah,
      );
    });
  }

  Future<void> _testPlay(AudioProvider audioProvider) async {
    if (audioProvider.isPlaying) {
      await audioProvider.stopAudio();
    } else {
      if (_currentUrl != null) {
        try {
          // تغيير القارئ أولاً
          await audioProvider.setReciter(_selectedReciter);
          // ثم تشغيل السورة
          await audioProvider.playSurah(_selectedSurah);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم بدء التشغيل'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في التشغيل: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    }
  }

  void _copyUrl() {
    if (_currentUrl != null) {
      // يمكن إضافة منطق نسخ الرابط هنا
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ الرابط'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _compareWithOthers() {
    // مقارنة القارئ الحالي مع قارئين آخرين
    final otherReciters = [
      'mishari_alafasy',
      'abdulbasit_abdulsamad',
      'maher_almuaiqly',
    ];
    final reciterToCompare = otherReciters.firstWhere(
      (r) => r != _selectedReciter,
      orElse: () => 'mishari_alafasy',
    );

    AudioUrlTester.compareReciters(
      _selectedReciter,
      reciterToCompare,
      _selectedSurah,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تمت المقارنة، راجع سجل التشخيص'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _testQuickSwitch() {
    // اختبار سريع لتبديل القراء
    final testReciters = [
      'mishari_alafasy',
      'abdulbasit_abdulsamad',
      'maher_almuaiqly',
      'nasser_alqatami',
    ];

    debugPrint('🚀 بدء اختبار التبديل السريع...');

    for (int i = 0; i < testReciters.length; i++) {
      final reciter = testReciters[i];
      final url = RealAudioService.getAudioUrl(reciter, 1);
      final info = RealAudioService.getReciterInfo(reciter);

      debugPrint('${i + 1}. ${info?['name']}: $url');
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم اختبار التبديل، راجع سجل التشخيص'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _updateUrl();
  }
}
