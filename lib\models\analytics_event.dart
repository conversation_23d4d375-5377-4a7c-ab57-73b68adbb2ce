import 'dart:convert';

/// نموذج حدث التحليلات
class AnalyticsEvent {
  final String name;
  final DateTime timestamp;
  final Map<String, dynamic> properties;
  final String? userId;
  final String? sessionId;

  const AnalyticsEvent({
    required this.name,
    required this.timestamp,
    this.properties = const {},
    this.userId,
    this.sessionId,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'timestamp': timestamp.toIso8601String(),
      'properties': properties,
      'userId': userId,
      'sessionId': sessionId,
    };
  }

  /// إنشاء من Map
  factory AnalyticsEvent.fromMap(Map<String, dynamic> map) {
    return AnalyticsEvent(
      name: map['name'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      properties: Map<String, dynamic>.from(map['properties'] ?? {}),
      userId: map['userId'],
      sessionId: map['sessionId'],
    );
  }

  /// تحويل إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء من JSON
  factory AnalyticsEvent.fromJson(String source) => 
      AnalyticsEvent.fromMap(json.decode(source));

  /// نسخ مع تعديل
  AnalyticsEvent copyWith({
    String? name,
    DateTime? timestamp,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
  }) {
    return AnalyticsEvent(
      name: name ?? this.name,
      timestamp: timestamp ?? this.timestamp,
      properties: properties ?? this.properties,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  @override
  String toString() {
    return 'AnalyticsEvent(name: $name, timestamp: $timestamp, properties: $properties)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is AnalyticsEvent &&
      other.name == name &&
      other.timestamp == timestamp &&
      other.userId == userId &&
      other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    return name.hashCode ^
      timestamp.hashCode ^
      userId.hashCode ^
      sessionId.hashCode;
  }
}

/// أنواع الأحداث المختلفة
enum EventType {
  appOpen,
  readingStart,
  readingEnd,
  surahRead,
  search,
  bookmark,
  audioPlay,
  settingsChange,
  featureUsed,
  error,
}

/// مساعد لإنشاء أحداث مختلفة
class AnalyticsEventBuilder {
  static AnalyticsEvent appOpen() {
    return AnalyticsEvent(
      name: 'app_open',
      timestamp: DateTime.now(),
      properties: {
        'app_version': '1.0.0',
        'platform': 'mobile',
      },
    );
  }

  static AnalyticsEvent readingSession({
    required Duration duration,
    required int ayahsRead,
    required int surahNumber,
  }) {
    return AnalyticsEvent(
      name: 'reading_session',
      timestamp: DateTime.now(),
      properties: {
        'duration_minutes': duration.inMinutes,
        'ayahs_read': ayahsRead,
        'surah_number': surahNumber,
        'reading_speed': ayahsRead / (duration.inMinutes > 0 ? duration.inMinutes : 1),
      },
    );
  }

  static AnalyticsEvent search({
    required String query,
    required int resultsCount,
    required String searchType,
  }) {
    return AnalyticsEvent(
      name: 'search',
      timestamp: DateTime.now(),
      properties: {
        'query_length': query.length,
        'results_count': resultsCount,
        'search_type': searchType,
        'has_results': resultsCount > 0,
      },
    );
  }

  static AnalyticsEvent bookmark({
    required int surahNumber,
    required int ayahNumber,
    required String action, // 'add' or 'remove'
  }) {
    return AnalyticsEvent(
      name: 'bookmark',
      timestamp: DateTime.now(),
      properties: {
        'surah_number': surahNumber,
        'ayah_number': ayahNumber,
        'action': action,
      },
    );
  }

  static AnalyticsEvent audioPlay({
    required String reciterName,
    required int surahNumber,
    required String action, // 'play', 'pause', 'stop'
  }) {
    return AnalyticsEvent(
      name: 'audio_play',
      timestamp: DateTime.now(),
      properties: {
        'reciter_name': reciterName,
        'surah_number': surahNumber,
        'action': action,
      },
    );
  }

  static AnalyticsEvent settingsChange({
    required String settingName,
    required dynamic oldValue,
    required dynamic newValue,
  }) {
    return AnalyticsEvent(
      name: 'settings_change',
      timestamp: DateTime.now(),
      properties: {
        'setting_name': settingName,
        'old_value': oldValue.toString(),
        'new_value': newValue.toString(),
      },
    );
  }

  static AnalyticsEvent featureUsed({
    required String featureName,
    Map<String, dynamic>? additionalData,
  }) {
    return AnalyticsEvent(
      name: 'feature_used',
      timestamp: DateTime.now(),
      properties: {
        'feature_name': featureName,
        ...?additionalData,
      },
    );
  }

  static AnalyticsEvent error({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
  }) {
    return AnalyticsEvent(
      name: 'error',
      timestamp: DateTime.now(),
      properties: {
        'error_type': errorType,
        'error_message': errorMessage,
        'stack_trace': stackTrace,
      },
    );
  }
}
