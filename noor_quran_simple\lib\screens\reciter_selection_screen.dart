import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/audio_provider.dart';

class ReciterSelectionScreen extends StatefulWidget {
  const ReciterSelectionScreen({super.key});

  @override
  State<ReciterSelectionScreen> createState() => _ReciterSelectionScreenState();
}

class _ReciterSelectionScreenState extends State<ReciterSelectionScreen> {
  String? _selectedReciter;
  bool _isChanging = false;

  final Map<String, Map<String, String>> _reciters = {
    'mishari_alafasy': {
      'name': 'مشاري بن راشد العفاسي',
      'country': 'الكويت',
      'description': 'قارئ مشهور بصوته العذب والتجويد المتقن'
    },
    'abdulbasit_abdulsamad': {
      'name': 'عبد الباسط عبد الصمد',
      'country': 'مصر',
      'description': 'من أشهر القراء في العالم الإسلامي'
    },
    'abdurrahman_sudais': {
      'name': 'عبد الرحمن السديس',
      'country': 'السعودية',
      'description': 'إمام الحرم المكي الشريف'
    },
    'ahmed_alajamy': {
      'name': 'أحمد بن علي العجمي',
      'country': 'السعودية',
      'description': 'قارئ معروف بتلاوته المؤثرة'
    },
    'saad_alghamdi': {
      'name': 'سعد الغامدي',
      'country': 'السعودية',
      'description': 'قارئ مشهور بصوته الجميل'
    },
    'yasser_aldosari': {
      'name': 'ياسر الدوسري',
      'country': 'السعودية',
      'description': 'إمام الحرم المكي الشريف'
    },
    'maher_almuaiqly': {
      'name': 'ماهر المعيقلي',
      'country': 'السعودية',
      'description': 'إمام الحرم المدني الشريف'
    },
    'mohamed_siddiq_alminshawi': {
      'name': 'محمد صديق المنشاوي',
      'country': 'مصر',
      'description': 'من أعظم قراء القرآن في التاريخ'
    },
    // القراء الموريتانيون
    'mohamed_ould_sidia': {
      'name': 'محمد ولد سيديا',
      'country': 'موريتانيا',
      'description': 'قارئ موريتاني مشهور'
    },
    'hussein_ould_ahmadou': {
      'name': 'حسين ولد أحمدو',
      'country': 'موريتانيا',
      'description': 'من القراء المعروفين في موريتانيا'
    },
    'mohamed_ould_mammi': {
      'name': 'محمد ولد مامي',
      'country': 'موريتانيا',
      'description': 'قارئ موريتاني متميز'
    },
    'abdullah_ould_abdulmalik': {
      'name': 'عبد الله ولد عبد المالك',
      'country': 'موريتانيا',
      'description': 'قارئ موريتاني مشهور'
    },
    'mohamed_salem_ould_adoud': {
      'name': 'محمد سالم ولد عدود',
      'country': 'موريتانيا',
      'description': 'من القراء المتميزين في موريتانيا'
    },
    'ahmadou_ould_lmarabit': {
      'name': 'أحمدو ولد المرابط',
      'country': 'موريتانيا',
      'description': 'قارئ موريتاني معروف'
    },
    'mohamed_fadel_ould_mohamed_amin': {
      'name': 'محمد فاضل ولد محمد أمين',
      'country': 'موريتانيا',
      'description': 'قارئ موريتاني متميز'
    },
  };

  @override
  void initState() {
    super.initState();
    final audioProvider = Provider.of<AudioProvider>(context, listen: false);
    _selectedReciter = audioProvider.selectedReciter;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار القارئ'),
        actions: [
          if (_selectedReciter != null)
            TextButton(
              onPressed: _isChanging ? null : _confirmSelection,
              child: _isChanging
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text(
                      'تأكيد',
                      style: TextStyle(color: Colors.white),
                    ),
            ),
        ],
      ),
      body: Column(
        children: [
          _buildCurrentReciterInfo(),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: _reciters.length,
              itemBuilder: (context, index) {
                final reciterKey = _reciters.keys.elementAt(index);
                final reciterInfo = _reciters[reciterKey]!;
                return _buildReciterTile(reciterKey, reciterInfo);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentReciterInfo() {
    final audioProvider = Provider.of<AudioProvider>(context);
    final currentReciterInfo = _reciters[audioProvider.selectedReciter];

    if (currentReciterInfo == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor,
            AppConstants.primaryColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'القارئ الحالي',
            style: TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            currentReciterInfo['name']!,
            style: const TextStyle(
              color: Colors.white,
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            currentReciterInfo['country']!,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReciterTile(String reciterKey, Map<String, String> reciterInfo) {
    final isSelected = _selectedReciter == reciterKey;
    final isCurrent = Provider.of<AudioProvider>(context).selectedReciter == reciterKey;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isSelected
                ? AppConstants.primaryColor
                : AppConstants.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            isCurrent ? Icons.volume_up : Icons.person,
            color: isSelected ? Colors.white : AppConstants.primaryColor,
          ),
        ),
        title: Text(
          reciterInfo['name']!,
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? AppConstants.primaryColor : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              reciterInfo['country']!,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              reciterInfo['description']!,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: isSelected
            ? const Icon(
                Icons.check_circle,
                color: AppConstants.primaryColor,
              )
            : isCurrent
                ? const Icon(
                    Icons.radio_button_checked,
                    color: AppConstants.accentColor,
                  )
                : const Icon(
                    Icons.radio_button_unchecked,
                    color: AppConstants.textSecondaryColor,
                  ),
        onTap: () {
          setState(() {
            _selectedReciter = reciterKey;
          });
        },
      ),
    );
  }

  Future<void> _confirmSelection() async {
    if (_selectedReciter == null) return;

    setState(() {
      _isChanging = true;
    });

    try {
      final audioProvider = Provider.of<AudioProvider>(context, listen: false);
      
      // عرض رسالة تأكيد
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد تغيير القارئ'),
          content: Text(
            'هل تريد تغيير القارئ إلى ${_reciters[_selectedReciter]!['name']}؟\n\nسيتم إيقاف التشغيل الحالي وإعادة تشغيله بصوت القارئ الجديد.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('تأكيد'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await audioProvider.setReciter(_selectedReciter!);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تغيير القارئ إلى ${_reciters[_selectedReciter]!['name']}'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تغيير القارئ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChanging = false;
        });
      }
    }
  }
}
