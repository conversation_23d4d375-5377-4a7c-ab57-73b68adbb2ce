/// نماذج البيانات للكتب الدينية
class ReligiousBook {
  final String id;
  final String title;
  final String titleArabic;
  final String author;
  final String authorArabic;
  final String description;
  final String category;
  final String coverImageUrl;
  final List<BookChapter> chapters;
  final BookType type;
  final bool isPoetic; // للكتب الشعرية مثل ابن عاشر والأخضري
  final int totalPages;
  final String language;
  final DateTime publishDate;
  final List<String> tags;

  ReligiousBook({
    required this.id,
    required this.title,
    required this.titleArabic,
    required this.author,
    required this.authorArabic,
    required this.description,
    required this.category,
    required this.coverImageUrl,
    required this.chapters,
    required this.type,
    this.isPoetic = false,
    required this.totalPages,
    this.language = 'ar',
    required this.publishDate,
    this.tags = const [],
  });

  factory ReligiousBook.fromJson(Map<String, dynamic> json) {
    return ReligiousBook(
      id: json['id'],
      title: json['title'],
      titleArabic: json['titleArabic'],
      author: json['author'],
      authorArabic: json['authorArabic'],
      description: json['description'],
      category: json['category'],
      coverImageUrl: json['coverImageUrl'],
      chapters: (json['chapters'] as List)
          .map((chapter) => BookChapter.fromJson(chapter))
          .toList(),
      type: BookType.values.firstWhere((e) => e.name == json['type']),
      isPoetic: json['isPoetic'] ?? false,
      totalPages: json['totalPages'],
      language: json['language'] ?? 'ar',
      publishDate: DateTime.parse(json['publishDate']),
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'titleArabic': titleArabic,
      'author': author,
      'authorArabic': authorArabic,
      'description': description,
      'category': category,
      'coverImageUrl': coverImageUrl,
      'chapters': chapters.map((chapter) => chapter.toJson()).toList(),
      'type': type.name,
      'isPoetic': isPoetic,
      'totalPages': totalPages,
      'language': language,
      'publishDate': publishDate.toIso8601String(),
      'tags': tags,
    };
  }
}

/// فصل من الكتاب
class BookChapter {
  final String id;
  final String title;
  final String titleArabic;
  final int chapterNumber;
  final List<BookSection> sections;
  final String? audioUrl;
  final int pageStart;
  final int pageEnd;

  BookChapter({
    required this.id,
    required this.title,
    required this.titleArabic,
    required this.chapterNumber,
    required this.sections,
    this.audioUrl,
    required this.pageStart,
    required this.pageEnd,
  });

  factory BookChapter.fromJson(Map<String, dynamic> json) {
    return BookChapter(
      id: json['id'],
      title: json['title'],
      titleArabic: json['titleArabic'],
      chapterNumber: json['chapterNumber'],
      sections: (json['sections'] as List)
          .map((section) => BookSection.fromJson(section))
          .toList(),
      audioUrl: json['audioUrl'],
      pageStart: json['pageStart'],
      pageEnd: json['pageEnd'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'titleArabic': titleArabic,
      'chapterNumber': chapterNumber,
      'sections': sections.map((section) => section.toJson()).toList(),
      'audioUrl': audioUrl,
      'pageStart': pageStart,
      'pageEnd': pageEnd,
    };
  }
}

/// قسم من الفصل
class BookSection {
  final String id;
  final String title;
  final String content;
  final String? explanation; // الشرح
  final String? audioUrl; // رابط الصوت
  final int sectionNumber;
  final SectionType type;
  final List<String> keywords;

  BookSection({
    required this.id,
    required this.title,
    required this.content,
    this.explanation,
    this.audioUrl,
    required this.sectionNumber,
    required this.type,
    this.keywords = const [],
  });

  factory BookSection.fromJson(Map<String, dynamic> json) {
    return BookSection(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      explanation: json['explanation'],
      audioUrl: json['audioUrl'],
      sectionNumber: json['sectionNumber'],
      type: SectionType.values.firstWhere((e) => e.name == json['type']),
      keywords: List<String>.from(json['keywords'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'explanation': explanation,
      'audioUrl': audioUrl,
      'sectionNumber': sectionNumber,
      'type': type.name,
      'keywords': keywords,
    };
  }
}

/// أنواع الكتب
enum BookType {
  tafsir, // تفسير
  fiqh, // فقه
  hadith, // حديث
  aqeedah, // عقيدة
  sirah, // سيرة
  poetry, // شعر ديني
  usul, // أصول
  nahw, // نحو
  balagha, // بلاغة
  other, // أخرى
}

/// أنواع الأقسام
enum SectionType {
  verse, // بيت شعري
  paragraph, // فقرة
  hadith, // حديث
  ayah, // آية
  explanation, // شرح
  example, // مثال
  rule, // قاعدة
}

/// فئات الكتب
class BookCategory {
  final String id;
  final String name;
  final String nameArabic;
  final String description;
  final String iconPath;
  final int bookCount;

  BookCategory({
    required this.id,
    required this.name,
    required this.nameArabic,
    required this.description,
    required this.iconPath,
    required this.bookCount,
  });

  factory BookCategory.fromJson(Map<String, dynamic> json) {
    return BookCategory(
      id: json['id'],
      name: json['name'],
      nameArabic: json['nameArabic'],
      description: json['description'],
      iconPath: json['iconPath'],
      bookCount: json['bookCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameArabic': nameArabic,
      'description': description,
      'iconPath': iconPath,
      'bookCount': bookCount,
    };
  }
}

/// إحصائيات المكتبة
class LibraryStats {
  final int totalBooks;
  final int totalChapters;
  final int totalSections;
  final int totalAudioFiles;
  final Map<BookType, int> booksByType;

  LibraryStats({
    required this.totalBooks,
    required this.totalChapters,
    required this.totalSections,
    required this.totalAudioFiles,
    required this.booksByType,
  });

  factory LibraryStats.fromJson(Map<String, dynamic> json) {
    return LibraryStats(
      totalBooks: json['totalBooks'],
      totalChapters: json['totalChapters'],
      totalSections: json['totalSections'],
      totalAudioFiles: json['totalAudioFiles'],
      booksByType: Map<BookType, int>.from(
        json['booksByType'].map((key, value) => MapEntry(
              BookType.values.firstWhere((e) => e.name == key),
              value,
            )),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalBooks': totalBooks,
      'totalChapters': totalChapters,
      'totalSections': totalSections,
      'totalAudioFiles': totalAudioFiles,
      'booksByType': booksByType.map((key, value) => MapEntry(key.name, value)),
    };
  }
}

/// درجة صحة الحديث
enum HadithGrade {
  sahih, // صحيح
  hasan, // حسن
  daif, // ضعيف
  mawdu, // موضوع
  unknown, // غير معروف
}

/// نوع التفسير
enum TafsirType {
  linguistic, // لغوي
  jurisprudential, // فقهي
  mystical, // صوفي
  modern, // معاصر
  classical, // كلاسيكي
}

/// نموذج الحديث الشريف
class Hadith {
  final String id;
  final String arabicText;
  final String englishText;
  final String narrator;
  final String narratorArabic;
  final String bookName;
  final String bookNameArabic;
  final int hadithNumber;
  final HadithGrade grade;
  final String? explanation;
  final String? explanationArabic;
  final List<String> keywords;
  final String? audioUrl;

  Hadith({
    required this.id,
    required this.arabicText,
    this.englishText = '',
    required this.narrator,
    required this.narratorArabic,
    required this.bookName,
    required this.bookNameArabic,
    required this.hadithNumber,
    required this.grade,
    this.explanation,
    this.explanationArabic,
    this.keywords = const [],
    this.audioUrl,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id']?.toString() ?? '',
      arabicText: json['hadith_arabic'] ?? json['text_arabic'] ?? '',
      englishText: json['hadith_english'] ?? json['text_english'] ?? '',
      narrator: json['narrator'] ?? json['rawi'] ?? '',
      narratorArabic: json['narrator_arabic'] ?? json['rawi_arabic'] ?? '',
      bookName: json['book_name'] ?? json['collection'] ?? '',
      bookNameArabic:
          json['book_name_arabic'] ?? json['collection_arabic'] ?? '',
      hadithNumber: json['hadith_number'] ?? json['number'] ?? 0,
      grade: _parseGrade(json['grade'] ?? json['authenticity']),
      explanation: json['explanation'],
      explanationArabic: json['explanation_arabic'] ?? json['sharh'],
      keywords: List<String>.from(json['keywords'] ?? []),
      audioUrl: json['audio_url'],
    );
  }

  static HadithGrade _parseGrade(String? grade) {
    if (grade == null) return HadithGrade.unknown;
    switch (grade.toLowerCase()) {
      case 'sahih':
      case 'صحيح':
        return HadithGrade.sahih;
      case 'hasan':
      case 'حسن':
        return HadithGrade.hasan;
      case 'daif':
      case 'ضعيف':
        return HadithGrade.daif;
      case 'mawdu':
      case 'موضوع':
        return HadithGrade.mawdu;
      default:
        return HadithGrade.unknown;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hadith_arabic': arabicText,
      'hadith_english': englishText,
      'narrator': narrator,
      'narrator_arabic': narratorArabic,
      'book_name': bookName,
      'book_name_arabic': bookNameArabic,
      'hadith_number': hadithNumber,
      'grade': grade.name,
      'explanation': explanation,
      'explanation_arabic': explanationArabic,
      'keywords': keywords,
      'audio_url': audioUrl,
    };
  }
}

/// نموذج كتاب الحديث
class HadithBook {
  final String id;
  final String name;
  final String nameArabic;
  final String author;
  final String authorArabic;
  final String description;
  final int totalHadiths;
  final List<HadithChapter> chapters;

  HadithBook({
    required this.id,
    required this.name,
    required this.nameArabic,
    required this.author,
    required this.authorArabic,
    required this.description,
    required this.totalHadiths,
    required this.chapters,
  });

  factory HadithBook.fromJson(Map<String, dynamic> json) {
    return HadithBook(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      nameArabic: json['name_arabic'] ?? '',
      author: json['author'] ?? '',
      authorArabic: json['author_arabic'] ?? '',
      description: json['description'] ?? '',
      totalHadiths: json['total_hadiths'] ?? 0,
      chapters: (json['chapters'] as List? ?? [])
          .map((chapter) => HadithChapter.fromJson(chapter))
          .toList(),
    );
  }
}

/// فصل في كتاب الحديث
class HadithChapter {
  final String id;
  final String title;
  final String titleArabic;
  final int chapterNumber;
  final List<Hadith> hadiths;

  HadithChapter({
    required this.id,
    required this.title,
    required this.titleArabic,
    required this.chapterNumber,
    required this.hadiths,
  });

  factory HadithChapter.fromJson(Map<String, dynamic> json) {
    return HadithChapter(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      titleArabic: json['title_arabic'] ?? '',
      chapterNumber: json['chapter_number'] ?? 0,
      hadiths: (json['hadiths'] as List? ?? [])
          .map((hadith) => Hadith.fromJson(hadith))
          .toList(),
    );
  }
}

/// نموذج التفسير
class TafsirVerse {
  final String id;
  final int surahNumber;
  final int ayahNumber;
  final String verseKey; // مثل "1:1"
  final String arabicText;
  final String tafsirText;
  final String tafsirTextArabic;
  final String tafsirName;
  final String tafsirNameArabic;
  final String authorName;
  final String authorNameArabic;
  final TafsirType type;
  final List<String> keywords;

  TafsirVerse({
    required this.id,
    required this.surahNumber,
    required this.ayahNumber,
    required this.verseKey,
    required this.arabicText,
    required this.tafsirText,
    required this.tafsirTextArabic,
    required this.tafsirName,
    required this.tafsirNameArabic,
    required this.authorName,
    required this.authorNameArabic,
    required this.type,
    this.keywords = const [],
  });

  factory TafsirVerse.fromJson(Map<String, dynamic> json) {
    return TafsirVerse(
      id: json['id']?.toString() ?? '',
      surahNumber: json['surah_number'] ?? 0,
      ayahNumber: json['ayah_number'] ?? 0,
      verseKey: json['verse_key'] ?? '',
      arabicText: json['arabic_text'] ?? '',
      tafsirText: json['tafsir_text'] ?? '',
      tafsirTextArabic: json['tafsir_text_arabic'] ?? '',
      tafsirName: json['tafsir_name'] ?? '',
      tafsirNameArabic: json['tafsir_name_arabic'] ?? '',
      authorName: json['author_name'] ?? '',
      authorNameArabic: json['author_name_arabic'] ?? '',
      type: _parseTafsirType(json['type']),
      keywords: List<String>.from(json['keywords'] ?? []),
    );
  }

  static TafsirType _parseTafsirType(String? type) {
    if (type == null) return TafsirType.classical;
    switch (type.toLowerCase()) {
      case 'linguistic':
      case 'لغوي':
        return TafsirType.linguistic;
      case 'jurisprudential':
      case 'فقهي':
        return TafsirType.jurisprudential;
      case 'mystical':
      case 'صوفي':
        return TafsirType.mystical;
      case 'modern':
      case 'معاصر':
        return TafsirType.modern;
      default:
        return TafsirType.classical;
    }
  }
}
