#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def test_quran_data():
    """اختبار بيانات القرآن"""
    
    try:
        with open('assets/data/quran_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("=== اختبار بيانات القرآن ===")
        print(f"عدد السور: {len(data['surahs'])}")
        
        for i, surah in enumerate(data['surahs'][:3], 1):  # أول 3 سور
            print(f"\nالسورة {i}:")
            print(f"  الرقم: {surah['number']}")
            print(f"  الاسم: {surah['name']}")
            print(f"  الاسم الإنجليزي: {surah['englishName']}")
            print(f"  عدد الآيات: {len(surah['ayahs'])}")
            
            if surah['ayahs']:
                print(f"  الآية الأولى: {surah['ayahs'][0]['text'][:50]}...")
                print(f"  الآية الأخيرة: {surah['ayahs'][-1]['text'][:50]}...")
        
        # إحصائيات عامة
        total_ayahs = sum(len(surah['ayahs']) for surah in data['surahs'])
        print(f"\nإجمالي الآيات: {total_ayahs}")
        
        # التحقق من صحة البيانات
        print("\n=== التحقق من صحة البيانات ===")
        for surah in data['surahs']:
            if not surah.get('name'):
                print(f"خطأ: السورة {surah['number']} بدون اسم")
            if not surah.get('ayahs'):
                print(f"خطأ: السورة {surah['number']} بدون آيات")
            else:
                for ayah in surah['ayahs']:
                    if not ayah.get('text'):
                        print(f"خطأ: آية فارغة في السورة {surah['number']}")
                        break
        
        print("تم الانتهاء من الاختبار!")
        
    except Exception as e:
        print(f"خطأ في اختبار البيانات: {e}")

if __name__ == "__main__":
    test_quran_data()
