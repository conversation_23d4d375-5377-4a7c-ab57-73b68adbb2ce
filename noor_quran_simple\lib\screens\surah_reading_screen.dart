import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/quran_models.dart';
import '../providers/quran_provider.dart';
import '../providers/audio_provider.dart';
import 'reciter_selection_screen.dart';

class SurahReadingScreen extends StatefulWidget {
  final int surahNumber;
  final String surahName;

  const SurahReadingScreen({
    super.key,
    required this.surahNumber,
    required this.surahName,
  });

  @override
  State<SurahReadingScreen> createState() => _SurahReadingScreenState();
}

class _SurahReadingScreenState extends State<SurahReadingScreen> {
  Surah? _surah;
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadSurah();
  }

  Future<void> _loadSurah() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    final surah = await quranProvider.getSurah(widget.surahNumber);

    setState(() {
      _surah = surah;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.surahName),
        actions: [
          Consumer<AudioProvider>(
            builder: (context, audioProvider, child) {
              return IconButton(
                icon: Icon(
                  audioProvider.isPlaying ? Icons.pause : Icons.play_arrow,
                ),
                onPressed: () {
                  if (audioProvider.isPlaying) {
                    audioProvider.pauseAudio();
                  } else {
                    audioProvider.playSurah(widget.surahNumber);
                  }
                },
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'bookmark_surah':
                  _bookmarkSurah();
                  break;
                case 'share_surah':
                  _shareSurah();
                  break;
                case 'change_reciter':
                  _showReciterDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'bookmark_surah',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_add),
                    SizedBox(width: 8),
                    Text('إضافة للمفضلة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share_surah',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'change_reciter',
                child: Row(
                  children: [
                    Icon(Icons.person),
                    SizedBox(width: 8),
                    Text('تغيير القارئ'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _surah == null
          ? _buildErrorState()
          : _buildSurahContent(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'فشل في تحميل السورة',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadSurah();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildSurahContent() {
    return Column(
      children: [
        _buildSurahHeader(),
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: _surah!.ayahs.length,
            itemBuilder: (context, index) {
              final ayah = _surah!.ayahs[index];
              return _buildAyahCard(ayah);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSurahHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor,
            AppConstants.primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          Text(
            _surah!.name,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _surah!.englishNameTranslation,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildInfoChip('${_surah!.numberOfAyahs} آية'),
              const SizedBox(width: 8),
              _buildInfoChip(
                _surah!.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: AppConstants.fontSizeSmall,
        ),
      ),
    );
  }

  Widget _buildAyahCard(Ayah ayah) {
    return Consumer2<QuranProvider, AudioProvider>(
      builder: (context, quranProvider, audioProvider, child) {
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${ayah.numberInSurah}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.play_arrow),
                          onPressed: () {
                            audioProvider.playAyah(
                              widget.surahNumber,
                              ayah.numberInSurah,
                            );
                          },
                        ),
                        FutureBuilder<bool>(
                          future: quranProvider.isBookmarked(
                            widget.surahNumber,
                            ayah.numberInSurah,
                          ),
                          builder: (context, snapshot) {
                            final isBookmarked = snapshot.data ?? false;
                            return IconButton(
                              icon: Icon(
                                isBookmarked
                                    ? Icons.bookmark
                                    : Icons.bookmark_border,
                                color: isBookmarked
                                    ? AppConstants.primaryColor
                                    : null,
                              ),
                              onPressed: () {
                                if (isBookmarked) {
                                  quranProvider.removeBookmark(
                                    widget.surahNumber,
                                    ayah.numberInSurah,
                                  );
                                } else {
                                  quranProvider.addBookmark(
                                    widget.surahNumber,
                                    ayah.numberInSurah,
                                    surahName: _surah!.name,
                                    ayahText: ayah.text,
                                  );
                                }
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  ayah.text,
                  style: const TextStyle(
                    fontSize: 24,
                    height: 2.0,
                    fontFamily: 'Amiri',
                  ),
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _bookmarkSurah() {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    // إضافة السورة كاملة للمفضلة (الآية الأولى كمرجع)
    quranProvider.addBookmark(
      widget.surahNumber,
      1,
      surahName: _surah!.name,
      ayahText: 'السورة كاملة',
    );

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم إضافة السورة للمفضلة')));
  }

  void _shareSurah() {
    // تنفيذ مشاركة السورة
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('مشاركة السورة')));
  }

  void _showReciterDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReciterSelectionScreen()),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
