class Tafsir {
  final int id;
  final String name;
  final String authorName;
  final String language;
  final String description;
  final bool isPopular;

  const Tafs<PERSON>({
    required this.id,
    required this.name,
    required this.authorName,
    required this.language,
    required this.description,
    this.isPopular = false,
  });

  factory Tafsir.fromJson(Map<String, dynamic> json) {
    return Tafsir(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      authorName: json['author_name'] ?? '',
      language: json['language'] ?? 'ar',
      description: json['description'] ?? '',
      isPopular: json['is_popular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'author_name': authorName,
      'language': language,
      'description': description,
      'is_popular': isPopular,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tafsir && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Tafsir(id: $id, name: $name, author: $authorName)';

  // Popular Tafsir sources
  static List<Tafsir> get popularTafsirs => [
    const Tafsir(
      id: 169,
      name: 'تفسير ابن كثير',
      authorName: 'ابن كثير',
      language: 'ar',
      description: 'تفسير القرآن العظيم لابن كثير',
      isPopular: true,
    ),
    const Tafsir(
      id: 164,
      name: 'تفسير الجلالين',
      authorName: 'الجلالان',
      language: 'ar',
      description: 'تفسير الجلالين المختصر',
      isPopular: true,
    ),
    const Tafsir(
      id: 168,
      name: 'تفسير القرطبي',
      authorName: 'القرطبي',
      language: 'ar',
      description: 'الجامع لأحكام القرآن',
      isPopular: true,
    ),
    const Tafsir(
      id: 167,
      name: 'تفسير الطبري',
      authorName: 'الطبري',
      language: 'ar',
      description: 'جامع البيان في تأويل القرآن',
      isPopular: true,
    ),
    const Tafsir(
      id: 171,
      name: 'تفسير السعدي',
      authorName: 'السعدي',
      language: 'ar',
      description: 'تيسير الكريم الرحمن في تفسير كلام المنان',
      isPopular: true,
    ),
  ];
}

class TafsirText {
  final int ayahNumber;
  final int surahNumber;
  final String text;
  final String tafsirName;
  final int tafsirId;
  final String? footnotes;

  const TafsirText({
    required this.ayahNumber,
    required this.surahNumber,
    required this.text,
    required this.tafsirName,
    required this.tafsirId,
    this.footnotes,
  });

  factory TafsirText.fromJson(Map<String, dynamic> json) {
    return TafsirText(
      ayahNumber: json['ayah_number'] ?? 0,
      surahNumber: json['surah_number'] ?? 0,
      text: json['text'] ?? '',
      tafsirName: json['tafsir_name'] ?? '',
      tafsirId: json['tafsir_id'] ?? 0,
      footnotes: json['footnotes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ayah_number': ayahNumber,
      'surah_number': surahNumber,
      'text': text,
      'tafsir_name': tafsirName,
      'tafsir_id': tafsirId,
      'footnotes': footnotes,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TafsirText &&
        other.ayahNumber == ayahNumber &&
        other.surahNumber == surahNumber &&
        other.tafsirId == tafsirId;
  }

  @override
  int get hashCode => Object.hash(ayahNumber, surahNumber, tafsirId);

  @override
  String toString() => 'TafsirText(${surahNumber}:${ayahNumber} - $tafsirName)';
}

class Translation {
  final int id;
  final String name;
  final String authorName;
  final String language;
  final String languageCode;
  final String direction;
  final bool isPopular;

  const Translation({
    required this.id,
    required this.name,
    required this.authorName,
    required this.language,
    required this.languageCode,
    required this.direction,
    this.isPopular = false,
  });

  factory Translation.fromJson(Map<String, dynamic> json) {
    return Translation(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      authorName: json['author_name'] ?? '',
      language: json['language'] ?? '',
      languageCode: json['language_code'] ?? 'en',
      direction: json['direction'] ?? 'ltr',
      isPopular: json['is_popular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'author_name': authorName,
      'language': language,
      'language_code': languageCode,
      'direction': direction,
      'is_popular': isPopular,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Translation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Translation(id: $id, name: $name, lang: $languageCode)';

  // Popular translations
  static List<Translation> get popularTranslations => [
    const Translation(
      id: 131,
      name: 'Dr. Mustafa Khattab, the Clear Quran',
      authorName: 'Dr. Mustafa Khattab',
      language: 'English',
      languageCode: 'en',
      direction: 'ltr',
      isPopular: true,
    ),
    const Translation(
      id: 85,
      name: 'Sahih International',
      authorName: 'Sahih International',
      language: 'English',
      languageCode: 'en',
      direction: 'ltr',
      isPopular: true,
    ),
    const Translation(
      id: 206,
      name: 'French Translation',
      authorName: 'Muhammad Hamidullah',
      language: 'Français',
      languageCode: 'fr',
      direction: 'ltr',
      isPopular: true,
    ),
    const Translation(
      id: 27,
      name: 'German Translation',
      authorName: 'Frank Bubenheim and Nadeem Elyas',
      language: 'Deutsch',
      languageCode: 'de',
      direction: 'ltr',
      isPopular: true,
    ),
    const Translation(
      id: 158,
      name: 'Spanish Translation',
      authorName: 'Abdel Ghani Melara Navio',
      language: 'Español',
      languageCode: 'es',
      direction: 'ltr',
      isPopular: true,
    ),
  ];
}

class TranslationText {
  final int ayahNumber;
  final int surahNumber;
  final String text;
  final String translationName;
  final int translationId;
  final String languageCode;

  const TranslationText({
    required this.ayahNumber,
    required this.surahNumber,
    required this.text,
    required this.translationName,
    required this.translationId,
    required this.languageCode,
  });

  factory TranslationText.fromJson(Map<String, dynamic> json) {
    return TranslationText(
      ayahNumber: json['ayah_number'] ?? 0,
      surahNumber: json['surah_number'] ?? 0,
      text: json['text'] ?? '',
      translationName: json['translation_name'] ?? '',
      translationId: json['translation_id'] ?? 0,
      languageCode: json['language_code'] ?? 'en',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ayah_number': ayahNumber,
      'surah_number': surahNumber,
      'text': text,
      'translation_name': translationName,
      'translation_id': translationId,
      'language_code': languageCode,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TranslationText &&
        other.ayahNumber == ayahNumber &&
        other.surahNumber == surahNumber &&
        other.translationId == translationId;
  }

  @override
  int get hashCode => Object.hash(ayahNumber, surahNumber, translationId);

  @override
  String toString() => 'TranslationText(${surahNumber}:${ayahNumber} - $languageCode)';
}
