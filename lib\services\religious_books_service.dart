import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';

/// خدمة جلب الكتب الدينية من noor-book.com
class ReligiousBooksService {
  static const String baseUrl = 'https://noor-book.com';
  static const String apiEndpoint = '/api/books';

  /// جلب جميع الكتب الدينية
  static Future<List<ReligiousBook>> getAllBooks() async {
    try {
      debugPrint('📚 جلب الكتب الدينية...');
      final books = _getLocalBooks();
      debugPrint('✅ تم جلب ${books.length} كتاب');
      return books;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الكتب: $e');
      return _getLocalBooks();
    }
  }

  /// جلب كتاب محدد بالمعرف
  static Future<ReligiousBook?> getBookById(String bookId) async {
    try {
      final books = await getAllBooks();
      return books.firstWhere((book) => book.id == bookId);
    } catch (e) {
      debugPrint('خطأ في جلب الكتاب: $e');
      return null;
    }
  }

  /// جلب الكتب حسب الفئة
  static Future<List<ReligiousBook>> getBooksByCategory(String category) async {
    try {
      final books = await getAllBooks();
      return books.where((book) => book.category == category).toList();
    } catch (e) {
      debugPrint('خطأ في جلب كتب الفئة: $e');
      return [];
    }
  }

  /// البحث في الكتب
  static Future<List<ReligiousBook>> searchBooks(String query) async {
    try {
      final books = await getAllBooks();
      return books.where((book) {
        return book.titleArabic.contains(query) ||
            book.authorArabic.contains(query) ||
            book.description.contains(query);
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      return [];
    }
  }

  /// جلب إحصائيات المكتبة
  static Future<LibraryStats> getLibraryStats() async {
    try {
      final books = await getAllBooks();

      int totalChapters = 0;
      int totalSections = 0;
      int totalAudioFiles = 0;
      Map<BookType, int> booksByType = {};

      for (final book in books) {
        totalChapters += book.chapters.length;

        // عد الأقسام والملفات الصوتية
        for (final chapter in book.chapters) {
          totalSections += chapter.sections.length;
          if (chapter.audioUrl != null) totalAudioFiles++;

          for (final section in chapter.sections) {
            if (section.audioUrl != null) totalAudioFiles++;
          }
        }

        // عد الكتب حسب النوع
        booksByType[book.type] = (booksByType[book.type] ?? 0) + 1;
      }

      return LibraryStats(
        totalBooks: books.length,
        totalChapters: totalChapters,
        totalSections: totalSections,
        totalAudioFiles: totalAudioFiles,
        booksByType: booksByType,
      );
    } catch (e) {
      debugPrint('خطأ في جلب الإحصائيات: $e');
      return LibraryStats(
        totalBooks: 0,
        totalChapters: 0,
        totalSections: 0,
        totalAudioFiles: 0,
        booksByType: {},
      );
    }
  }

  /// جلب فئات الكتب
  static Future<List<BookCategory>> getBookCategories() async {
    return [
      BookCategory(
        id: 'tafsir',
        name: 'Tafsir',
        nameArabic: 'كتب التفسير',
        description: 'كتب تفسير القرآن الكريم',
        iconPath: 'assets/icons/tafsir.png',
        bookCount: 2,
      ),
      BookCategory(
        id: 'fiqh',
        name: 'Fiqh',
        nameArabic: 'الكتب الفقهية',
        description: 'كتب الفقه الإسلامي',
        iconPath: 'assets/icons/fiqh.png',
        bookCount: 3,
      ),
      BookCategory(
        id: 'hadith',
        name: 'Hadith',
        nameArabic: 'الأحاديث النبوية',
        description: 'كتب الأحاديث النبوية الشريفة',
        iconPath: 'assets/icons/hadith.png',
        bookCount: 5,
      ),
      BookCategory(
        id: 'poetry',
        name: 'Religious Poetry',
        nameArabic: 'القراءة',
        description: 'المتون الشعرية الدينية',
        iconPath: 'assets/icons/poetry.png',
        bookCount: 22,
      ),
    ];
  }

  /// البيانات المحلية للكتب
  static List<ReligiousBook> _getLocalBooks() {
    return [
      // متن ابن عاشر (الأشعرية)
      ReligiousBook(
        id: 'ibn_asher',
        title: 'Ibn Asher\'s Matn',
        titleArabic: 'متن ابن عاشر في العقيدة الأشعرية',
        author: 'Ibn Asher',
        authorArabic: 'عبد الواحد بن عاشر الأندلسي',
        description: 'متن شعري في العقيدة الأشعرية والفقه المالكي والتصوف',
        category: 'poetry',
        coverImageUrl: 'assets/images/books/ibn_asher.jpg',
        type: BookType.aqeedah,
        isPoetic: true,
        totalPages: 45,
        publishDate: DateTime(1600),
        tags: ['عقيدة', 'أشعرية', 'فقه مالكي', 'تصوف'],
        chapters: _getIbnAsherChapters(),
      ),

      // متن الأخضري
      ReligiousBook(
        id: 'al_akhdari',
        title: 'Al-Akhdari\'s Matn',
        titleArabic: 'متن الأخضري في العبادات',
        author: 'Al-Akhdari',
        authorArabic: 'عبد الرحمن الأخضري',
        description: 'متن شعري في فقه العبادات على المذهب المالكي',
        category: 'poetry',
        coverImageUrl: 'assets/images/books/al_akhdari.jpg',
        type: BookType.fiqh,
        isPoetic: true,
        totalPages: 32,
        publishDate: DateTime(1500),
        tags: ['فقه', 'عبادات', 'مالكي'],
        chapters: _getAkhdariChapters(),
      ),

      // أسهل المسالك
      ReligiousBook(
        id: 'ashal_masalik',
        title: 'Ashal Al-Masalik',
        titleArabic: 'أسهل المسالك إلى مذهب الإمام مالك',
        author: 'Ahmad Zarruq',
        authorArabic: 'أحمد زروق',
        description: 'متن شعري في الفقه المالكي',
        category: 'poetry',
        coverImageUrl: 'assets/images/books/ashal_masalik.jpg',
        type: BookType.fiqh,
        isPoetic: true,
        totalPages: 28,
        publishDate: DateTime(1450),
        tags: ['فقه', 'مالكي', 'مسالك'],
        chapters: _getAshalMasalikChapters(),
      ),

      // قرة الأبصار
      ReligiousBook(
        id: 'qurrat_absar',
        title: 'Qurrat Al-Absar',
        titleArabic: 'قرة الأبصار في أخبار الصحابة الأخيار',
        author: 'Ibn Hajar',
        authorArabic: 'ابن حجر العسقلاني',
        description: 'متن شعري في سير الصحابة رضوان الله عليهم',
        category: 'poetry',
        coverImageUrl: 'assets/images/books/qurrat_absar.jpg',
        type: BookType.sirah,
        isPoetic: true,
        totalPages: 67,
        publishDate: DateTime(1400),
        tags: ['سيرة', 'صحابة', 'تاريخ'],
        chapters: _getQurratAbsarChapters(),
      ),

      // تفسير الجلالين
      ReligiousBook(
        id: 'tafsir_jalalayn',
        title: 'Tafsir Al-Jalalayn',
        titleArabic: 'تفسير الجلالين',
        author: 'Jalal ad-Din al-Mahalli & Jalal ad-Din as-Suyuti',
        authorArabic: 'جلال الدين المحلي وجلال الدين السيوطي',
        description: 'تفسير مختصر للقرآن الكريم',
        category: 'tafsir',
        coverImageUrl: 'assets/images/books/tafsir_jalalayn.jpg',
        type: BookType.tafsir,
        isPoetic: false,
        totalPages: 890,
        publishDate: DateTime(1460),
        tags: ['تفسير', 'قرآن', 'جلالين'],
        chapters: _getTafsirJalalynChapters(),
      ),
    ];
  }

  /// فصول متن ابن عاشر
  static List<BookChapter> _getIbnAsherChapters() {
    return [
      BookChapter(
        id: 'ibn_asher_ch1',
        title: 'Introduction',
        titleArabic: 'المقدمة',
        chapterNumber: 1,
        pageStart: 1,
        pageEnd: 5,
        audioUrl: 'assets/audio/ibn_asher/ch1.mp3',
        sections: [
          BookSection(
            id: 'ibn_asher_s1',
            title: 'Opening Verses',
            content: '''يقول راجي عفو رب غافر
عبد الواحد بن عاشر الأندلسي
أحمد ربي أولاً وأصلي
على النبي المصطفى المرسل

وبعد فالعون من الله المجيد
في نظم أبيات للأمر المفيد
جمعت فيها ما على كل مسلم
أن يعلم الأحكام فيما قد ألزم''',
            explanation:
                'هذه الأبيات الافتتاحية يذكر فيها الناظم اسمه ونسبه، ويبدأ بحمد الله والصلاة على النبي صلى الله عليه وسلم، ثم يبين غرضه من النظم وهو جمع ما يجب على كل مسلم معرفته.',
            audioUrl: 'assets/audio/ibn_asher/s1.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['مقدمة', 'حمد', 'صلاة', 'غرض النظم'],
          ),
        ],
      ),
      BookChapter(
        id: 'ibn_asher_ch2',
        title: 'Aqeedah Section',
        titleArabic: 'باب العقيدة',
        chapterNumber: 2,
        pageStart: 6,
        pageEnd: 15,
        audioUrl: 'assets/audio/ibn_asher/ch2.mp3',
        sections: [
          BookSection(
            id: 'ibn_asher_s2',
            title: 'Divine Attributes',
            content: '''واجب وجود إلهنا ووحدته
وكونه غنياً عن الخليقة
قدرته وإرادة وعلمه
وحياة وسمع وبصر وكلامه

وأنه ليس بجوهر ولا عرض
ولا جسم ولا جهة ولا غرض
وليس كالأشياء بل هو الواحد
القهار العزيز الماجد''',
            explanation:
                'هذه الأبيات تتحدث عن الصفات الواجبة لله تعالى وهي: الوجود والوحدانية والغنى عن الخليقة، والصفات السبع: القدرة والإرادة والعلم والحياة والسمع والبصر والكلام. كما تنفي عنه سبحانه صفات الأجسام.',
            audioUrl: 'assets/audio/ibn_asher/s2.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['صفات الله', 'توحيد', 'تنزيه'],
          ),
          BookSection(
            id: 'ibn_asher_s3',
            title: 'Prophets',
            content: '''ورسله حق وخير الرسل
محمد صلى عليه الله
صادق مصدوق أمين مؤتمن
بلغ الرسالة والهدى بين

وله معاجز ظاهرة
والقرآن أعظم معجزة باهرة
وهو كلام الله غير مخلوق
منزل على النبي المصدوق''',
            explanation:
                'هذه الأبيات تتحدث عن الإيمان بالرسل عليهم السلام، وأن خيرهم محمد صلى الله عليه وسلم، وأنه صادق مصدوق أمين، وأن له معاجز، وأن القرآن كلام الله غير مخلوق.',
            audioUrl: 'assets/audio/ibn_asher/s3.mp3',
            sectionNumber: 2,
            type: SectionType.verse,
            keywords: ['رسل', 'محمد', 'معاجز', 'قرآن'],
          ),
        ],
      ),
      BookChapter(
        id: 'ibn_asher_ch3',
        title: 'Fiqh Section',
        titleArabic: 'باب الفقه',
        chapterNumber: 3,
        pageStart: 16,
        pageEnd: 35,
        audioUrl: 'assets/audio/ibn_asher/ch3.mp3',
        sections: [
          BookSection(
            id: 'ibn_asher_s4',
            title: 'Purification',
            content: '''وطهارة الأحداث بالماء الطهور
إن وجد أو تيمم عند العذر
والنجس يطهر بالماء إذا زال
لونه وطعمه والريح بال

وفرض وضوء النية والغسل
للوجه واليدين مع المرفق
ومسح بعض الرأس والرجل
إلى الكعبين والترتيب جل''',
            explanation:
                'هذه الأبيات تتحدث عن أحكام الطهارة، وأن الأحداث تطهر بالماء الطهور أو التيمم عند العذر، وأن النجاسة تطهر بزوال لونها وطعمها وريحها، وتذكر فرائض الوضوء.',
            audioUrl: 'assets/audio/ibn_asher/s4.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['طهارة', 'وضوء', 'تيمم', 'نجاسة'],
          ),
        ],
      ),
    ];
  }

  /// فصول متن الأخضري
  static List<BookChapter> _getAkhdariChapters() {
    return [
      BookChapter(
        id: 'akhdari_ch1',
        title: 'Introduction',
        titleArabic: 'المقدمة',
        chapterNumber: 1,
        pageStart: 1,
        pageEnd: 3,
        audioUrl: 'assets/audio/akhdari/ch1.mp3',
        sections: [
          BookSection(
            id: 'akhdari_s1',
            title: 'Opening',
            content: '''يقول عبد الرحمن الأخضري
راجي عفو الواحد القهار
الحمد لله الذي تفضلا
علينا بالإسلام وكملا

نعمته بإرسال المختار
محمد خير الأنام الطاهر
وبعد فهذا مختصر مفيد
في الفقه للطالب المريد''',
            explanation:
                'مقدمة المتن يذكر فيها الناظم اسمه ويحمد الله ويصلي على النبي، ويبين أن هذا مختصر في الفقه للطالب المريد.',
            audioUrl: 'assets/audio/akhdari/s1.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['مقدمة', 'فقه', 'مختصر'],
          ),
        ],
      ),
      BookChapter(
        id: 'akhdari_ch2',
        title: 'Purification',
        titleArabic: 'باب الطهارة',
        chapterNumber: 2,
        pageStart: 4,
        pageEnd: 12,
        audioUrl: 'assets/audio/akhdari/ch2.mp3',
        sections: [
          BookSection(
            id: 'akhdari_s2',
            title: 'Types of Water',
            content: '''والماء طاهر مطهر إذا
لم تتغير أوصافه بذا
النجس أو بطاهر كثير
يمنع إطلاق اسم الماء عليه

وإن تغير بطاهر قليل
فطاهر مطهر جميل
والماء المستعمل في رفع الحدث
طاهر غير مطهر بحث''',
            explanation:
                'هذه الأبيات تبين أحكام المياه وأقسامها: الطاهر المطهر، والطاهر غير المطهر، والنجس. والماء يبقى طاهراً مطهراً ما لم تتغير أوصافه بنجاسة أو بطاهر كثير.',
            audioUrl: 'assets/audio/akhdari/s2.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['ماء', 'طهارة', 'نجاسة'],
          ),
          BookSection(
            id: 'akhdari_s3',
            title: 'Ablution',
            content: '''وفرض وضوء النية أولا
والغسل للوجه كما قد نقلا
واليدين مع المرفقين
والمسح لبعض الرأس مبين

والرجلين مع الكعبين
والترتيب بين هذين
والموالاة عند الذكر
والقدرة فاحفظ ما ذكر''',
            explanation:
                'هذه الأبيات تذكر فرائض الوضوء السبعة: النية، وغسل الوجه، وغسل اليدين مع المرفقين، ومسح بعض الرأس، وغسل الرجلين مع الكعبين، والترتيب، والموالاة.',
            audioUrl: 'assets/audio/akhdari/s3.mp3',
            sectionNumber: 2,
            type: SectionType.verse,
            keywords: ['وضوء', 'فرائض', 'ترتيب'],
          ),
        ],
      ),
    ];
  }

  /// فصول أسهل المسالك
  static List<BookChapter> _getAshalMasalikChapters() {
    return [
      BookChapter(
        id: 'ashal_ch1',
        title: 'Introduction',
        titleArabic: 'المقدمة',
        chapterNumber: 1,
        pageStart: 1,
        pageEnd: 4,
        audioUrl: 'assets/audio/ashal/ch1.mp3',
        sections: [
          BookSection(
            id: 'ashal_s1',
            title: 'Opening',
            content: '''بسم الله الرحمن الرحيم
الحمد لله رب العالمين
وصلى الله على سيد المرسلين
محمد وآله الطاهرين

وبعد فهذا أسهل المسالك
إلى مذهب الإمام مالك
نظمته للطالب المبتدي
ليسهل عليه طريق الهدى''',
            explanation:
                'مقدمة المتن في أسهل المسالك، يبين فيها الناظم أن هذا النظم لتسهيل مذهب الإمام مالك على الطالب المبتدي.',
            audioUrl: 'assets/audio/ashal/s1.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['مقدمة', 'مالك', 'مسالك'],
          ),
        ],
      ),
    ];
  }

  /// فصول قرة الأبصار
  static List<BookChapter> _getQurratAbsarChapters() {
    return [
      BookChapter(
        id: 'qurrat_ch1',
        title: 'Introduction',
        titleArabic: 'المقدمة',
        chapterNumber: 1,
        pageStart: 1,
        pageEnd: 5,
        audioUrl: 'assets/audio/qurrat/ch1.mp3',
        sections: [
          BookSection(
            id: 'qurrat_s1',
            title: 'Opening',
            content: '''الحمد لله الذي اختار لنبيه
أصحاباً كراماً أولي التقوى
رضي الله عنهم أجمعين
وجعلهم للمؤمنين قدوة

وبعد فهذا نظم في سير
الصحابة الكرام أهل الخير
قرة الأبصار في أخبارهم
وما حووه من عظيم فخارهم''',
            explanation:
                'مقدمة المتن في سير الصحابة، يحمد الله الذي اختار للنبي أصحاباً كراماً، ويبين أن هذا نظم في سيرهم وأخبارهم.',
            audioUrl: 'assets/audio/qurrat/s1.mp3',
            sectionNumber: 1,
            type: SectionType.verse,
            keywords: ['صحابة', 'سيرة', 'قرة الأبصار'],
          ),
        ],
      ),
    ];
  }

  /// فصول تفسير الجلالين
  static List<BookChapter> _getTafsirJalalynChapters() {
    return [
      BookChapter(
        id: 'jalalayn_ch1',
        title: 'Al-Fatiha',
        titleArabic: 'سورة الفاتحة',
        chapterNumber: 1,
        pageStart: 1,
        pageEnd: 2,
        audioUrl: 'assets/audio/jalalayn/ch1.mp3',
        sections: [
          BookSection(
            id: 'jalalayn_s1',
            title: 'Verse 1',
            content: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            explanation:
                'أي أبتدئ بسم الله مستعيناً به، والله علم على الذات الواجب الوجود، الرحمن الرحيم صفتان مشتقتان من الرحمة على وجه المبالغة.',
            audioUrl: 'assets/audio/jalalayn/s1.mp3',
            sectionNumber: 1,
            type: SectionType.ayah,
            keywords: ['بسملة', 'الله', 'رحمن', 'رحيم'],
          ),
        ],
      ),
    ];
  }
}
