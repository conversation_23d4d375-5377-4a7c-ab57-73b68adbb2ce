import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/recommendation.dart';
import '../models/surah.dart';
import '../services/progress_service.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  final ProgressService _progressService = ProgressService();
  final Random _random = Random();

  // Cache keys
  static const String _userPreferencesKey = 'user_preferences';
  static const String _recommendationsKey = 'cached_recommendations';
  static const String _insightsKey = 'reading_insights';

  // Generate personalized recommendations
  Future<List<Recommendation>> generateRecommendations({int limit = 5}) async {
    try {
      final preferences = await _getUserPreferences();
      final statistics = await _progressService.getStatistics();
      
      final recommendations = <Recommendation>[];

      // Generate different types of recommendations
      recommendations.addAll(await _generateReadingRecommendations(preferences, statistics));
      recommendations.addAll(await _generateSurahRecommendations(preferences, statistics));
      recommendations.addAll(await _generateReflectionRecommendations(preferences, statistics));
      recommendations.addAll(await _generateMemorizationRecommendations(preferences, statistics));

      // Sort by relevance score and priority
      recommendations.sort((a, b) {
        final priorityComparison = b.priority.index.compareTo(a.priority.index);
        if (priorityComparison != 0) return priorityComparison;
        return b.relevanceScore.compareTo(a.relevanceScore);
      });

      // Cache recommendations
      await _cacheRecommendations(recommendations.take(limit).toList());

      return recommendations.take(limit).toList();
    } catch (e) {
      debugPrint('Error generating recommendations: $e');
      return await _getCachedRecommendations();
    }
  }

  // Generate reading recommendations based on patterns
  Future<List<Recommendation>> _generateReadingRecommendations(
    UserPreference preferences,
    Map<String, dynamic> statistics,
  ) async {
    final recommendations = <Recommendation>[];
    final readingStreak = statistics['readingStreak'] ?? 0;
    final dailyProgress = statistics['dailyProgress'] ?? 0.0;

    // Streak maintenance recommendation
    if (readingStreak > 0 && readingStreak < 7) {
      recommendations.add(Recommendation(
        id: 'streak_${DateTime.now().millisecondsSinceEpoch}',
        type: RecommendationType.reading,
        title: 'حافظ على سلسلة القراءة',
        description: 'لديك سلسلة قراءة $readingStreak أيام. استمر في القراءة اليوم للحفاظ عليها!',
        priority: RecommendationPriority.high,
        createdAt: DateTime.now(),
        isPersonalized: true,
        relevanceScore: 0.9,
        metadata: {'streak_days': readingStreak},
      ));
    }

    // Daily goal recommendation
    if (dailyProgress < 0.5) {
      recommendations.add(Recommendation(
        id: 'daily_goal_${DateTime.now().millisecondsSinceEpoch}',
        type: RecommendationType.reading,
        title: 'أكمل هدفك اليومي',
        description: 'لم تكمل هدفك اليومي بعد. اقرأ بعض الآيات لتحقيق هدفك!',
        priority: RecommendationPriority.medium,
        createdAt: DateTime.now(),
        isPersonalized: true,
        relevanceScore: 0.7,
        metadata: {'daily_progress': dailyProgress},
      ));
    }

    return recommendations;
  }

  // Generate surah recommendations
  Future<List<Recommendation>> _generateSurahRecommendations(
    UserPreference preferences,
    Map<String, dynamic> statistics,
  ) async {
    final recommendations = <Recommendation>[];
    final readSurahs = await _progressService.getReadSurahs();

    // Recommend short surahs for beginners
    if (readSurahs.length < 10) {
      final shortSurahs = [114, 113, 112, 111, 110]; // Last 5 surahs
      final unreadShortSurahs = shortSurahs.where((s) => !readSurahs.contains(s)).toList();
      
      if (unreadShortSurahs.isNotEmpty) {
        final surahNumber = unreadShortSurahs[_random.nextInt(unreadShortSurahs.length)];
        recommendations.add(Recommendation(
          id: 'short_surah_$surahNumber',
          type: RecommendationType.surah,
          title: 'ابدأ بسورة قصيرة',
          description: 'جرب قراءة سورة قصيرة وسهلة الحفظ',
          priority: RecommendationPriority.medium,
          createdAt: DateTime.now(),
          isPersonalized: true,
          relevanceScore: 0.8,
          metadata: {'surah_number': surahNumber},
        ));
      }
    }

    // Recommend popular surahs
    final popularSurahs = [2, 18, 36, 55, 67]; // Al-Baqarah, Al-Kahf, Ya-Sin, Ar-Rahman, Al-Mulk
    final unreadPopularSurahs = popularSurahs.where((s) => !readSurahs.contains(s)).toList();
    
    if (unreadPopularSurahs.isNotEmpty) {
      final surahNumber = unreadPopularSurahs[_random.nextInt(unreadPopularSurahs.length)];
      recommendations.add(Recommendation(
        id: 'popular_surah_$surahNumber',
        type: RecommendationType.surah,
        title: 'سورة مميزة',
        description: 'اكتشف سورة من السور المحبوبة والمقروءة كثيراً',
        priority: RecommendationPriority.medium,
        createdAt: DateTime.now(),
        isPersonalized: false,
        relevanceScore: 0.6,
        metadata: {'surah_number': surahNumber},
      ));
    }

    return recommendations;
  }

  // Generate reflection recommendations
  Future<List<Recommendation>> _generateReflectionRecommendations(
    UserPreference preferences,
    Map<String, dynamic> statistics,
  ) async {
    final recommendations = <Recommendation>[];
    final currentHour = DateTime.now().hour;

    // Morning reflection
    if (currentHour >= 6 && currentHour <= 10) {
      recommendations.add(Recommendation(
        id: 'morning_reflection_${DateTime.now().day}',
        type: RecommendationType.reflection,
        title: 'تأمل صباحي',
        description: 'ابدأ يومك بتأمل آية من القرآن الكريم',
        priority: RecommendationPriority.medium,
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(hours: 4)),
        isPersonalized: true,
        relevanceScore: 0.7,
        metadata: {'time_of_day': 'morning'},
      ));
    }

    // Evening reflection
    if (currentHour >= 18 && currentHour <= 22) {
      recommendations.add(Recommendation(
        id: 'evening_reflection_${DateTime.now().day}',
        type: RecommendationType.reflection,
        title: 'تأمل مسائي',
        description: 'اختتم يومك بالتفكر في معاني القرآن',
        priority: RecommendationPriority.medium,
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(hours: 4)),
        isPersonalized: true,
        relevanceScore: 0.7,
        metadata: {'time_of_day': 'evening'},
      ));
    }

    return recommendations;
  }

  // Generate memorization recommendations
  Future<List<Recommendation>> _generateMemorizationRecommendations(
    UserPreference preferences,
    Map<String, dynamic> statistics,
  ) async {
    final recommendations = <Recommendation>[];
    final readSurahs = await _progressService.getReadSurahs();

    // Recommend memorization for frequently read surahs
    if (readSurahs.length >= 5) {
      recommendations.add(Recommendation(
        id: 'memorization_${DateTime.now().millisecondsSinceEpoch}',
        type: RecommendationType.memorization,
        title: 'ابدأ الحفظ',
        description: 'لديك تقدم جيد في القراءة. حان الوقت لبدء حفظ بعض السور!',
        priority: RecommendationPriority.low,
        createdAt: DateTime.now(),
        isPersonalized: true,
        relevanceScore: 0.5,
        metadata: {'read_surahs_count': readSurahs.length},
      ));
    }

    return recommendations;
  }

  // Generate reading insights
  Future<List<ReadingInsight>> generateInsights() async {
    try {
      final statistics = await _progressService.getStatistics();
      final insights = <ReadingInsight>[];

      // Reading progress insight
      final completionPercentage = statistics['completionPercentage'] ?? 0.0;
      if (completionPercentage > 0) {
        insights.add(ReadingInsight(
          id: 'progress_insight_${DateTime.now().millisecondsSinceEpoch}',
          title: 'تقدمك في القراءة',
          description: 'لقد أكملت ${completionPercentage.toStringAsFixed(1)}% من القرآن الكريم',
          category: 'progress',
          data: {'completion_percentage': completionPercentage},
          generatedAt: DateTime.now(),
          confidence: 1.0,
        ));
      }

      // Reading streak insight
      final readingStreak = statistics['readingStreak'] ?? 0;
      if (readingStreak > 0) {
        insights.add(ReadingInsight(
          id: 'streak_insight_${DateTime.now().millisecondsSinceEpoch}',
          title: 'سلسلة القراءة',
          description: 'لديك سلسلة قراءة مستمرة لمدة $readingStreak ${readingStreak == 1 ? 'يوم' : 'أيام'}',
          category: 'streak',
          data: {'streak_days': readingStreak},
          generatedAt: DateTime.now(),
          confidence: 1.0,
        ));
      }

      // Reading time insight
      final totalTime = statistics['totalReadingTime'] ?? 0;
      if (totalTime > 0) {
        insights.add(ReadingInsight(
          id: 'time_insight_${DateTime.now().millisecondsSinceEpoch}',
          title: 'وقت القراءة',
          description: 'قضيت $totalTime دقيقة في قراءة القرآن الكريم',
          category: 'time',
          data: {'total_minutes': totalTime},
          generatedAt: DateTime.now(),
          confidence: 1.0,
        ));
      }

      await _cacheInsights(insights);
      return insights;
    } catch (e) {
      debugPrint('Error generating insights: $e');
      return await _getCachedInsights();
    }
  }

  // User preferences management
  Future<UserPreference> _getUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefsJson = prefs.getString(_userPreferencesKey);
      
      if (prefsJson != null) {
        return UserPreference.fromJson(json.decode(prefsJson));
      }
      
      // Create default preferences
      return UserPreference(
        userId: 'default_user',
        favoriteTopics: [],
        favoriteSurahs: [],
        readingPatterns: {},
        interactionCounts: {},
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error loading user preferences: $e');
      return UserPreference(
        userId: 'default_user',
        favoriteTopics: [],
        favoriteSurahs: [],
        readingPatterns: {},
        interactionCounts: {},
        lastUpdated: DateTime.now(),
      );
    }
  }

  // Cache management
  Future<void> _cacheRecommendations(List<Recommendation> recommendations) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recommendationsJson = recommendations.map((r) => r.toJson()).toList();
      await prefs.setString(_recommendationsKey, json.encode(recommendationsJson));
    } catch (e) {
      debugPrint('Error caching recommendations: $e');
    }
  }

  Future<List<Recommendation>> _getCachedRecommendations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recommendationsJson = prefs.getString(_recommendationsKey);
      
      if (recommendationsJson != null) {
        final List<dynamic> decoded = json.decode(recommendationsJson);
        return decoded.map((r) => Recommendation.fromJson(r)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error loading cached recommendations: $e');
      return [];
    }
  }

  Future<void> _cacheInsights(List<ReadingInsight> insights) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final insightsJson = insights.map((i) => i.toJson()).toList();
      await prefs.setString(_insightsKey, json.encode(insightsJson));
    } catch (e) {
      debugPrint('Error caching insights: $e');
    }
  }

  Future<List<ReadingInsight>> _getCachedInsights() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final insightsJson = prefs.getString(_insightsKey);
      
      if (insightsJson != null) {
        final List<dynamic> decoded = json.decode(insightsJson);
        return decoded.map((i) => ReadingInsight.fromJson(i)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error loading cached insights: $e');
      return [];
    }
  }

  // Clear all AI data
  Future<void> clearAIData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userPreferencesKey);
      await prefs.remove(_recommendationsKey);
      await prefs.remove(_insightsKey);
    } catch (e) {
      debugPrint('Error clearing AI data: $e');
    }
  }
}
