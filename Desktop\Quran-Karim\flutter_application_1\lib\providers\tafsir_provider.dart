import 'package:flutter/material.dart';
import '../models/tafsir.dart';
import '../services/tafsir_service.dart';

class TafsirProvider extends ChangeNotifier {
  final TafsirService _tafsirService = TafsirService();

  // Current selections
  Tafsir? _selectedTafsir;
  Translation? _selectedTranslation;

  // Enable/disable states
  bool _isTafsirEnabled = false;
  bool _isTranslationEnabled = false;

  // Loading states
  bool _isLoadingTafsir = false;
  bool _isLoadingTranslation = false;

  // Current texts
  TafsirText? _currentTafsirText;
  TranslationText? _currentTranslationText;

  // Error messages
  String? _tafsirError;
  String? _translationError;

  // Getters
  Tafsir? get selectedTafsir => _selectedTafsir;
  Translation? get selectedTranslation => _selectedTranslation;
  bool get isTafsirEnabled => _isTafsirEnabled;
  bool get isTranslationEnabled => _isTranslationEnabled;
  bool get isLoadingTafsir => _isLoadingTafsir;
  bool get isLoadingTranslation => _isLoadingTranslation;
  TafsirText? get currentTafsirText => _currentTafsirText;
  TranslationText? get currentTranslationText => _currentTranslationText;
  String? get tafsirError => _tafsirError;
  String? get translationError => _translationError;

  List<Tafsir> get availableTafsirs => _tafsirService.getAvailableTafsirs();
  List<Translation> get availableTranslations =>
      _tafsirService.getAvailableTranslations();

  // Initialize provider
  Future<void> initialize() async {
    await _loadSettings();
    await _loadSelectedTafsir();
    await _loadSelectedTranslation();
  }

  // Load settings from storage
  Future<void> _loadSettings() async {
    try {
      _isTafsirEnabled = await _tafsirService.isTafsirEnabled();
      _isTranslationEnabled = await _tafsirService.isTranslationEnabled();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading tafsir settings: $e');
    }
  }

  // Load selected tafsir
  Future<void> _loadSelectedTafsir() async {
    try {
      final tafsirId = await _tafsirService.getSelectedTafsirId();
      if (tafsirId != null) {
        final tafsirs = availableTafsirs.where((t) => t.id == tafsirId);
        _selectedTafsir = tafsirs.isNotEmpty ? tafsirs.first : null;
      } else {
        // Set default tafsir (Ibn Kathir)
        _selectedTafsir = availableTafsirs.firstWhere(
          (t) => t.id == 169,
          orElse: () => availableTafsirs.first,
        );
        await _tafsirService.setSelectedTafsirId(_selectedTafsir!.id);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading selected tafsir: $e');
    }
  }

  // Load selected translation
  Future<void> _loadSelectedTranslation() async {
    try {
      final translationId = await _tafsirService.getSelectedTranslationId();
      if (translationId != null) {
        final translations = availableTranslations.where(
          (t) => t.id == translationId,
        );
        _selectedTranslation = translations.isNotEmpty
            ? translations.first
            : null;
      } else {
        // Set default translation (Clear Quran)
        _selectedTranslation = availableTranslations.firstWhere(
          (t) => t.id == 131,
          orElse: () => availableTranslations.first,
        );
        await _tafsirService.setSelectedTranslationId(_selectedTranslation!.id);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading selected translation: $e');
    }
  }

  // Set selected tafsir
  Future<void> setSelectedTafsir(Tafsir tafsir) async {
    try {
      _selectedTafsir = tafsir;
      await _tafsirService.setSelectedTafsirId(tafsir.id);
      _currentTafsirText = null; // Clear current text to force reload
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting selected tafsir: $e');
    }
  }

  // Set selected translation
  Future<void> setSelectedTranslation(Translation translation) async {
    try {
      _selectedTranslation = translation;
      await _tafsirService.setSelectedTranslationId(translation.id);
      _currentTranslationText = null; // Clear current text to force reload
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting selected translation: $e');
    }
  }

  // Toggle tafsir enabled state
  Future<void> toggleTafsirEnabled() async {
    try {
      _isTafsirEnabled = !_isTafsirEnabled;
      await _tafsirService.setTafsirEnabled(_isTafsirEnabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling tafsir enabled: $e');
    }
  }

  // Toggle translation enabled state
  Future<void> toggleTranslationEnabled() async {
    try {
      _isTranslationEnabled = !_isTranslationEnabled;
      await _tafsirService.setTranslationEnabled(_isTranslationEnabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error toggling translation enabled: $e');
    }
  }

  // Load tafsir for specific ayah
  Future<void> loadTafsirForAyah(int surahNumber, int ayahNumber) async {
    if (!_isTafsirEnabled || _selectedTafsir == null) {
      _currentTafsirText = null;
      notifyListeners();
      return;
    }

    try {
      _isLoadingTafsir = true;
      _tafsirError = null;
      notifyListeners();

      final tafsirText = await _tafsirService.getTafsirForAyah(
        surahNumber,
        ayahNumber,
        tafsirId: _selectedTafsir!.id,
      );

      _currentTafsirText = tafsirText;
      _isLoadingTafsir = false;
      notifyListeners();
    } catch (e) {
      _isLoadingTafsir = false;
      _tafsirError = 'فشل في تحميل التفسير: ${e.toString()}';
      notifyListeners();
    }
  }

  // Load translation for specific ayah
  Future<void> loadTranslationForAyah(int surahNumber, int ayahNumber) async {
    if (!_isTranslationEnabled || _selectedTranslation == null) {
      _currentTranslationText = null;
      notifyListeners();
      return;
    }

    try {
      _isLoadingTranslation = true;
      _translationError = null;
      notifyListeners();

      final translationText = await _tafsirService.getTranslationForAyah(
        surahNumber,
        ayahNumber,
        translationId: _selectedTranslation!.id,
      );

      _currentTranslationText = translationText;
      _isLoadingTranslation = false;
      notifyListeners();
    } catch (e) {
      _isLoadingTranslation = false;
      _translationError = 'فشل في تحميل الترجمة: ${e.toString()}';
      notifyListeners();
    }
  }

  // Load both tafsir and translation for ayah
  Future<void> loadContentForAyah(int surahNumber, int ayahNumber) async {
    await Future.wait([
      loadTafsirForAyah(surahNumber, ayahNumber),
      loadTranslationForAyah(surahNumber, ayahNumber),
    ]);
  }

  // Clear current content
  void clearCurrentContent() {
    _currentTafsirText = null;
    _currentTranslationText = null;
    _tafsirError = null;
    _translationError = null;
    notifyListeners();
  }

  // Clear cache
  void clearCache() {
    _tafsirService.clearCache();
  }

  // Get cache info
  String getCacheInfo() {
    final cacheSize = _tafsirService.getCacheSize();
    return 'عدد النصوص المحفوظة: $cacheSize';
  }

  // Check if content is available for ayah
  bool hasContentForAyah(int surahNumber, int ayahNumber) {
    if (_currentTafsirText != null &&
        _currentTafsirText!.surahNumber == surahNumber &&
        _currentTafsirText!.ayahNumber == ayahNumber) {
      return true;
    }

    if (_currentTranslationText != null &&
        _currentTranslationText!.surahNumber == surahNumber &&
        _currentTranslationText!.ayahNumber == ayahNumber) {
      return true;
    }

    return false;
  }

  // Get content summary
  String getContentSummary() {
    final parts = <String>[];

    if (_isTafsirEnabled && _selectedTafsir != null) {
      parts.add('التفسير: ${_selectedTafsir!.name}');
    }

    if (_isTranslationEnabled && _selectedTranslation != null) {
      parts.add('الترجمة: ${_selectedTranslation!.language}');
    }

    return parts.isEmpty ? 'لا توجد محتويات إضافية' : parts.join(' • ');
  }
}
