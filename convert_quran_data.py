#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests

def convert_quran_data():
    """تحويل بيانات القرآن من المصدر الخارجي إلى التنسيق المطلوب"""
    
    print("🔄 بدء تحويل بيانات القرآن الكريم...")
    
    try:
        # تحميل البيانات من المصدر
        print("📥 تحميل البيانات من المصدر...")
        url = "https://cdn.jsdelivr.net/npm/quran-json@3.1.2/dist/quran.json"
        response = requests.get(url)
        
        if response.status_code != 200:
            print(f"❌ فشل في تحميل البيانات: {response.status_code}")
            return
        
        source_data = response.json()
        print(f"✅ تم تحميل {len(source_data)} سورة")
        
        # تحويل البيانات إلى التنسيق المطلوب
        print("🔄 تحويل البيانات...")
        converted_data = {
            "surahs": []
        }
        
        ayah_counter = 1  # عداد الآيات الإجمالي
        
        for surah_data in source_data:
            # تحويل بيانات السورة
            converted_surah = {
                "number": surah_data["id"],
                "name": surah_data["name"],
                "englishName": surah_data["transliteration"],
                "englishNameTranslation": surah_data["transliteration"],
                "revelationType": "Meccan" if surah_data["type"] == "meccan" else "Medinan",
                "numberOfAyahs": surah_data["total_verses"],
                "ayahs": []
            }
            
            # تحويل الآيات
            for verse_data in surah_data["verses"]:
                converted_ayah = {
                    "number": ayah_counter,
                    "text": verse_data["text"],
                    "numberInSurah": verse_data["id"],
                    "surahNumber": surah_data["id"],
                    "juz": 1,  # قيمة افتراضية
                    "manzil": 1,  # قيمة افتراضية
                    "page": 1,  # قيمة افتراضية
                    "ruku": 1,  # قيمة افتراضية
                    "hizbQuarter": 1,  # قيمة افتراضية
                    "sajda": False  # قيمة افتراضية
                }
                
                converted_surah["ayahs"].append(converted_ayah)
                ayah_counter += 1
            
            converted_data["surahs"].append(converted_surah)
            print(f"✅ تم تحويل السورة {converted_surah['number']}: {converted_surah['name']} ({len(converted_surah['ayahs'])} آية)")
        
        # حفظ البيانات المحولة
        print("💾 حفظ البيانات المحولة...")
        with open('assets/data/quran_text.json', 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)
        
        print(f"🎉 تم تحويل وحفظ {len(converted_data['surahs'])} سورة بنجاح!")
        
        # إحصائيات
        total_ayahs = sum(len(surah['ayahs']) for surah in converted_data['surahs'])
        print(f"📊 إجمالي الآيات: {total_ayahs}")
        print(f"📊 إجمالي السور: {len(converted_data['surahs'])}")
        
    except Exception as e:
        print(f"❌ خطأ في التحويل: {e}")

if __name__ == "__main__":
    convert_quran_data()
