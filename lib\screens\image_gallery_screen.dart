import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ImageGalleryScreen extends StatefulWidget {
  const ImageGalleryScreen({super.key});

  @override
  State<ImageGalleryScreen> createState() => _ImageGalleryScreenState();
}

class _ImageGalleryScreenState extends State<ImageGalleryScreen> {
  final List<ImageCategory> _categories = [
    ImageCategory(
      id: 'islamic_art',
      name: 'الفن الإسلامي',
      description: 'مجموعة من الصور للفن الإسلامي والخط العربي',
      icon: Icons.palette,
      color: Colors.blue,
      images: [
        ImageItem(
          id: 'calligraphy_1',
          title: 'خط عربي - بسم الله',
          description: 'خط عربي جميل لبسم الله الرحمن الرحيم',
          url: 'assets/images/calligraphy_bismillah.jpg',
          category: 'خط عربي',
        ),
        ImageItem(
          id: 'mosque_1',
          title: 'المسجد الحرام',
          description: 'صورة للمسجد الحرام في مكة المكرمة',
          url: 'assets/images/masjid_haram.jpg',
          category: 'مساجد',
        ),
        ImageItem(
          id: 'quran_1',
          title: 'مصحف شريف',
          description: 'صورة لمصحف شريف مفتوح',
          url: 'assets/images/quran_open.jpg',
          category: 'قرآن',
        ),
      ],
    ),
    ImageCategory(
      id: 'mosques',
      name: 'المساجد',
      description: 'صور للمساجد المقدسة والتاريخية',
      icon: Icons.mosque,
      color: Colors.green,
      images: [
        ImageItem(
          id: 'masjid_nabawi',
          title: 'المسجد النبوي',
          description: 'المسجد النبوي الشريف في المدينة المنورة',
          url: 'assets/images/masjid_nabawi.jpg',
          category: 'مساجد',
        ),
        ImageItem(
          id: 'al_aqsa',
          title: 'المسجد الأقصى',
          description: 'المسجد الأقصى المبارك في القدس',
          url: 'assets/images/al_aqsa.jpg',
          category: 'مساجد',
        ),
      ],
    ),
    ImageCategory(
      id: 'calligraphy',
      name: 'الخط العربي',
      description: 'مجموعة من أجمل الخطوط العربية',
      icon: Icons.brush,
      color: Colors.amber,
      images: [
        ImageItem(
          id: 'ayat_kursi',
          title: 'آية الكرسي',
          description: 'آية الكرسي بخط عربي جميل',
          url: 'assets/images/ayat_kursi.jpg',
          category: 'خط عربي',
        ),
        ImageItem(
          id: 'shahada',
          title: 'الشهادة',
          description: 'لا إله إلا الله محمد رسول الله',
          url: 'assets/images/shahada.jpg',
          category: 'خط عربي',
        ),
      ],
    ),
  ];

  String _selectedCategory = 'all';
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('فهرس الصور'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفئات
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                _buildCategoryChip('all', 'الكل', Icons.grid_view, Colors.grey),
                ..._categories.map((category) => _buildCategoryChip(
                  category.id,
                  category.name,
                  category.icon,
                  category.color,
                )),
              ],
            ),
          ),
          
          // شريط البحث
          if (_searchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'نتائج البحث عن: "$_searchQuery"',
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () => setState(() => _searchQuery = ''),
                  ),
                ],
              ),
            ),
          
          // شبكة الصور
          Expanded(
            child: _buildImageGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String id, String name, IconData icon, Color color) {
    final isSelected = _selectedCategory == id;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: isSelected ? Colors.white : color),
            const SizedBox(width: 4),
            Text(name),
          ],
        ),
        onSelected: (selected) {
          setState(() {
            _selectedCategory = id;
            _searchQuery = '';
          });
        },
        selectedColor: color,
        checkmarkColor: Colors.white,
      ),
    );
  }

  Widget _buildImageGrid() {
    final filteredImages = _getFilteredImages();
    
    if (filteredImages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد صور في هذه الفئة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: filteredImages.length,
      itemBuilder: (context, index) {
        final image = filteredImages[index];
        return _buildImageCard(image);
      },
    );
  }

  Widget _buildImageCard(ImageItem image) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () => _showImageDetails(image),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.blue[100]!,
                      Colors.blue[300]!,
                    ],
                  ),
                ),
                child: Icon(
                  Icons.image,
                  size: 48,
                  color: Colors.blue[600],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      image.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      image.category,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ImageItem> _getFilteredImages() {
    List<ImageItem> allImages = [];
    
    for (final category in _categories) {
      allImages.addAll(category.images);
    }
    
    if (_selectedCategory != 'all') {
      final selectedCat = _categories.firstWhere((cat) => cat.id == _selectedCategory);
      allImages = selectedCat.images;
    }
    
    if (_searchQuery.isNotEmpty) {
      allImages = allImages.where((image) =>
        image.title.contains(_searchQuery) ||
        image.description.contains(_searchQuery) ||
        image.category.contains(_searchQuery)
      ).toList();
    }
    
    return allImages;
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الصور'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'ابحث عن صورة...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (value) {
            setState(() {
              _searchQuery = value;
              _selectedCategory = 'all';
            });
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showImageDetails(ImageItem image) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.blue[100]!,
                      Colors.blue[300]!,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.image,
                  size: 64,
                  color: Colors.blue[600],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                image.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                image.description,
                style: TextStyle(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    label: const Text('إغلاق'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: تنفيذ مشاركة الصورة
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('سيتم إضافة المشاركة قريباً')),
                      );
                    },
                    icon: const Icon(Icons.share),
                    label: const Text('مشاركة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// نماذج البيانات
class ImageCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final List<ImageItem> images;

  ImageCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.images,
  });
}

class ImageItem {
  final String id;
  final String title;
  final String description;
  final String url;
  final String category;

  ImageItem({
    required this.id,
    required this.title,
    required this.description,
    required this.url,
    required this.category,
  });
}
