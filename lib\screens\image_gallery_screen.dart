import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_constants.dart';

class ImageGalleryScreen extends StatefulWidget {
  const ImageGalleryScreen({super.key});

  @override
  State<ImageGalleryScreen> createState() => _ImageGalleryScreenState();
}

class _ImageGalleryScreenState extends State<ImageGalleryScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final Set<String> _favoriteImages = <String>{};
  bool _isGridView = true;
  final List<ImageCategory> _categories = [
    ImageCategory(
      id: 'islamic_art',
      name: 'الفن الإسلامي',
      description: 'مجموعة من الصور للفن الإسلامي والخط العربي',
      icon: Icons.palette,
      color: Colors.blue,
      images: [
        ImageItem(
          id: 'calligraphy_1',
          title: 'خط عربي - بسم الله',
          description: 'خط عربي جميل لبسم الله الرحمن الرحيم',
          url: 'assets/images/calligraphy_bismillah.jpg',
          category: 'خط عربي',
          tags: ['خط عربي', 'بسملة', 'فن إسلامي'],
          downloadCount: 1250,
        ),
        ImageItem(
          id: 'geometric_1',
          title: 'زخارف هندسية إسلامية',
          description: 'نماذج من الزخارف الهندسية الإسلامية التقليدية',
          url: 'assets/images/geometric_patterns.jpg',
          category: 'زخارف',
          tags: ['زخارف', 'هندسة', 'فن إسلامي'],
          downloadCount: 890,
        ),
        ImageItem(
          id: 'arabesque_1',
          title: 'أرابيسك إسلامي',
          description: 'تصاميم أرابيسك تقليدية بألوان ذهبية',
          url: 'assets/images/arabesque.jpg',
          category: 'أرابيسك',
          tags: ['أرابيسك', 'ذهبي', 'تقليدي'],
          downloadCount: 675,
        ),
      ],
    ),
    ImageCategory(
      id: 'mosques',
      name: 'المساجد',
      description: 'صور للمساجد المقدسة والتاريخية',
      icon: Icons.mosque,
      color: Colors.green,
      images: [
        ImageItem(
          id: 'masjid_haram',
          title: 'المسجد الحرام',
          description: 'المسجد الحرام في مكة المكرمة مع الكعبة المشرفة',
          url: 'assets/images/masjid_haram.jpg',
          category: 'مساجد',
          tags: ['مكة', 'الكعبة', 'الحرم'],
          downloadCount: 2150,
          author: 'مصور إسلامي',
          rating: 4.9,
        ),
        ImageItem(
          id: 'masjid_nabawi',
          title: 'المسجد النبوي',
          description: 'المسجد النبوي الشريف في المدينة المنورة',
          url: 'assets/images/masjid_nabawi.jpg',
          category: 'مساجد',
          tags: ['المدينة', 'النبوي', 'الحرم'],
          downloadCount: 1890,
          author: 'مصور إسلامي',
          rating: 4.8,
        ),
        ImageItem(
          id: 'al_aqsa',
          title: 'المسجد الأقصى',
          description: 'المسجد الأقصى المبارك في القدس الشريف',
          url: 'assets/images/al_aqsa.jpg',
          category: 'مساجد',
          tags: ['القدس', 'الأقصى', 'فلسطين'],
          downloadCount: 1650,
          author: 'مصور فلسطيني',
          rating: 4.7,
        ),
        ImageItem(
          id: 'dome_rock',
          title: 'قبة الصخرة',
          description: 'قبة الصخرة المشرفة في القدس',
          url: 'assets/images/dome_of_rock.jpg',
          category: 'مساجد',
          tags: ['القدس', 'قبة الصخرة', 'ذهبية'],
          downloadCount: 1420,
          author: 'مصور فلسطيني',
          rating: 4.6,
        ),
      ],
    ),
    ImageCategory(
      id: 'calligraphy',
      name: 'الخط العربي',
      description: 'مجموعة من أجمل الخطوط العربية',
      icon: Icons.brush,
      color: Colors.amber,
      images: [
        ImageItem(
          id: 'ayat_kursi',
          title: 'آية الكرسي',
          description: 'آية الكرسي بخط الثلث الجميل',
          url: 'assets/images/ayat_kursi.jpg',
          category: 'خط عربي',
          tags: ['آية الكرسي', 'خط الثلث', 'قرآن'],
          downloadCount: 1980,
          author: 'خطاط محترف',
          rating: 4.9,
        ),
        ImageItem(
          id: 'shahada',
          title: 'الشهادة',
          description: 'لا إله إلا الله محمد رسول الله بخط النسخ',
          url: 'assets/images/shahada.jpg',
          category: 'خط عربي',
          tags: ['الشهادة', 'خط النسخ', 'إسلام'],
          downloadCount: 1750,
          author: 'خطاط محترف',
          rating: 4.8,
        ),
        ImageItem(
          id: 'bismillah_diwani',
          title: 'بسم الله - خط الديواني',
          description: 'بسم الله الرحمن الرحيم بخط الديواني الجلي',
          url: 'assets/images/bismillah_diwani.jpg',
          category: 'خط عربي',
          tags: ['بسملة', 'خط الديواني', 'جلي'],
          downloadCount: 1560,
          author: 'خطاط تركي',
          rating: 4.7,
        ),
      ],
    ),
    ImageCategory(
      id: 'quran',
      name: 'المصاحف',
      description: 'صور للمصاحف الشريفة والمخطوطات القرآنية',
      icon: Icons.menu_book,
      color: Colors.purple,
      images: [
        ImageItem(
          id: 'mushaf_uthmani',
          title: 'المصحف العثماني',
          description: 'صفحة من المصحف العثماني الأصلي',
          url: 'assets/images/mushaf_uthmani.jpg',
          category: 'مصاحف',
          tags: ['عثماني', 'تاريخي', 'مخطوط'],
          downloadCount: 1340,
          author: 'متحف إسلامي',
          rating: 4.8,
        ),
        ImageItem(
          id: 'mushaf_madinah',
          title: 'مصحف المدينة',
          description: 'مصحف المدينة النبوية الشريف',
          url: 'assets/images/mushaf_madinah.jpg',
          category: 'مصاحف',
          tags: ['المدينة', 'مطبوع', 'حديث'],
          downloadCount: 2100,
          author: 'مجمع الملك فهد',
          rating: 4.9,
        ),
      ],
    ),
  ];

  String _selectedCategory = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('فهرس الصور'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            tooltip: _isGridView ? 'عرض قائمة' : 'عرض شبكة',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'البحث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'favorites':
                  _showFavorites();
                  break;
                case 'stats':
                  _showStatistics();
                  break;
                case 'sort':
                  _showSortOptions();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'favorites',
                child: Row(
                  children: [
                    Icon(Icons.favorite, color: Colors.red),
                    SizedBox(width: 8),
                    Text('المفضلة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'stats',
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('الإحصائيات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort',
                child: Row(
                  children: [
                    Icon(Icons.sort, color: Colors.green),
                    SizedBox(width: 8),
                    Text('ترتيب'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفئات
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                _buildCategoryChip('all', 'الكل', Icons.grid_view, Colors.grey),
                ..._categories.map((category) => _buildCategoryChip(
                      category.id,
                      category.name,
                      category.icon,
                      category.color,
                    )),
              ],
            ),
          ),

          // شريط البحث
          if (_searchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'نتائج البحث عن: "$_searchQuery"',
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () => setState(() => _searchQuery = ''),
                  ),
                ],
              ),
            ),

          // إحصائيات سريعة
          _buildQuickStats(),

          // شبكة الصور
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: _isGridView ? _buildImageGrid() : _buildImageList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(
      String id, String name, IconData icon, Color color) {
    final isSelected = _selectedCategory == id;

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: isSelected ? Colors.white : color),
            const SizedBox(width: 4),
            Text(name),
          ],
        ),
        onSelected: (selected) {
          setState(() {
            _selectedCategory = id;
            _searchQuery = '';
          });
        },
        selectedColor: color,
        checkmarkColor: Colors.white,
      ),
    );
  }

  Widget _buildImageGrid() {
    final filteredImages = _getFilteredImages();

    if (filteredImages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد صور في هذه الفئة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: filteredImages.length,
      itemBuilder: (context, index) {
        final image = filteredImages[index];
        return _buildImageCard(image);
      },
    );
  }

  Widget _buildImageCard(ImageItem image) {
    final isFavorite = _favoriteImages.contains(image.id);

    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 4,
      child: InkWell(
        onTap: () => _showImageDetails(image),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.blue[100]!,
                          Colors.blue[300]!,
                        ],
                      ),
                    ),
                    child: Icon(
                      Icons.image,
                      size: 48,
                      color: Colors.blue[600],
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () => _toggleFavorite(image.id),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: isFavorite ? Colors.red : Colors.grey,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                  if (image.rating != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.star,
                                size: 12, color: Colors.white),
                            const SizedBox(width: 2),
                            Text(
                              image.rating!.toStringAsFixed(1),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      image.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            image.category,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ),
                        if (image.downloadCount > 0)
                          Row(
                            children: [
                              Icon(Icons.download,
                                  size: 12, color: Colors.grey[600]),
                              const SizedBox(width: 2),
                              Text(
                                _formatDownloadCount(image.downloadCount),
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ImageItem> _getFilteredImages() {
    List<ImageItem> allImages = [];

    for (final category in _categories) {
      allImages.addAll(category.images);
    }

    if (_selectedCategory != 'all') {
      final selectedCat =
          _categories.firstWhere((cat) => cat.id == _selectedCategory);
      allImages = selectedCat.images;
    }

    if (_searchQuery.isNotEmpty) {
      allImages = allImages
          .where((image) =>
              image.title.contains(_searchQuery) ||
              image.description.contains(_searchQuery) ||
              image.category.contains(_searchQuery))
          .toList();
    }

    return allImages;
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الصور'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'ابحث عن صورة...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (value) {
            setState(() {
              _searchQuery = value;
              _selectedCategory = 'all';
            });
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showImageDetails(ImageItem image) {
    final isFavorite = _favoriteImages.contains(image.id);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // صورة مع تقييم ومفضلة
                Stack(
                  children: [
                    Container(
                      height: 200,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.blue[100]!,
                            Colors.blue[300]!,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.image,
                        size: 64,
                        color: Colors.blue[600],
                      ),
                    ),
                    if (image.rating != null)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.amber.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.star,
                                  size: 16, color: Colors.white),
                              const SizedBox(width: 4),
                              Text(
                                image.rating!.toStringAsFixed(1),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () {
                          _toggleFavorite(image.id);
                          Navigator.pop(context);
                          _showImageDetails(
                              image); // إعادة فتح النافذة لتحديث الحالة
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? Colors.red : Colors.grey,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // العنوان
                Text(
                  image.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // الفئة
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    image.category,
                    style: TextStyle(
                      color: Colors.blue[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // الوصف
                Text(
                  image.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // معلومات إضافية
                if (image.author != null || image.downloadCount > 0)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        if (image.author != null)
                          Row(
                            children: [
                              const Icon(Icons.person, size: 16),
                              const SizedBox(width: 8),
                              Text('المؤلف: ${image.author}'),
                            ],
                          ),
                        if (image.author != null && image.downloadCount > 0)
                          const SizedBox(height: 8),
                        if (image.downloadCount > 0)
                          Row(
                            children: [
                              const Icon(Icons.download, size: 16),
                              const SizedBox(width: 8),
                              Text(
                                  'التحميلات: ${_formatDownloadCount(image.downloadCount)}'),
                            ],
                          ),
                      ],
                    ),
                  ),

                // العلامات
                if (image.tags.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'العلامات:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: image.tags
                        .map((tag) => Chip(
                              label: Text(
                                tag,
                                style: const TextStyle(fontSize: 12),
                              ),
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                              visualDensity: VisualDensity.compact,
                            ))
                        .toList(),
                  ),
                ],

                const SizedBox(height: 20),

                // أزرار العمليات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      label: const Text('إغلاق'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[300],
                        foregroundColor: Colors.black,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('تم تحميل ${image.title}'),
                            action: SnackBarAction(
                              label: 'تراجع',
                              onPressed: () {},
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.download),
                      label: const Text('تحميل'),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('تم مشاركة ${image.title}'),
                          ),
                        );
                      },
                      icon: const Icon(Icons.share),
                      label: const Text('مشاركة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دوال مساعدة
  void _toggleFavorite(String imageId) {
    setState(() {
      if (_favoriteImages.contains(imageId)) {
        _favoriteImages.remove(imageId);
      } else {
        _favoriteImages.add(imageId);
      }
    });
  }

  String _formatDownloadCount(int count) {
    if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    }
    return count.toString();
  }

  Widget _buildQuickStats() {
    final totalImages = _getFilteredImages().length;
    final totalCategories = _categories.length;
    final favoriteCount = _favoriteImages.length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('الصور', totalImages.toString(), Icons.image),
          _buildStatItem('الفئات', totalCategories.toString(), Icons.category),
          _buildStatItem('المفضلة', favoriteCount.toString(), Icons.favorite),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue[600], size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue[800],
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildImageList() {
    final filteredImages = _getFilteredImages();

    if (filteredImages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد صور في هذه الفئة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredImages.length,
      itemBuilder: (context, index) {
        final image = filteredImages[index];
        return _buildImageListTile(image);
      },
    );
  }

  Widget _buildImageListTile(ImageItem image) {
    final isFavorite = _favoriteImages.contains(image.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue[100]!,
                Colors.blue[300]!,
              ],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.image,
            color: Colors.blue[600],
          ),
        ),
        title: Text(
          image.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(image.category),
            if (image.author != null)
              Text(
                'بواسطة: ${image.author}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            if (image.tags.isNotEmpty)
              Wrap(
                spacing: 4,
                children: image.tags
                    .take(3)
                    .map((tag) => Chip(
                          label: Text(
                            tag,
                            style: const TextStyle(fontSize: 10),
                          ),
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                        ))
                    .toList(),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (image.rating != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star, size: 12, color: Colors.white),
                    const SizedBox(width: 2),
                    Text(
                      image.rating!.toStringAsFixed(1),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _toggleFavorite(image.id),
              child: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : Colors.grey,
              ),
            ),
          ],
        ),
        onTap: () => _showImageDetails(image),
      ),
    );
  }

  void _showFavorites() {
    final favoriteImages = _getFilteredImages()
        .where((image) => _favoriteImages.contains(image.id))
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الصور المفضلة'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: favoriteImages.isEmpty
              ? const Center(child: Text('لا توجد صور مفضلة'))
              : ListView.builder(
                  itemCount: favoriteImages.length,
                  itemBuilder: (context, index) {
                    final image = favoriteImages[index];
                    return ListTile(
                      title: Text(image.title),
                      subtitle: Text(image.category),
                      onTap: () {
                        Navigator.pop(context);
                        _showImageDetails(image);
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showStatistics() {
    final allImages = _getFilteredImages();
    final totalDownloads =
        allImages.fold<int>(0, (sum, image) => sum + image.downloadCount);
    final avgRating = allImages.where((img) => img.rating != null).isEmpty
        ? 0.0
        : allImages
                .where((img) => img.rating != null)
                .map((img) => img.rating!)
                .reduce((a, b) => a + b) /
            allImages.where((img) => img.rating != null).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات المعرض'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('إجمالي الصور', allImages.length.toString()),
            _buildStatRow(
                'إجمالي التحميلات', _formatDownloadCount(totalDownloads)),
            _buildStatRow('متوسط التقييم', avgRating.toStringAsFixed(1)),
            _buildStatRow('الصور المفضلة', _favoriteImages.length.toString()),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترتيب الصور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('الأحدث'),
              leading: const Icon(Icons.access_time),
              onTap: () {
                Navigator.pop(context);
                // TODO: تطبيق الترتيب
              },
            ),
            ListTile(
              title: const Text('الأكثر تحميلاً'),
              leading: const Icon(Icons.download),
              onTap: () {
                Navigator.pop(context);
                // TODO: تطبيق الترتيب
              },
            ),
            ListTile(
              title: const Text('الأعلى تقييماً'),
              leading: const Icon(Icons.star),
              onTap: () {
                Navigator.pop(context);
                // TODO: تطبيق الترتيب
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}

// نماذج البيانات
class ImageCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final List<ImageItem> images;

  ImageCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.images,
  });
}

class ImageItem {
  final String id;
  final String title;
  final String description;
  final String url;
  final String category;
  final List<String> tags;
  final int downloadCount;
  final DateTime? dateAdded;
  final String? author;
  final double? rating;

  ImageItem({
    required this.id,
    required this.title,
    required this.description,
    required this.url,
    required this.category,
    this.tags = const [],
    this.downloadCount = 0,
    this.dateAdded,
    this.author,
    this.rating,
  });
}
