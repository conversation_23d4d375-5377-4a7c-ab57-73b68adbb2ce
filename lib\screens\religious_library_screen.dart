import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/religious_books_provider.dart';
import '../providers/theme_provider.dart';
import '../models/religious_book_models.dart';
import 'book_reader_screen.dart';
import 'hadith_screen.dart';
import 'advanced_tafsir_screen.dart';

class ReligiousLibraryScreen extends StatefulWidget {
  const ReligiousLibraryScreen({super.key});

  @override
  State<ReligiousLibraryScreen> createState() => _ReligiousLibraryScreenState();
}

class _ReligiousLibraryScreenState extends State<ReligiousLibraryScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReligiousBooksProvider>().loadBooks();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final booksProvider = context.watch<ReligiousBooksProvider>();

    return Scaffold(
      backgroundColor:
          themeProvider.isDarkMode ? Colors.black87 : Colors.grey[50],
      appBar: AppBar(
        title: const Text('المكتبة الدينية'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: booksProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات المكتبة
                  _buildLibraryStats(context, booksProvider.stats),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // الأقسام الرئيسية
                  _buildMainSections(context, booksProvider),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // المحتوى المميز
                  _buildFeaturedContent(context, booksProvider),
                ],
              ),
            ),
    );
  }

  /// بناء إحصائيات المكتبة
  Widget _buildLibraryStats(BuildContext context, LibraryStats? stats) {
    if (stats == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.teal.shade400, Colors.teal.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'إحصائيات المكتبة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('الكتب', stats.totalBooks.toString(), Icons.book),
              _buildStatItem(
                  'الفصول', stats.totalChapters.toString(), Icons.menu_book),
              _buildStatItem(
                  'الأقسام', stats.totalSections.toString(), Icons.article),
              _buildStatItem('الصوتيات', stats.totalAudioFiles.toString(),
                  Icons.audiotrack),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// بناء الأقسام الرئيسية
  Widget _buildMainSections(
      BuildContext context, ReligiousBooksProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأقسام الرئيسية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.paddingMedium,
          mainAxisSpacing: AppConstants.paddingMedium,
          childAspectRatio: 1.2,
          children: [
            _buildSectionCard(
              'الأحاديث النبوية',
              '5',
              Icons.format_quote,
              Colors.orange,
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const HadithScreen()),
              ),
            ),
            _buildSectionCard(
              'القراءة',
              '22',
              Icons.mic,
              Colors.amber,
              () => _showPoeticBooks(context),
            ),
            _buildSectionCard(
              'كتب التفسير',
              '6',
              Icons.book,
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const AdvancedTafsirScreen()),
              ),
            ),
            _buildSectionCard(
              'الكتب الفقهية',
              '5',
              Icons.gavel,
              Colors.blue,
              () => _showFiqhBooks(context),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard(
    String title,
    String count,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              count,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء المحتوى المميز
  Widget _buildFeaturedContent(
      BuildContext context, ReligiousBooksProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المحتوى المميز',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // فقه الحج والعمرة
        _buildFeaturedSection(
          'فقه الحج والعمرة',
          'مجموعة من العلماء',
          Colors.green,
          () {},
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        // فقه الصيام
        _buildFeaturedSection(
          'فقه الصيام',
          'مجموعة من العلماء',
          Colors.teal,
          () {},
        ),
      ],
    );
  }

  /// بناء قسم مميز
  Widget _buildFeaturedSection(
    String title,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: color,
                borderRadius:
                    BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: const Icon(Icons.book, color: Colors.white, size: 30),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في المكتبة'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'ابحث عن كتاب أو مؤلف...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context
                  .read<ReligiousBooksProvider>()
                  .searchBooks(_searchController.text);
              Navigator.pop(context);
              _showSearchResults(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// عرض نتائج البحث
  void _showSearchResults(BuildContext context) {
    // يمكن تطوير هذا لاحقاً لعرض نتائج البحث في شاشة منفصلة
  }

  void _showFiqhBooks(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(Icons.gavel, color: Colors.blue, size: 28),
                  const SizedBox(width: 12),
                  const Text(
                    'الكتب الفقهية',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildBookTile(
                    'فقه السنة',
                    'كتاب شامل في الفقه الإسلامي للسيد سابق',
                    Icons.book,
                    Colors.green,
                    () => _showBookContent(context, 'فقه_السنة'),
                  ),
                  _buildBookTile(
                    'الفقه الميسر',
                    'كتاب مبسط في الفقه الإسلامي',
                    Icons.lightbulb,
                    Colors.orange,
                    () => _showBookContent(context, 'فقه_ميسر'),
                  ),
                  _buildBookTile(
                    'بداية المجتهد',
                    'كتاب ابن رشد في الفقه المقارن',
                    Icons.school,
                    Colors.purple,
                    () => _showBookContent(context, 'بداية_المجتهد'),
                  ),
                  _buildBookTile(
                    'المغني لابن قدامة',
                    'موسوعة فقهية شاملة في المذهب الحنبلي',
                    Icons.library_books,
                    Colors.red,
                    () => _showBookContent(context, 'المغني'),
                  ),
                  _buildBookTile(
                    'الأم للشافعي',
                    'كتاب الإمام الشافعي في الفقه',
                    Icons.star,
                    Colors.blue,
                    () => _showBookContent(context, 'الأم'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPoeticBooks(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(Icons.book, color: Colors.amber, size: 28),
                  const SizedBox(width: 12),
                  const Text(
                    'الكتب الشعرية والأدبية',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildBookTile(
                    'ديوان الإمام الشافعي',
                    'مجموعة من أشعار الإمام الشافعي في الحكمة والموعظة',
                    Icons.auto_stories,
                    Colors.green,
                    () => _showBookContent(context, 'شافعي'),
                  ),
                  _buildBookTile(
                    'البردة للبوصيري',
                    'قصيدة البردة في مدح النبي صلى الله عليه وسلم',
                    Icons.favorite,
                    Colors.red,
                    () => _showBookContent(context, 'بردة'),
                  ),
                  _buildBookTile(
                    'نهج البلاغة',
                    'مختارات من خطب وأقوال الإمام علي رضي الله عنه',
                    Icons.record_voice_over,
                    Colors.blue,
                    () => _showBookContent(context, 'نهج'),
                  ),
                  _buildBookTile(
                    'الأسماء الحسنى',
                    'شرح وتفسير أسماء الله الحسنى',
                    Icons.star,
                    Colors.purple,
                    () => _showBookContent(context, 'أسماء'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookTile(String title, String description, IconData icon,
      Color color, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showBookContent(BuildContext context, String bookType) {
    Navigator.pop(context); // إغلاق القائمة السفلية

    String content = '';
    String title = '';

    switch (bookType) {
      case 'شافعي':
        title = 'ديوان الإمام الشافعي';
        content = '''دع الأيام تفعل ما تشاء
وطب نفساً إذا حكم القضاء

ولا تجزع لحادثة الليالي
فما لحوادث الدنيا بقاء

وكن رجلاً على الأهوال جلداً
وشيمتك السماحة والوفاء

وإن كثرت عيوبك في البرايا
وسرك أن يكون لها غطاء

تستر بالسخاء فكل عيب
يغطيه كما قيل السخاء''';
        break;
      case 'بردة':
        title = 'البردة للبوصيري';
        content = '''أمن تذكر جيران بذي سلم
مزجت دمعاً جرى من مقلة بدم

أم هبت الريح من تلقاء كاظمة
وأومض البرق في الظلماء من إضم

فما لعينيك إن قلت اكففا همتا
وما لقلبك إن قلت استفق يهم''';
        break;
      case 'نهج':
        title = 'نهج البلاغة';
        content = '''من خطب الإمام علي رضي الله عنه:

"أيها الناس، إن أخوف ما أخاف عليكم اثنان: اتباع الهوى، وطول الأمل."

"كن في الفتنة كابن اللبون، لا ظهر فيركب، ولا ضرع فيحلب."

"قيمة كل امرئ ما يحسنه."''';
        break;
      case 'أسماء':
        title = 'الأسماء الحسنى';
        content = '''أسماء الله الحسنى التسعة والتسعون:

الله - الرحمن - الرحيم - الملك - القدوس - السلام - المؤمن - المهيمن - العزيز - الجبار - المتكبر - الخالق - البارئ - المصور - الغفار - القهار - الوهاب - الرزاق - الفتاح - العليم''';
        break;
      case 'فقه_السنة':
        title = 'فقه السنة';
        content = '''كتاب فقه السنة للسيد سابق

باب الطهارة:
الطهارة شرط من شروط صحة الصلاة، وهي نوعان:
1. طهارة حكمية: وهي الوضوء والغسل والتيمم
2. طهارة حسية: وهي إزالة النجاسات

الوضوء:
فرائض الوضوء أربعة:
- غسل الوجه
- غسل اليدين إلى المرفقين
- مسح الرأس
- غسل الرجلين إلى الكعبين

سنن الوضوء:
- التسمية في أوله
- السواك
- غسل الكفين ثلاثاً
- المضمضة والاستنشاق''';
        break;
      case 'فقه_ميسر':
        title = 'الفقه الميسر';
        content = '''الفقه الميسر في العبادات

أركان الإسلام الخمسة:
1. شهادة أن لا إله إلا الله وأن محمداً رسول الله
2. إقام الصلاة
3. إيتاء الزكاة
4. صوم رمضان
5. حج البيت لمن استطاع إليه سبيلاً

الصلاة:
- الصلوات الخمس فريضة على كل مسلم بالغ عاقل
- أوقات الصلوات: الفجر، الظهر، العصر، المغرب، العشاء
- شروط الصلاة: الطهارة، ستر العورة، استقبال القبلة، دخول الوقت''';
        break;
      case 'بداية_المجتهد':
        title = 'بداية المجتهد';
        content = '''بداية المجتهد ونهاية المقتصد لابن رشد

مقدمة في أصول الفقه:
الفقه هو العلم بالأحكام الشرعية العملية المكتسب من أدلتها التفصيلية

مصادر التشريع:
1. القرآن الكريم
2. السنة النبوية
3. الإجماع
4. القياس

اختلاف الفقهاء:
أسباب اختلاف الفقهاء ترجع إلى:
- اختلاف في فهم النصوص
- اختلاف في صحة الأحاديث
- اختلاف في القياس والاجتهاد''';
        break;
      case 'المغني':
        title = 'المغني لابن قدامة';
        content = '''المغني لابن قدامة المقدسي

كتاب الصلاة:
الصلاة عمود الدين، وهي أول ما يحاسب عليه العبد يوم القيامة

شروط وجوب الصلاة:
- الإسلام
- البلوغ
- العقل
- الطهارة من الحيض والنفاس

أركان الصلاة:
1. القيام مع القدرة
2. تكبيرة الإحرام
3. قراءة الفاتحة
4. الركوع
5. الرفع من الركوع
6. السجود
7. الجلوس بين السجدتين
8. التشهد الأخير
9. التسليم''';
        break;
      case 'الأم':
        title = 'الأم للشافعي';
        content = '''كتاب الأم للإمام الشافعي

باب الزكاة:
الزكاة ركن من أركان الإسلام، وهي حق معلوم في المال

أنواع الأموال التي تجب فيها الزكاة:
1. الذهب والفضة
2. عروض التجارة
3. الزروع والثمار
4. الأنعام (الإبل والبقر والغنم)

شروط وجوب الزكاة:
- الإسلام
- الحرية
- ملك النصاب
- حولان الحول
- عدم الدين المستغرق''';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    content,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.8,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
