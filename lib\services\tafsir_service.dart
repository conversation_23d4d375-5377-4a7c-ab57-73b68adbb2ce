import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/tafsir.dart';

class TafsirService {
  static final TafsirService _instance = TafsirService._internal();
  factory TafsirService() => _instance;
  TafsirService._internal();

  // API endpoints
  static const String _baseUrl = 'https://api.alquran.cloud/v1';
  static const String _tafsirEndpoint = '/ayah';
  static const String _translationEndpoint = '/ayah';

  // Cache keys
  static const String _selectedTafsirKey = 'selected_tafsir_id';
  static const String _selectedTranslationKey = 'selected_translation_id';
  static const String _enableTafsirKey = 'enable_tafsir';
  static const String _enableTranslationKey = 'enable_translation';

  // Cache for tafsir and translation texts
  final Map<String, TafsirText> _tafsirCache = {};
  final Map<String, TranslationText> _translationCache = {};

  // Get available tafsirs
  List<Tafsir> getAvailableTafsirs() {
    return Tafsir.popularTafsirs;
  }

  // Get available translations
  List<Translation> getAvailableTranslations() {
    return Translation.popularTranslations;
  }

  // Get tafsir for specific ayah
  Future<TafsirText?> getTafsirForAyah(int surahNumber, int ayahNumber, {int? tafsirId}) async {
    try {
      final selectedTafsirId = tafsirId ?? await getSelectedTafsirId();
      if (selectedTafsirId == null) return null;

      final cacheKey = '${surahNumber}_${ayahNumber}_${selectedTafsirId}';
      
      // Check cache first
      if (_tafsirCache.containsKey(cacheKey)) {
        return _tafsirCache[cacheKey];
      }

      // Calculate global ayah number
      final globalAyahNumber = await _getGlobalAyahNumber(surahNumber, ayahNumber);
      if (globalAyahNumber == null) return null;

      final response = await http.get(
        Uri.parse('$_baseUrl$_tafsirEndpoint/$globalAyahNumber/tafsirs/$selectedTafsirId'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200 && jsonData['data'] != null) {
          final tafsirData = jsonData['data'];
          final tafsir = getAvailableTafsirs().firstWhere(
            (t) => t.id == selectedTafsirId,
            orElse: () => const Tafsir(
              id: 0,
              name: 'Unknown',
              authorName: 'Unknown',
              language: 'ar',
              description: '',
            ),
          );

          final tafsirText = TafsirText(
            ayahNumber: ayahNumber,
            surahNumber: surahNumber,
            text: tafsirData['text'] ?? '',
            tafsirName: tafsir.name,
            tafsirId: selectedTafsirId,
          );

          // Cache the result
          _tafsirCache[cacheKey] = tafsirText;
          return tafsirText;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching tafsir: $e');
      return null;
    }
  }

  // Get translation for specific ayah
  Future<TranslationText?> getTranslationForAyah(int surahNumber, int ayahNumber, {int? translationId}) async {
    try {
      final selectedTranslationId = translationId ?? await getSelectedTranslationId();
      if (selectedTranslationId == null) return null;

      final cacheKey = '${surahNumber}_${ayahNumber}_${selectedTranslationId}';
      
      // Check cache first
      if (_translationCache.containsKey(cacheKey)) {
        return _translationCache[cacheKey];
      }

      // Calculate global ayah number
      final globalAyahNumber = await _getGlobalAyahNumber(surahNumber, ayahNumber);
      if (globalAyahNumber == null) return null;

      final response = await http.get(
        Uri.parse('$_baseUrl$_translationEndpoint/$globalAyahNumber/editions/$selectedTranslationId'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200 && jsonData['data'] != null) {
          final translationData = jsonData['data'];
          final translation = getAvailableTranslations().firstWhere(
            (t) => t.id == selectedTranslationId,
            orElse: () => const Translation(
              id: 0,
              name: 'Unknown',
              authorName: 'Unknown',
              language: 'English',
              languageCode: 'en',
              direction: 'ltr',
            ),
          );

          final translationText = TranslationText(
            ayahNumber: ayahNumber,
            surahNumber: surahNumber,
            text: translationData['text'] ?? '',
            translationName: translation.name,
            translationId: selectedTranslationId,
            languageCode: translation.languageCode,
          );

          // Cache the result
          _translationCache[cacheKey] = translationText;
          return translationText;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching translation: $e');
      return null;
    }
  }

  // Helper method to calculate global ayah number
  Future<int?> _getGlobalAyahNumber(int surahNumber, int ayahNumber) async {
    try {
      // This is a simplified calculation - in a real app, you'd have a lookup table
      // For now, we'll use the API to get the ayah directly
      final response = await http.get(
        Uri.parse('$_baseUrl/surah/$surahNumber/ayah/$ayahNumber'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200 && jsonData['data'] != null) {
          return jsonData['data']['number'];
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error calculating global ayah number: $e');
      return null;
    }
  }

  // Settings methods
  Future<int?> getSelectedTafsirId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_selectedTafsirKey);
    } catch (e) {
      return null;
    }
  }

  Future<void> setSelectedTafsirId(int tafsirId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_selectedTafsirKey, tafsirId);
    } catch (e) {
      debugPrint('Error saving selected tafsir: $e');
    }
  }

  Future<int?> getSelectedTranslationId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_selectedTranslationKey);
    } catch (e) {
      return null;
    }
  }

  Future<void> setSelectedTranslationId(int translationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_selectedTranslationKey, translationId);
    } catch (e) {
      debugPrint('Error saving selected translation: $e');
    }
  }

  Future<bool> isTafsirEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_enableTafsirKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  Future<void> setTafsirEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enableTafsirKey, enabled);
    } catch (e) {
      debugPrint('Error saving tafsir enabled state: $e');
    }
  }

  Future<bool> isTranslationEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_enableTranslationKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  Future<void> setTranslationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enableTranslationKey, enabled);
    } catch (e) {
      debugPrint('Error saving translation enabled state: $e');
    }
  }

  // Clear cache
  void clearCache() {
    _tafsirCache.clear();
    _translationCache.clear();
  }

  // Get cache size
  int getCacheSize() {
    return _tafsirCache.length + _translationCache.length;
  }
}
