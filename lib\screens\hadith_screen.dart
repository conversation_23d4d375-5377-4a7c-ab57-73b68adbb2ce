import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/hadith_provider.dart';
import '../providers/theme_provider.dart';
import '../models/religious_book_models.dart';

class HadithScreen extends StatefulWidget {
  const HadithScreen({super.key});

  @override
  State<HadithScreen> createState() => _HadithScreenState();
}

class _HadithScreenState extends State<HadithScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<HadithProvider>();
      provider.loadHadithBooks();
      provider.loadRandomHadiths();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final hadithProvider = context.watch<HadithProvider>();

    return Scaffold(
      backgroundColor: themeProvider.isDarkMode ? Colors.black87 : Colors.grey[50],
      appBar: AppBar(
        title: const Text('الأحاديث النبوية'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => hadithProvider.refresh(),
          ),
        ],
      ),
      body: hadithProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات الأحاديث
                  _buildHadithStats(context, hadithProvider),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // كتب الحديث
                  _buildHadithBooks(context, hadithProvider),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // الأحاديث العشوائية
                  _buildRandomHadiths(context, hadithProvider),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // المفضلة
                  if (hadithProvider.hasFavorites)
                    _buildFavoriteHadiths(context, hadithProvider),
                ],
              ),
            ),
    );
  }

  /// بناء إحصائيات الأحاديث
  Widget _buildHadithStats(BuildContext context, HadithProvider provider) {
    final stats = provider.hadithStats;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade400, Colors.green.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'إحصائيات الأحاديث',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('المجموع', stats['total'].toString(), Icons.book),
              _buildStatItem('الصحيح', stats['sahih'].toString(), Icons.verified),
              _buildStatItem('الحسن', stats['hasan'].toString(), Icons.star),
              _buildStatItem('المفضلة', stats['favorites'].toString(), Icons.favorite),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// بناء كتب الحديث
  Widget _buildHadithBooks(BuildContext context, HadithProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'كتب الحديث',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: provider.hadithBooks.length,
          itemBuilder: (context, index) {
            final book = provider.hadithBooks[index];
            return _buildHadithBookCard(context, book, provider);
          },
        ),
      ],
    );
  }

  /// بناء بطاقة كتاب الحديث
  Widget _buildHadithBookCard(BuildContext context, HadithBook book, HadithProvider provider) {
    final isSelected = provider.selectedBookId == book.id;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: isSelected ? 4 : 2,
      child: InkWell(
        onTap: () {
          provider.loadHadithsFromBook(book.id);
          _navigateToHadithList(context, book);
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: isSelected ? Border.all(color: Colors.green, width: 2) : null,
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                child: const Icon(Icons.menu_book, color: Colors.white, size: 30),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book.nameArabic,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      book.authorArabic,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${book.totalHadiths} حديث',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الأحاديث العشوائية
  Widget _buildRandomHadiths(BuildContext context, HadithProvider provider) {
    if (provider.randomHadiths.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أحاديث مختارة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => provider.loadRandomHadiths(),
              child: const Text('تحديث'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: provider.randomHadiths.length,
            itemBuilder: (context, index) {
              final hadith = provider.randomHadiths[index];
              return _buildHadithCard(context, hadith, provider);
            },
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة الحديث
  Widget _buildHadithCard(BuildContext context, Hadith hadith, HadithProvider provider) {
    return Container(
      width: 300,
      margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getGradeColor(hadith.grade),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getGradeText(hadith.grade),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      provider.isFavorite(hadith) ? Icons.favorite : Icons.favorite_border,
                      color: provider.isFavorite(hadith) ? Colors.red : Colors.grey,
                    ),
                    onPressed: () => provider.toggleFavorite(hadith),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  hadith.arabicText,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.6,
                  ),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                hadith.narratorArabic,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المفضلة
  Widget _buildFavoriteHadiths(BuildContext context, HadithProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأحاديث المفضلة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: provider.favoriteHadiths.length,
          itemBuilder: (context, index) {
            final hadith = provider.favoriteHadiths[index];
            return _buildHadithListTile(context, hadith, provider);
          },
        ),
      ],
    );
  }

  /// بناء عنصر قائمة الحديث
  Widget _buildHadithListTile(BuildContext context, Hadith hadith, HadithProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        title: Text(
          hadith.arabicText,
          style: const TextStyle(fontSize: 14),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(hadith.narratorArabic),
        trailing: IconButton(
          icon: const Icon(Icons.favorite, color: Colors.red),
          onPressed: () => provider.removeFromFavorites(hadith),
        ),
        onTap: () => _showHadithDetails(context, hadith),
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الأحاديث'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'ابحث في نص الحديث...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<HadithProvider>().searchHadiths(_searchController.text);
              Navigator.pop(context);
              _showSearchResults(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// عرض نتائج البحث
  void _showSearchResults(BuildContext context) {
    // يمكن تطوير هذا لاحقاً لعرض نتائج البحث في شاشة منفصلة
  }

  /// عرض تفاصيل الحديث
  void _showHadithDetails(BuildContext context, Hadith hadith) {
    // يمكن تطوير هذا لاحقاً لعرض تفاصيل الحديث في شاشة منفصلة
  }

  /// الانتقال لقائمة أحاديث الكتاب
  void _navigateToHadithList(BuildContext context, HadithBook book) {
    // يمكن تطوير هذا لاحقاً للانتقال لشاشة قائمة الأحاديث
  }

  /// الحصول على لون درجة الحديث
  Color _getGradeColor(HadithGrade grade) {
    switch (grade) {
      case HadithGrade.sahih:
        return Colors.green;
      case HadithGrade.hasan:
        return Colors.orange;
      case HadithGrade.daif:
        return Colors.red;
      case HadithGrade.mawdu:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  /// الحصول على نص درجة الحديث
  String _getGradeText(HadithGrade grade) {
    switch (grade) {
      case HadithGrade.sahih:
        return 'صحيح';
      case HadithGrade.hasan:
        return 'حسن';
      case HadithGrade.daif:
        return 'ضعيف';
      case HadithGrade.mawdu:
        return 'موضوع';
      default:
        return 'غير معروف';
    }
  }
}
