enum RecommendationType {
  surah,
  ayah,
  topic,
  reading,
  reflection,
  memorization,
}

enum RecommendationPriority {
  low,
  medium,
  high,
  urgent,
}

class Recommendation {
  final String id;
  final RecommendationType type;
  final String title;
  final String description;
  final String? content;
  final RecommendationPriority priority;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final Map<String, dynamic>? metadata;
  final bool isPersonalized;
  final double relevanceScore;

  const Recommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.content,
    required this.priority,
    required this.createdAt,
    this.expiresAt,
    this.metadata,
    this.isPersonalized = false,
    this.relevanceScore = 0.0,
  });

  factory Recommendation.fromJson(Map<String, dynamic> json) {
    return Recommendation(
      id: json['id'] ?? '',
      type: RecommendationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RecommendationType.reading,
      ),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      content: json['content'],
      priority: RecommendationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => RecommendationPriority.medium,
      ),
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      expiresAt: json['expires_at'] != null 
          ? DateTime.tryParse(json['expires_at']) 
          : null,
      metadata: json['metadata'],
      isPersonalized: json['is_personalized'] ?? false,
      relevanceScore: (json['relevance_score'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'content': content,
      'priority': priority.name,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'metadata': metadata,
      'is_personalized': isPersonalized,
      'relevance_score': relevanceScore,
    };
  }

  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  
  bool get isHighPriority => priority == RecommendationPriority.high || 
                            priority == RecommendationPriority.urgent;

  String get priorityDisplayName {
    switch (priority) {
      case RecommendationPriority.low:
        return 'منخفضة';
      case RecommendationPriority.medium:
        return 'متوسطة';
      case RecommendationPriority.high:
        return 'عالية';
      case RecommendationPriority.urgent:
        return 'عاجلة';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case RecommendationType.surah:
        return 'سورة';
      case RecommendationType.ayah:
        return 'آية';
      case RecommendationType.topic:
        return 'موضوع';
      case RecommendationType.reading:
        return 'قراءة';
      case RecommendationType.reflection:
        return 'تأمل';
      case RecommendationType.memorization:
        return 'حفظ';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recommendation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Recommendation(id: $id, type: $type, title: $title)';
}

class UserPreference {
  final String userId;
  final List<String> favoriteTopics;
  final List<int> favoriteSurahs;
  final Map<String, double> readingPatterns;
  final Map<String, int> interactionCounts;
  final DateTime lastUpdated;

  const UserPreference({
    required this.userId,
    required this.favoriteTopics,
    required this.favoriteSurahs,
    required this.readingPatterns,
    required this.interactionCounts,
    required this.lastUpdated,
  });

  factory UserPreference.fromJson(Map<String, dynamic> json) {
    return UserPreference(
      userId: json['user_id'] ?? '',
      favoriteTopics: List<String>.from(json['favorite_topics'] ?? []),
      favoriteSurahs: List<int>.from(json['favorite_surahs'] ?? []),
      readingPatterns: Map<String, double>.from(json['reading_patterns'] ?? {}),
      interactionCounts: Map<String, int>.from(json['interaction_counts'] ?? {}),
      lastUpdated: DateTime.tryParse(json['last_updated'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'favorite_topics': favoriteTopics,
      'favorite_surahs': favoriteSurahs,
      'reading_patterns': readingPatterns,
      'interaction_counts': interactionCounts,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPreference && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;
}

class ReadingInsight {
  final String id;
  final String title;
  final String description;
  final String category;
  final Map<String, dynamic> data;
  final DateTime generatedAt;
  final double confidence;

  const ReadingInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.data,
    required this.generatedAt,
    this.confidence = 0.0,
  });

  factory ReadingInsight.fromJson(Map<String, dynamic> json) {
    return ReadingInsight(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      generatedAt: DateTime.tryParse(json['generated_at'] ?? '') ?? DateTime.now(),
      confidence: (json['confidence'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'data': data,
      'generated_at': generatedAt.toIso8601String(),
      'confidence': confidence,
    };
  }

  bool get isHighConfidence => confidence >= 0.8;
  bool get isMediumConfidence => confidence >= 0.5 && confidence < 0.8;
  bool get isLowConfidence => confidence < 0.5;

  String get confidenceDisplayName {
    if (isHighConfidence) return 'عالية';
    if (isMediumConfidence) return 'متوسطة';
    return 'منخفضة';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReadingInsight && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'ReadingInsight(id: $id, title: $title, confidence: $confidence)';
}
