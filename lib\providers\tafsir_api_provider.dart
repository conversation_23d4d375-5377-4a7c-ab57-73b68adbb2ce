import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';
import '../services/tafsir_api_service.dart';

/// Provider لإدارة التفاسير مع API
class TafsirApiProvider with ChangeNotifier {
  List<Map<String, dynamic>> _availableTafsirs = [];
  List<TafsirVerse> _currentTafsir = [];
  List<TafsirVerse> _searchResults = [];
  List<TafsirVerse> _favoriteTafsirs = [];
  
  TafsirVerse? _currentVerse;
  String _selectedTafsir = 'tafsir_muyassar';
  int _currentSurah = 1;
  int _currentAyah = 1;
  
  bool _isLoading = false;
  String _searchQuery = '';

  // Getters
  List<Map<String, dynamic>> get availableTafsirs => _availableTafsirs;
  List<TafsirVerse> get currentTafsir => _currentTafsir;
  List<TafsirVerse> get searchResults => _searchResults;
  List<TafsirVerse> get favoriteTafsirs => _favoriteTafsirs;
  
  TafsirVerse? get currentVerse => _currentVerse;
  String get selectedTafsir => _selectedTafsir;
  int get currentSurah => _currentSurah;
  int get currentAyah => _currentAyah;
  
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;

  bool get hasTafsir => _currentTafsir.isNotEmpty;
  bool get hasSearchResults => _searchResults.isNotEmpty;
  bool get hasFavorites => _favoriteTafsirs.isNotEmpty;

  /// تحميل قائمة التفاسير المتاحة
  Future<void> loadAvailableTafsirs() async {
    try {
      _setLoading(true);
      _availableTafsirs = await TafsirApiService.getAvailableTafsirs();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل قائمة التفاسير: $e');
    }
  }

  /// تحديد التفسير المختار
  void setSelectedTafsir(String tafsirName) {
    _selectedTafsir = tafsirName;
    notifyListeners();
    
    // إعادة تحميل التفسير الحالي بالتفسير الجديد
    if (_currentSurah > 0 && _currentAyah > 0) {
      loadTafsirForVerse(_currentSurah, _currentAyah);
    }
  }

  /// تحميل تفسير آية محددة
  Future<void> loadTafsirForVerse(int surahNumber, int ayahNumber) async {
    try {
      _setLoading(true);
      _currentSurah = surahNumber;
      _currentAyah = ayahNumber;
      
      final tafsirVerse = await TafsirApiService.getTafsirForVerse(
        surahNumber: surahNumber,
        ayahNumber: ayahNumber,
        tafsirName: _selectedTafsir,
      );
      
      if (tafsirVerse != null) {
        _currentVerse = tafsirVerse;
        _currentTafsir = [tafsirVerse];
      } else {
        _currentTafsir = [];
      }
      
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل تفسير الآية: $e');
    }
  }

  /// تحميل تفسير سورة كاملة
  Future<void> loadTafsirForSurah(int surahNumber) async {
    try {
      _setLoading(true);
      _currentSurah = surahNumber;
      
      _currentTafsir = await TafsirApiService.getTafsirForSurah(
        surahNumber: surahNumber,
        tafsirName: _selectedTafsir,
      );
      
      if (_currentTafsir.isNotEmpty) {
        _currentVerse = _currentTafsir.first;
        _currentAyah = _currentVerse!.ayahNumber;
      }
      
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في تحميل تفسير السورة: $e');
    }
  }

  /// البحث في التفاسير
  Future<void> searchTafsir(String query) async {
    try {
      _searchQuery = query;
      
      if (query.trim().isEmpty) {
        _searchResults = [];
        notifyListeners();
        return;
      }

      _setLoading(true);
      _searchResults = await TafsirApiService.searchTafsir(
        query: query,
        tafsirName: _selectedTafsir,
      );
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('خطأ في البحث في التفسير: $e');
    }
  }

  /// تحديد الآية الحالية
  void setCurrentVerse(TafsirVerse verse) {
    _currentVerse = verse;
    _currentSurah = verse.surahNumber;
    _currentAyah = verse.ayahNumber;
    notifyListeners();
  }

  /// إضافة تفسير للمفضلة
  void addToFavorites(TafsirVerse verse) {
    if (!_favoriteTafsirs.any((v) => v.id == verse.id)) {
      _favoriteTafsirs.add(verse);
      notifyListeners();
      _saveFavorites();
    }
  }

  /// إزالة تفسير من المفضلة
  void removeFromFavorites(TafsirVerse verse) {
    _favoriteTafsirs.removeWhere((v) => v.id == verse.id);
    notifyListeners();
    _saveFavorites();
  }

  /// التحقق من وجود تفسير في المفضلة
  bool isFavorite(TafsirVerse verse) {
    return _favoriteTafsirs.any((v) => v.id == verse.id);
  }

  /// تبديل حالة المفضلة
  void toggleFavorite(TafsirVerse verse) {
    if (isFavorite(verse)) {
      removeFromFavorites(verse);
    } else {
      addToFavorites(verse);
    }
  }

  /// الحصول على الآية التالية
  TafsirVerse? getNextVerse() {
    if (_currentVerse == null || _currentTafsir.isEmpty) return null;
    
    final currentIndex = _currentTafsir.indexOf(_currentVerse!);
    if (currentIndex < _currentTafsir.length - 1) {
      return _currentTafsir[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على الآية السابقة
  TafsirVerse? getPreviousVerse() {
    if (_currentVerse == null || _currentTafsir.isEmpty) return null;
    
    final currentIndex = _currentTafsir.indexOf(_currentVerse!);
    if (currentIndex > 0) {
      return _currentTafsir[currentIndex - 1];
    }
    return null;
  }

  /// الانتقال للآية التالية
  void goToNextVerse() {
    final nextVerse = getNextVerse();
    if (nextVerse != null) {
      setCurrentVerse(nextVerse);
    }
  }

  /// الانتقال للآية السابقة
  void goToPreviousVerse() {
    final previousVerse = getPreviousVerse();
    if (previousVerse != null) {
      setCurrentVerse(previousVerse);
    }
  }

  /// الحصول على تفسير آية بالمعرف
  TafsirVerse? getTafsirById(String id) {
    return _currentTafsir.where((verse) => verse.id == id).firstOrNull;
  }

  /// تصفية التفاسير حسب النوع
  List<TafsirVerse> getTafsirsByType(TafsirType type) {
    return _currentTafsir.where((verse) => verse.type == type).toList();
  }

  /// الحصول على التفاسير الكلاسيكية
  List<TafsirVerse> get classicalTafsirs => getTafsirsByType(TafsirType.classical);

  /// الحصول على التفاسير المعاصرة
  List<TafsirVerse> get modernTafsirs => getTafsirsByType(TafsirType.modern);

  /// الحصول على التفاسير الفقهية
  List<TafsirVerse> get jurisprudentialTafsirs => getTafsirsByType(TafsirType.jurisprudential);

  /// مسح البحث
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
    notifyListeners();
  }

  /// مسح جميع المفضلة
  void clearFavorites() {
    _favoriteTafsirs.clear();
    notifyListeners();
    _saveFavorites();
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// حفظ المفضلة (يمكن تطوير هذا لاحقاً مع SharedPreferences)
  void _saveFavorites() {
    // TODO: حفظ المفضلة في التخزين المحلي
    debugPrint('تم حفظ ${_favoriteTafsirs.length} تفسير في المفضلة');
  }

  /// تحميل المفضلة (يمكن تطوير هذا لاحقاً مع SharedPreferences)
  void _loadFavorites() {
    // TODO: تحميل المفضلة من التخزين المحلي
  }

  /// إحصائيات التفاسير
  Map<String, int> get tafsirStats {
    return {
      'total': _currentTafsir.length,
      'classical': classicalTafsirs.length,
      'modern': modernTafsirs.length,
      'jurisprudential': jurisprudentialTafsirs.length,
      'favorites': _favoriteTafsirs.length,
    };
  }

  /// الحصول على اسم التفسير بالعربية
  String getTafsirArabicName(String tafsirName) {
    switch (tafsirName) {
      case 'tafsir_muyassar':
        return 'التفسير الميسر';
      case 'ibn_kathir':
        return 'تفسير ابن كثير';
      case 'jalalayn':
        return 'تفسير الجلالين';
      case 'qurtubi':
        return 'تفسير القرطبي';
      case 'tabari':
        return 'تفسير الطبري';
      case 'saadi':
        return 'تفسير السعدي';
      default:
        return 'التفسير الميسر';
    }
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await loadAvailableTafsirs();
    if (_currentSurah > 0) {
      await loadTafsirForSurah(_currentSurah);
    }
  }

  /// الحصول على قائمة التفاسير المتاحة كخيارات
  List<Map<String, String>> get tafsirOptions {
    return [
      {'id': 'tafsir_muyassar', 'name': 'التفسير الميسر'},
      {'id': 'ibn_kathir', 'name': 'تفسير ابن كثير'},
      {'id': 'jalalayn', 'name': 'تفسير الجلالين'},
      {'id': 'qurtubi', 'name': 'تفسير القرطبي'},
      {'id': 'tabari', 'name': 'تفسير الطبري'},
      {'id': 'saadi', 'name': 'تفسير السعدي'},
    ];
  }
}
