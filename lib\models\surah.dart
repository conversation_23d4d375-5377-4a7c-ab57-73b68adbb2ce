class Surah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final int numberOfAyahs;
  final String revelationType;
  final int juz;
  final int hizb;
  final int rukus;
  final List<Ayah> ayahs;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.numberOfAyahs,
    required this.revelationType,
    required this.juz,
    required this.hizb,
    required this.rukus,
    this.ayahs = const [],
  });

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'] ?? 0,
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      englishNameTranslation: json['englishNameTranslation'] ?? '',
      numberOfAyahs: json['numberOfAyahs'] ?? 0,
      revelationType: json['revelationType'] ?? '',
      juz: json['juz'] ?? 1,
      hizb: json['hizb'] ?? 1,
      rukus: json['rukus'] ?? 1,
      ayahs: json['ayahs'] != null
          ? (json['ayahs'] as List).map((ayah) => Ayah.fromJson(ayah)).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
      'juz': juz,
      'hizb': hizb,
      'rukus': rukus,
      'ayahs': ayahs.map((ayah) => ayah.toJson()).toList(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
      'juz': juz,
      'hizb': hizb,
      'rukus': rukus,
    };
  }

  factory Surah.fromMap(Map<String, dynamic> map) {
    return Surah(
      number: map['number'] ?? 0,
      name: map['name'] ?? '',
      englishName: map['englishName'] ?? '',
      englishNameTranslation: map['englishNameTranslation'] ?? '',
      numberOfAyahs: map['numberOfAyahs'] ?? 0,
      revelationType: map['revelationType'] ?? '',
      juz: map['juz'] ?? 1,
      hizb: map['hizb'] ?? 1,
      rukus: map['rukus'] ?? 1,
    );
  }

  @override
  String toString() {
    return 'Surah{number: $number, name: $name, englishName: $englishName, numberOfAyahs: $numberOfAyahs}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Surah &&
          runtimeType == other.runtimeType &&
          number == other.number;

  @override
  int get hashCode => number.hashCode;

  // Helper methods
  bool get isMakki => revelationType == 'Meccan';
  bool get isMadani => revelationType == 'Medinan';

  String get revelationTypeArabic => isMakki ? 'مكية' : 'مدنية';

  String get formattedName => '$number. $name';

  String get shortDescription =>
      '$name - $numberOfAyahs آية - $revelationTypeArabic';
}

class Ayah {
  final int number;
  final int numberInSurah;
  final String text;
  final int surahNumber;
  final int juz;
  final int manzil;
  final int page;
  final int ruku;
  final int hizbQuarter;
  final bool sajda;
  final String? audioUrl;

  Ayah({
    required this.number,
    required this.numberInSurah,
    required this.text,
    required this.surahNumber,
    required this.juz,
    required this.manzil,
    required this.page,
    required this.ruku,
    required this.hizbQuarter,
    this.sajda = false,
    this.audioUrl,
  });

  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'] ?? 0,
      numberInSurah: json['numberInSurah'] ?? 0,
      text: json['text'] ?? '',
      surahNumber: json['surah']?['number'] ?? json['surahNumber'] ?? 0,
      juz: json['juz'] ?? 1,
      manzil: json['manzil'] ?? 1,
      page: json['page'] ?? 1,
      ruku: json['ruku'] ?? 1,
      hizbQuarter: json['hizbQuarter'] ?? 1,
      sajda: json['sajda'] ?? false,
      audioUrl: json['audio'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'numberInSurah': numberInSurah,
      'text': text,
      'surahNumber': surahNumber,
      'juz': juz,
      'manzil': manzil,
      'page': page,
      'ruku': ruku,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
      'audio': audioUrl,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'numberInSurah': numberInSurah,
      'text': text,
      'surahNumber': surahNumber,
      'juz': juz,
      'manzil': manzil,
      'page': page,
      'ruku': ruku,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda ? 1 : 0,
    };
  }

  factory Ayah.fromMap(Map<String, dynamic> map) {
    return Ayah(
      number: map['number'] ?? 0,
      numberInSurah: map['numberInSurah'] ?? 0,
      text: map['text'] ?? '',
      surahNumber: map['surahNumber'] ?? 0,
      juz: map['juz'] ?? 1,
      manzil: map['manzil'] ?? 1,
      page: map['page'] ?? 1,
      ruku: map['ruku'] ?? 1,
      hizbQuarter: map['hizbQuarter'] ?? 1,
      sajda: map['sajda'] == 1,
    );
  }

  @override
  String toString() {
    return 'Ayah{number: $number, numberInSurah: $numberInSurah, surahNumber: $surahNumber}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Ayah &&
          runtimeType == other.runtimeType &&
          number == other.number;

  @override
  int get hashCode => number.hashCode;

  // Helper methods
  String get formattedText => '$text ﴿$numberInSurah﴾';

  String get ayahReference => '$surahNumber:$numberInSurah';

  bool get hasSajda => sajda;
}
