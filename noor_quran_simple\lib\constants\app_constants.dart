import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'نور القرآن';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'تطبيق شامل لقراءة القرآن الكريم مع التلاوات الصوتية والميزات الذكية';

  // Colors
  static const Color primaryColor = Color(0xFF1B4332);
  static const Color secondaryColor = Color(0xFF2D6A4F);
  static const Color accentColor = Color(0xFFD4AF37);
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color textPrimaryColor = Color(0xFF212529);
  static const Color textSecondaryColor = Color(0xFF6C757D);

  // Dark Theme Colors
  static const Color darkPrimaryColor = Color(0xFF0F2419);
  static const Color darkSecondaryColor = Color(0xFF1B4332);
  static const Color darkAccentColor = Color(0xFFD4AF37);
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkSurfaceColor = Color(0xFF1E1E1E);
  static const Color darkTextPrimaryColor = Color(0xFFE8F5E8);
  static const Color darkTextSecondaryColor = Color(0xFFB0BEC5);

  // Font Sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 20.0;
  static const double fontSizeXLarge = 24.0;
  static const double fontSizeXXLarge = 28.0;

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;

  // Animation Duration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // API URLs
  static const String quranApiBaseUrl = 'https://api.alquran.cloud/v1';
  static const String audioBaseUrl = 'https://server8.mp3quran.net';

  // Database
  static const String databaseName = 'noor_quran.db';
  static const int databaseVersion = 1;

  // Shared Preferences Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyFontSize = 'font_size';
  static const String keyLastReadSurah = 'last_read_surah';
  static const String keyLastReadAyah = 'last_read_ayah';
  static const String keySelectedReciter = 'selected_reciter';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyDailyReminder = 'daily_reminder';
  static const String keyAutoPlay = 'auto_play';
  static const String keyRepeatMode = 'repeat_mode';

  // Quran Data
  static const int totalSurahs = 114;
  static const int totalAyahs = 6236;
  static const int totalJuz = 30;
  static const int totalHizb = 60;

  // Audio Settings
  static const double defaultPlaybackSpeed = 1.0;
  static const double minPlaybackSpeed = 0.5;
  static const double maxPlaybackSpeed = 2.0;

  // UI Constants
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 60.0;
  static const double fabSize = 56.0;

  // Grid Settings
  static const int surahGridCrossAxisCount = 2;
  static const double surahGridAspectRatio = 1.5;

  // List of Popular Reciters
  static const List<Map<String, String>> popularReciters = [
    {'name': 'عبد الباسط عبد الصمد', 'identifier': 'abdulbasit_abdulsamad'},
    {'name': 'مشاري العفاسي', 'identifier': 'mishari_alafasy'},
    {'name': 'عبد الرحمن السديس', 'identifier': 'abdurrahman_sudais'},
    {'name': 'أحمد العجمي', 'identifier': 'ahmed_alajamy'},
    {'name': 'سعد الغامدي', 'identifier': 'saad_alghamdi'},
    {'name': 'ياسر الدوسري', 'identifier': 'yasser_aldosari'},
    {'name': 'ماهر المعيقلي', 'identifier': 'maher_almuaiqly'},
    {'name': 'محمد صديق المنشاوي', 'identifier': 'mohamed_siddiq_alminshawi'},
    // القراء الموريتانيون
    {'name': 'الشيخ محمد ولد سيديا', 'identifier': 'mohamed_ould_sidia'},
    {'name': 'الشيخ الحسين ولد أحمدو', 'identifier': 'hussein_ould_ahmadou'},
    {'name': 'القارئ محمد ولد المامي', 'identifier': 'mohamed_ould_mammi'},
    {
      'name': 'الشيخ عبد الله ولد عبد المالك',
      'identifier': 'abdullah_ould_abdulmalik',
    },
    {
      'name': 'الشيخ محمد سالم ولد عبد الودود (عدود)',
      'identifier': 'mohamed_salem_ould_adoud',
    },
    {'name': 'الشيخ أحمدو ولد لمرابط', 'identifier': 'ahmadou_ould_lmarabit'},
    {
      'name': 'القارئ محمد فاضل ولد محمد الأمين',
      'identifier': 'mohamed_fadel_ould_mohamed_amin',
    },
  ];

  // Error Messages
  static const String errorNetworkConnection = 'تحقق من اتصالك بالإنترنت';
  static const String errorLoadingData = 'حدث خطأ في تحميل البيانات';
  static const String errorPlayingAudio = 'حدث خطأ في تشغيل الصوت';
  static const String errorSavingData = 'حدث خطأ في حفظ البيانات';

  // Success Messages
  static const String successDataSaved = 'تم حفظ البيانات بنجاح';
  static const String successBookmarkAdded = 'تم إضافة العلامة المرجعية';
  static const String successBookmarkRemoved = 'تم حذف العلامة المرجعية';
}
