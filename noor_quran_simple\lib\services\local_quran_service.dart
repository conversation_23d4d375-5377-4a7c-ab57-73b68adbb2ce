import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';
import '../models/search_result.dart';

class LocalQuranService {
  static List<Surah>? _cachedSurahs;

  /// جلب القرآن الكامل من الملف المحلي
  static Future<List<Surah>> getLocalQuran() async {
    if (_cachedSurahs != null) {
      return _cachedSurahs!;
    }

    try {
      debugPrint('محاولة قراءة الملف المحلي...');

      // قراءة الملف من assets
      final String jsonString = await rootBundle.loadString(
        'assets/data/quran_data.json',
      );
      debugPrint('تم قراءة الملف بنجاح، الحجم: ${jsonString.length}');

      final Map<String, dynamic> jsonData = json.decode(jsonString);
      debugPrint('تم تحليل JSON بنجاح');

      // تحويل البيانات إلى قائمة السور
      final surahsData = jsonData['surahs'];
      if (surahsData == null || surahsData is! List) {
        throw Exception('بيانات السور غير صحيحة في الملف');
      }

      debugPrint('عدد السور في الملف: ${surahsData.length}');

      _cachedSurahs = surahsData
          .map((surahJson) => Surah.fromJson(surahJson as Map<String, dynamic>))
          .toList();

      debugPrint('تم تحويل السور بنجاح');
      return _cachedSurahs!;
    } catch (e) {
      debugPrint('خطأ في قراءة الملف المحلي: $e');
      throw Exception('فشل في قراءة القرآن من الملف المحلي: $e');
    }
  }

  /// جلب سورة محددة
  static Future<Surah?> getLocalSurah(int surahNumber) async {
    try {
      final surahs = await getLocalQuran();
      return surahs.firstWhere(
        (surah) => surah.number == surahNumber,
        orElse: () => throw Exception('السورة غير موجودة'),
      );
    } catch (e) {
      return null;
    }
  }

  /// البحث في القرآن المحلي
  static Future<List<SearchResult>> searchLocalQuran(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      final surahs = await getLocalQuran();
      final List<SearchResult> results = [];

      for (final surah in surahs) {
        for (final ayah in surah.ayahs) {
          if (ayah.text.contains(query)) {
            results.add(
              SearchResult(
                surahNumber: surah.number,
                surahName: surah.name,
                ayahNumber: ayah.numberInSurah,
                ayahText: ayah.text,
                highlightedText: _highlightSearchTerm(ayah.text, query),
              ),
            );
          }
        }
      }

      return results;
    } catch (e) {
      throw Exception('فشل في البحث: $e');
    }
  }

  /// تمييز نص البحث
  static String _highlightSearchTerm(String text, String searchTerm) {
    if (searchTerm.isEmpty) return text;

    return text.replaceAll(searchTerm, '<mark>$searchTerm</mark>');
  }

  /// جلب آية محددة
  static Future<Ayah?> getLocalAyah(int surahNumber, int ayahNumber) async {
    try {
      final surah = await getLocalSurah(surahNumber);
      if (surah == null) return null;

      return surah.ayahs.firstWhere(
        (ayah) => ayah.numberInSurah == ayahNumber,
        orElse: () => throw Exception('الآية غير موجودة'),
      );
    } catch (e) {
      return null;
    }
  }

  /// جلب عدد الآيات في سورة
  static Future<int> getSurahAyahCount(int surahNumber) async {
    try {
      final surah = await getLocalSurah(surahNumber);
      return surah?.numberOfAyahs ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// جلب أسماء السور فقط
  static Future<List<Map<String, dynamic>>> getSurahNames() async {
    try {
      final surahs = await getLocalQuran();
      return surahs
          .map(
            (surah) => {
              'number': surah.number,
              'name': surah.name,
              'englishName': surah.englishName,
              'numberOfAyahs': surah.numberOfAyahs,
              'revelationType': surah.revelationType,
            },
          )
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب أسماء السور: $e');
    }
  }

  /// مسح الكاش
  static void clearCache() {
    _cachedSurahs = null;
  }

  /// التحقق من توفر البيانات المحلية
  static Future<bool> isLocalDataAvailable() async {
    try {
      await rootBundle.loadString('assets/data/complete_quran.json');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// جلب آيات متتالية من سورة
  static Future<List<Ayah>> getSurahAyahs(
    int surahNumber, {
    int? startAyah,
    int? endAyah,
  }) async {
    try {
      final surah = await getLocalSurah(surahNumber);
      if (surah == null) return [];

      List<Ayah> ayahs = surah.ayahs;

      if (startAyah != null) {
        ayahs = ayahs.where((ayah) => ayah.numberInSurah >= startAyah).toList();
      }

      if (endAyah != null) {
        ayahs = ayahs.where((ayah) => ayah.numberInSurah <= endAyah).toList();
      }

      return ayahs;
    } catch (e) {
      return [];
    }
  }

  /// جلب آية عشوائية
  static Future<Map<String, dynamic>?> getRandomAyah() async {
    try {
      final surahs = await getLocalQuran();
      if (surahs.isEmpty) return null;

      // اختيار سورة عشوائية
      final randomSurah =
          surahs[(DateTime.now().millisecondsSinceEpoch % surahs.length)];

      if (randomSurah.ayahs.isEmpty) return null;

      // اختيار آية عشوائية من السورة
      final randomAyah =
          randomSurah.ayahs[(DateTime.now().millisecondsSinceEpoch %
              randomSurah.ayahs.length)];

      return {'surah': randomSurah, 'ayah': randomAyah};
    } catch (e) {
      return null;
    }
  }

  /// إحصائيات القرآن
  static Future<Map<String, int>> getQuranStats() async {
    try {
      final surahs = await getLocalQuran();

      int totalAyahs = 0;
      int meccanSurahs = 0;
      int medinanSurahs = 0;

      for (final surah in surahs) {
        totalAyahs += surah.numberOfAyahs;
        if (surah.revelationType == 'Meccan') {
          meccanSurahs++;
        } else {
          medinanSurahs++;
        }
      }

      return {
        'totalSurahs': surahs.length,
        'totalAyahs': totalAyahs,
        'meccanSurahs': meccanSurahs,
        'medinanSurahs': medinanSurahs,
      };
    } catch (e) {
      return {
        'totalSurahs': 0,
        'totalAyahs': 0,
        'meccanSurahs': 0,
        'medinanSurahs': 0,
      };
    }
  }
}
