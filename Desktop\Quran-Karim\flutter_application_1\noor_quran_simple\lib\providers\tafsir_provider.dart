import 'package:flutter/material.dart';

class TafsirProvider extends ChangeNotifier {
  String _selectedTafsir = 'ibn_kathir';
  bool _isLoading = false;
  Map<String, dynamic>? _currentTafsir;

  String get selectedTafsir => _selectedTafsir;
  bool get isLoading => _isLoading;
  Map<String, dynamic>? get currentTafsir => _currentTafsir;

  final List<Map<String, String>> availableTafsirs = [
    {'id': 'ibn_kathir', 'name': 'تفسير ابن كثير'},
    {'id': 'al_tabari', 'name': 'تفسير الطبري'},
    {'id': 'al_qurtubi', 'name': 'تفسير القرطبي'},
    {'id': 'al_jalalayn', 'name': 'تفسير الجلالين'},
  ];

  void setSelectedTafsir(String tafsirId) {
    _selectedTafsir = tafsirId;
    notifyListeners();
  }

  Future<void> loadTafsir(int surah, int ayah) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate loading tafsir data
      await Future.delayed(const Duration(seconds: 1));
      
      _currentTafsir = {
        'surah': surah,
        'ayah': ayah,
        'tafsir': 'هذا نص تفسير تجريبي للآية $ayah من سورة رقم $surah',
        'source': _selectedTafsir,
      };
    } catch (e) {
      debugPrint('Error loading tafsir: $e');
      _currentTafsir = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  String getTafsirName(String tafsirId) {
    final tafsir = availableTafsirs.firstWhere(
      (t) => t['id'] == tafsirId,
      orElse: () => {'name': 'غير معروف'},
    );
    return tafsir['name'] ?? 'غير معروف';
  }
}
