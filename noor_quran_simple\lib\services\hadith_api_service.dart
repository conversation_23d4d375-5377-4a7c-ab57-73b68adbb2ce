import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/library_models.dart';

/// خدمة API للأحاديث الشريفة
class HadithApiService {
  static const String _baseUrl = 'https://hadithapi.com/api';
  static const String _apiKey = 'YOUR_API_KEY'; // يجب الحصول عليه من hadithapi.com
  
  // مصادر الأحاديث المتاحة
  static const Map<String, String> hadithSources = {
    'bukhari': 'صحيح البخاري',
    'muslim': 'صحيح مسلم',
    'abudawud': 'سنن أبي داود',
    'tirmidhi': 'سنن الترمذي',
    'nasai': 'سنن النسائي',
    'ibnmajah': 'سنن ابن ماجه',
    'malik': 'موطأ مالك',
    'ahmad': 'مسند أحمد',
  };

  /// جلب الأحاديث من مصدر محدد
  static Future<List<Hadith>> getHadithsBySource(
    String source, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      debugPrint('🔍 جلب الأحاديث من $source...');
      
      final url = Uri.parse('$_baseUrl/hadiths')
          .replace(queryParameters: {
        'apikey': _apiKey,
        'book': source,
        'page': page.toString(),
        'limit': limit.toString(),
      });

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadithsList = data['hadiths'] as List<dynamic>? ?? [];
        
        return hadithsList
            .map((hadithJson) => Hadith.fromJson(hadithJson as Map<String, dynamic>))
            .toList();
      } else {
        debugPrint('❌ خطأ في جلب الأحاديث: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في الاتصال بـ API الأحاديث: $e');
      return [];
    }
  }

  /// البحث في الأحاديث
  static Future<List<Hadith>> searchHadiths(
    String query, {
    String? source,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      debugPrint('🔍 البحث في الأحاديث: $query');
      
      final queryParams = <String, String>{
        'apikey': _apiKey,
        'query': query,
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (source != null) {
        queryParams['book'] = source;
      }

      final url = Uri.parse('$_baseUrl/hadiths/search')
          .replace(queryParameters: queryParams);

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadithsList = data['hadiths'] as List<dynamic>? ?? [];
        
        return hadithsList
            .map((hadithJson) => Hadith.fromJson(hadithJson as Map<String, dynamic>))
            .toList();
      } else {
        debugPrint('❌ خطأ في البحث: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في البحث في الأحاديث: $e');
      return [];
    }
  }

  /// جلب حديث محدد بالرقم
  static Future<Hadith?> getHadithById(String id) async {
    try {
      debugPrint('🔍 جلب الحديث رقم: $id');
      
      final url = Uri.parse('$_baseUrl/hadiths/$id')
          .replace(queryParameters: {'apikey': _apiKey});

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Hadith.fromJson(data['hadith'] as Map<String, dynamic>);
      } else {
        debugPrint('❌ خطأ في جلب الحديث: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب الحديث: $e');
      return null;
    }
  }

  /// جلب الأحاديث حسب الراوي
  static Future<List<Hadith>> getHadithsByNarrator(
    String narrator, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      debugPrint('🔍 جلب أحاديث الراوي: $narrator');
      
      final url = Uri.parse('$_baseUrl/hadiths/narrator')
          .replace(queryParameters: {
        'apikey': _apiKey,
        'narrator': narrator,
        'page': page.toString(),
        'limit': limit.toString(),
      });

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadithsList = data['hadiths'] as List<dynamic>? ?? [];
        
        return hadithsList
            .map((hadithJson) => Hadith.fromJson(hadithJson as Map<String, dynamic>))
            .toList();
      } else {
        debugPrint('❌ خطأ في جلب أحاديث الراوي: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب أحاديث الراوي: $e');
      return [];
    }
  }

  /// جلب الأحاديث العشوائية
  static Future<List<Hadith>> getRandomHadiths({int count = 5}) async {
    try {
      debugPrint('🔍 جلب أحاديث عشوائية...');
      
      final url = Uri.parse('$_baseUrl/hadiths/random')
          .replace(queryParameters: {
        'apikey': _apiKey,
        'count': count.toString(),
      });

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final hadithsList = data['hadiths'] as List<dynamic>? ?? [];
        
        return hadithsList
            .map((hadithJson) => Hadith.fromJson(hadithJson as Map<String, dynamic>))
            .toList();
      } else {
        debugPrint('❌ خطأ في جلب الأحاديث العشوائية: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب الأحاديث العشوائية: $e');
      return [];
    }
  }

  /// جلب إحصائيات الأحاديث
  static Future<Map<String, int>> getHadithStats() async {
    try {
      debugPrint('📊 جلب إحصائيات الأحاديث...');
      
      final url = Uri.parse('$_baseUrl/hadiths/stats')
          .replace(queryParameters: {'apikey': _apiKey});

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Map<String, int>.from(data['stats'] as Map<String, dynamic>);
      } else {
        debugPrint('❌ خطأ في جلب الإحصائيات: ${response.statusCode}');
        return {};
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
      return {};
    }
  }

  /// الأحاديث المحلية (للاختبار بدون API)
  static List<Hadith> getLocalHadiths() {
    return [
      const Hadith(
        id: '1',
        text: 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى',
        narrator: 'عمر بن الخطاب',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        commentary: 'هذا الحديث أصل في اعتبار المقاصد في الأعمال',
        keywords: ['النية', 'الأعمال', 'المقاصد'],
      ),
      const Hadith(
        id: '2',
        text: 'من كان يؤمن بالله واليوم الآخر فليقل خيراً أو ليصمت',
        narrator: 'أبو هريرة',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        commentary: 'حديث في آداب الكلام وحفظ اللسان',
        keywords: ['الكلام', 'الصمت', 'الأدب'],
      ),
      const Hadith(
        id: '3',
        text: 'المؤمن للمؤمن كالبنيان يشد بعضه بعضاً',
        narrator: 'أبو موسى الأشعري',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        commentary: 'حديث في التعاون والتكافل بين المؤمنين',
        keywords: ['التعاون', 'الأخوة', 'المؤمنين'],
      ),
      const Hadith(
        id: '4',
        text: 'لا يؤمن أحدكم حتى يحب لأخيه ما يحب لنفسه',
        narrator: 'أنس بن مالك',
        source: 'صحيح البخاري',
        grade: 'صحيح',
        commentary: 'حديث في كمال الإيمان والمحبة للآخرين',
        keywords: ['الإيمان', 'المحبة', 'الأخوة'],
      ),
      const Hadith(
        id: '5',
        text: 'من حسن إسلام المرء تركه ما لا يعنيه',
        narrator: 'أبو هريرة',
        source: 'سنن الترمذي',
        grade: 'حسن',
        commentary: 'حديث في آداب المسلم وترك الفضول',
        keywords: ['الإسلام', 'الأدب', 'الفضول'],
      ),
    ];
  }

  /// اختبار الاتصال بـ API
  static Future<bool> testApiConnection() async {
    try {
      debugPrint('🔍 اختبار الاتصال بـ API الأحاديث...');
      
      final url = Uri.parse('$_baseUrl/test')
          .replace(queryParameters: {'apikey': _apiKey});

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        debugPrint('✅ الاتصال بـ API الأحاديث ناجح');
        return true;
      } else {
        debugPrint('❌ فشل الاتصال بـ API الأحاديث: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الاتصال: $e');
      return false;
    }
  }
}
