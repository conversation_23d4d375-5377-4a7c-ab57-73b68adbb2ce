import 'package:flutter/material.dart';
import '../models/achievement.dart';

/// بطاقة عرض الإنجاز - تصميم جذاب ومحفز
class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final bool isUnlocked;
  final VoidCallback? onTap;

  const AchievementCard({
    super.key,
    required this.achievement,
    required this.isUnlocked,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isUnlocked ? 8 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: isUnlocked ? _getUnlockedGradient() : null,
            border: achievement.isSpecial && isUnlocked
                ? Border.all(color: Colors.amber, width: 2)
                : null,
          ),
          child: Row(
            children: [
              // أيقونة الإنجاز
              _buildAchievementIcon(),
              const SizedBox(width: 16),
              
              // تفاصيل الإنجاز
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            achievement.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isUnlocked ? Colors.white : theme.textTheme.titleLarge?.color,
                            ),
                          ),
                        ),
                        if (achievement.isSpecial)
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 20,
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    
                    // الوصف
                    Text(
                      achievement.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: isUnlocked 
                            ? Colors.white70 
                            : theme.textTheme.bodyMedium?.color,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // شريط التقدم
                    if (!isUnlocked) _buildProgressBar(),
                    
                    // معلومات إضافية
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // النقاط
                        Row(
                          children: [
                            Icon(
                              Icons.stars,
                              size: 16,
                              color: isUnlocked ? Colors.amber : Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${achievement.rewardPoints} نقطة',
                              style: TextStyle(
                                fontSize: 12,
                                color: isUnlocked ? Colors.white70 : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                        
                        // الفئة
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: isUnlocked 
                                ? Colors.white.withValues(alpha: 0.2)
                                : achievement.category.name == 'reading' 
                                    ? Colors.blue.withValues(alpha: 0.1)
                                    : Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            achievement.category.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              color: isUnlocked 
                                  ? Colors.white 
                                  : achievement.category.name == 'reading'
                                      ? Colors.blue
                                      : Colors.green,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    // تاريخ الإنجاز
                    if (isUnlocked && achievement.unlockedDate != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          'تم الإنجاز: ${_formatDate(achievement.unlockedDate!)}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white60,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة الإنجاز
  Widget _buildAchievementIcon() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isUnlocked 
            ? Colors.white.withValues(alpha: 0.2)
            : Colors.grey.withValues(alpha: 0.1),
        border: Border.all(
          color: isUnlocked ? Colors.white : Colors.grey,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          achievement.icon,
          style: TextStyle(
            fontSize: 24,
            color: isUnlocked ? null : Colors.grey,
          ),
        ),
      ),
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${achievement.currentProgress.toInt()} / ${achievement.targetValue.toInt()}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: achievement.progressPercentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(),
          ),
        ),
      ],
    );
  }

  /// الحصول على لون التقدم
  Color _getProgressColor() {
    switch (achievement.category) {
      case AchievementCategory.reading:
        return Colors.blue;
      case AchievementCategory.streak:
        return Colors.orange;
      case AchievementCategory.time:
        return Colors.green;
      case AchievementCategory.surah:
        return Colors.purple;
      case AchievementCategory.social:
        return Colors.teal;
      case AchievementCategory.special:
        return Colors.amber;
    }
  }

  /// الحصول على تدرج الإنجاز المفتوح
  LinearGradient _getUnlockedGradient() {
    switch (achievement.category) {
      case AchievementCategory.reading:
        return const LinearGradient(
          colors: [Colors.blue, Colors.blueAccent],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case AchievementCategory.streak:
        return const LinearGradient(
          colors: [Colors.orange, Colors.deepOrange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case AchievementCategory.time:
        return const LinearGradient(
          colors: [Colors.green, Colors.lightGreen],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case AchievementCategory.surah:
        return const LinearGradient(
          colors: [Colors.purple, Colors.purpleAccent],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case AchievementCategory.social:
        return const LinearGradient(
          colors: [Colors.teal, Colors.tealAccent],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case AchievementCategory.special:
        return const LinearGradient(
          colors: [Colors.amber, Colors.yellow],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// بطاقة إنجاز مصغرة للعرض السريع
class MiniAchievementCard extends StatelessWidget {
  final Achievement achievement;
  final VoidCallback? onTap;

  const MiniAchievementCard({
    super.key,
    required this.achievement,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: achievement.isUnlocked 
              ? Colors.amber.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: achievement.isUnlocked ? Colors.amber : Colors.grey,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              achievement.icon,
              style: TextStyle(
                fontSize: 20,
                color: achievement.isUnlocked ? null : Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              achievement.title,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: achievement.isUnlocked ? Colors.amber[800] : Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
