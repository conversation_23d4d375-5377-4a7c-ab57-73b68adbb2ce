import 'package:shared_preferences/shared_preferences.dart';

class ProgressService {
  static final ProgressService _instance = ProgressService._internal();
  factory ProgressService() => _instance;
  ProgressService._internal();

  // Keys for SharedPreferences
  static const String _keyReadSurahs = 'read_surahs';
  static const String _keyReadAyahs = 'read_ayahs';
  static const String _keyReadingStreak = 'reading_streak';
  static const String _keyLastReadDate = 'last_read_date';
  static const String _keyTotalReadingTime = 'total_reading_time';
  static const String _keyDailyGoal = 'daily_goal';
  static const String _keyWeeklyGoal = 'weekly_goal';
  static const String _keyMonthlyGoal = 'monthly_goal';

  // Reading progress tracking
  Future<void> markSurahAsRead(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readSurahs = prefs.getStringList(_keyReadSurahs) ?? [];

      if (!readSurahs.contains(surahNumber.toString())) {
        readSurahs.add(surahNumber.toString());
        await prefs.setStringList(_keyReadSurahs, readSurahs);
        await _updateReadingStreak();
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> markAyahAsRead(int ayahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readAyahs = prefs.getStringList(_keyReadAyahs) ?? [];

      if (!readAyahs.contains(ayahNumber.toString())) {
        readAyahs.add(ayahNumber.toString());
        await prefs.setStringList(_keyReadAyahs, readAyahs);
        await _updateReadingStreak();
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<List<int>> getReadSurahs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readSurahs = prefs.getStringList(_keyReadSurahs) ?? [];
      return readSurahs
          .map((s) => int.tryParse(s) ?? 0)
          .where((n) => n > 0)
          .toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<int>> getReadAyahs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readAyahs = prefs.getStringList(_keyReadAyahs) ?? [];
      return readAyahs
          .map((s) => int.tryParse(s) ?? 0)
          .where((n) => n > 0)
          .toList();
    } catch (e) {
      return [];
    }
  }

  Future<bool> isSurahRead(int surahNumber) async {
    final readSurahs = await getReadSurahs();
    return readSurahs.contains(surahNumber);
  }

  Future<bool> isAyahRead(int ayahNumber) async {
    final readAyahs = await getReadAyahs();
    return readAyahs.contains(ayahNumber);
  }

  // Reading streak tracking
  Future<void> _updateReadingStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      final lastReadDate = prefs.getString(_keyLastReadDate);

      if (lastReadDate == null) {
        // First time reading
        await prefs.setInt(_keyReadingStreak, 1);
        await prefs.setString(_keyLastReadDate, todayString);
      } else if (lastReadDate != todayString) {
        // Reading on a new day
        final lastDate = DateTime.tryParse(lastReadDate.replaceAll('-', ''));
        final yesterday = today.subtract(const Duration(days: 1));
        final yesterdayString =
            '${yesterday.year}-${yesterday.month}-${yesterday.day}';

        if (lastReadDate == yesterdayString) {
          // Consecutive day - increment streak
          final currentStreak = prefs.getInt(_keyReadingStreak) ?? 0;
          await prefs.setInt(_keyReadingStreak, currentStreak + 1);
        } else {
          // Streak broken - reset to 1
          await prefs.setInt(_keyReadingStreak, 1);
        }

        await prefs.setString(_keyLastReadDate, todayString);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<int> getReadingStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_keyReadingStreak) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // Reading time tracking
  Future<void> addReadingTime(Duration duration) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentTime = prefs.getInt(_keyTotalReadingTime) ?? 0;
      await prefs.setInt(
        _keyTotalReadingTime,
        currentTime + duration.inMinutes,
      );
    } catch (e) {
      // Handle error silently
    }
  }

  Future<Duration> getTotalReadingTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final minutes = prefs.getInt(_keyTotalReadingTime) ?? 0;
      return Duration(minutes: minutes);
    } catch (e) {
      return Duration.zero;
    }
  }

  // Goals management
  Future<void> setDailyGoal(int ayahsCount) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_keyDailyGoal, ayahsCount);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> setWeeklyGoal(int surahsCount) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_keyWeeklyGoal, surahsCount);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> setMonthlyGoal(int surahsCount) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_keyMonthlyGoal, surahsCount);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<int> getDailyGoal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_keyDailyGoal) ?? 10; // Default: 10 ayahs per day
    } catch (e) {
      return 10;
    }
  }

  Future<int> getWeeklyGoal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_keyWeeklyGoal) ?? 3; // Default: 3 surahs per week
    } catch (e) {
      return 3;
    }
  }

  Future<int> getMonthlyGoal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_keyMonthlyGoal) ??
          10; // Default: 10 surahs per month
    } catch (e) {
      return 10;
    }
  }

  // Progress calculations
  Future<double> getDailyProgress() async {
    try {
      final goal = await getDailyGoal();
      if (goal == 0) return 0.0;

      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      final lastReadDate = await SharedPreferences.getInstance().then(
        (prefs) => prefs.getString(_keyLastReadDate),
      );

      if (lastReadDate == todayString) {
        // Count ayahs read today (simplified - in real app, track daily separately)
        final readAyahs = await getReadAyahs();
        return (readAyahs.length % goal) / goal;
      }

      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  Future<double> getWeeklyProgress() async {
    try {
      final goal = await getWeeklyGoal();
      if (goal == 0) return 0.0;

      final readSurahs = await getReadSurahs();
      final thisWeekSurahs = readSurahs.length % goal;
      return thisWeekSurahs / goal;
    } catch (e) {
      return 0.0;
    }
  }

  Future<double> getMonthlyProgress() async {
    try {
      final goal = await getMonthlyGoal();
      if (goal == 0) return 0.0;

      final readSurahs = await getReadSurahs();
      final thisMonthSurahs = readSurahs.length % goal;
      return thisMonthSurahs / goal;
    } catch (e) {
      return 0.0;
    }
  }

  // Statistics
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final readSurahs = await getReadSurahs();
      final readAyahs = await getReadAyahs();
      final streak = await getReadingStreak();
      final totalTime = await getTotalReadingTime();
      final dailyProgress = await getDailyProgress();
      final weeklyProgress = await getWeeklyProgress();
      final monthlyProgress = await getMonthlyProgress();

      return {
        'readSurahs': readSurahs.length,
        'readAyahs': readAyahs.length,
        'readingStreak': streak,
        'totalReadingTime': totalTime.inMinutes,
        'dailyProgress': dailyProgress,
        'weeklyProgress': weeklyProgress,
        'monthlyProgress': monthlyProgress,
        'completionPercentage': (readSurahs.length / 114) * 100,
      };
    } catch (e) {
      return {
        'readSurahs': 0,
        'readAyahs': 0,
        'readingStreak': 0,
        'totalReadingTime': 0,
        'dailyProgress': 0.0,
        'weeklyProgress': 0.0,
        'monthlyProgress': 0.0,
        'completionPercentage': 0.0,
      };
    }
  }

  // Reset progress
  Future<void> resetProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyReadSurahs);
      await prefs.remove(_keyReadAyahs);
      await prefs.remove(_keyReadingStreak);
      await prefs.remove(_keyLastReadDate);
      await prefs.remove(_keyTotalReadingTime);
    } catch (e) {
      // Handle error silently
    }
  }
}
