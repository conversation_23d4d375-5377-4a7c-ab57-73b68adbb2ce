name: noor_quran
description: "تطبيق نور القرآن - تطبيق شامل لقراءة القرآن الكريم مع التلاوات الصوتية والميزات الذكية"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1

  # State Management
  provider: ^6.1.2

  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.3+1

  # Audio Player
  just_audio: ^0.9.37
  audio_service: ^0.18.12

  # Database
  sqflite: ^2.3.3+1
  path: ^1.9.0

  # Storage & Preferences
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3

  # Permissions
  permission_handler: ^11.3.1

  # Notifications
  flutter_local_notifications: ^17.1.2
  timezone: ^0.9.4

  # UI Components
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0

  # Utilities
  intl: ^0.19.0
  url_launcher: ^6.2.6
  share_plus: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/fonts/
  #   - assets/audio/
  #   - assets/data/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom Fonts (will be added later)
  # fonts:
  #   - family: Amiri
  #     fonts:
  #       - asset: assets/fonts/Amiri-Regular.ttf
  #       - asset: assets/fonts/Amiri-Bold.ttf
  #         weight: 700
