import 'dart:convert';

/// نموذج الإنجاز
class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final AchievementCategory category;
  final double targetValue;
  final int rewardPoints;
  final bool isSpecial;
  final DateTime? unlockedDate;
  final double currentProgress;
  final bool isUnlocked;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.category,
    required this.targetValue,
    required this.rewardPoints,
    this.isSpecial = false,
    this.unlockedDate,
    this.currentProgress = 0.0,
    this.isUnlocked = false,
  });

  /// نسبة التقدم (0.0 - 1.0)
  double get progressPercentage => 
      targetValue > 0 ? (currentProgress / targetValue).clamp(0.0, 1.0) : 0.0;

  /// هل الإنجاز مكتمل؟
  bool get isCompleted => currentProgress >= targetValue;

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'category': category.name,
      'targetValue': targetValue,
      'rewardPoints': rewardPoints,
      'isSpecial': isSpecial,
      'unlockedDate': unlockedDate?.toIso8601String(),
      'currentProgress': currentProgress,
      'isUnlocked': isUnlocked,
    };
  }

  /// إنشاء من Map
  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      icon: map['icon'] ?? '🏆',
      category: AchievementCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => AchievementCategory.reading,
      ),
      targetValue: map['targetValue']?.toDouble() ?? 0.0,
      rewardPoints: map['rewardPoints']?.toInt() ?? 0,
      isSpecial: map['isSpecial'] ?? false,
      unlockedDate: map['unlockedDate'] != null 
          ? DateTime.parse(map['unlockedDate']) 
          : null,
      currentProgress: map['currentProgress']?.toDouble() ?? 0.0,
      isUnlocked: map['isUnlocked'] ?? false,
    );
  }

  /// تحويل إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء من JSON
  factory Achievement.fromJson(String source) => 
      Achievement.fromMap(json.decode(source));

  /// نسخ مع تعديل
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    AchievementCategory? category,
    double? targetValue,
    int? rewardPoints,
    bool? isSpecial,
    DateTime? unlockedDate,
    double? currentProgress,
    bool? isUnlocked,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      category: category ?? this.category,
      targetValue: targetValue ?? this.targetValue,
      rewardPoints: rewardPoints ?? this.rewardPoints,
      isSpecial: isSpecial ?? this.isSpecial,
      unlockedDate: unlockedDate ?? this.unlockedDate,
      currentProgress: currentProgress ?? this.currentProgress,
      isUnlocked: isUnlocked ?? this.isUnlocked,
    );
  }

  @override
  String toString() {
    return 'Achievement(id: $id, title: $title, progress: $currentProgress/$targetValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Achievement &&
      other.id == id &&
      other.title == title &&
      other.targetValue == targetValue;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ targetValue.hashCode;
  }
}

/// فئات الإنجازات
enum AchievementCategory {
  reading('قراءة', '📖'),
  streak('سلسلة', '🔥'),
  time('وقت', '⏰'),
  surah('سور', '📜'),
  social('اجتماعي', '👥'),
  special('خاص', '⭐');

  const AchievementCategory(this.displayName, this.icon);
  
  final String displayName;
  final String icon;
}

/// نموذج التحدي
class Challenge {
  final String id;
  final String title;
  final String description;
  final ChallengeType type;
  final double targetValue;
  final int rewardPoints;
  final DateTime expiryDate;
  final double currentProgress;
  final bool isCompleted;
  final bool isActive;

  const Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.targetValue,
    required this.rewardPoints,
    required this.expiryDate,
    this.currentProgress = 0.0,
    this.isCompleted = false,
    this.isActive = true,
  });

  /// نسبة التقدم
  double get progressPercentage => 
      targetValue > 0 ? (currentProgress / targetValue).clamp(0.0, 1.0) : 0.0;

  /// هل التحدي منتهي الصلاحية؟
  bool get isExpired => DateTime.now().isAfter(expiryDate);

  /// الوقت المتبقي
  Duration get timeRemaining => 
      isExpired ? Duration.zero : expiryDate.difference(DateTime.now());

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'targetValue': targetValue,
      'rewardPoints': rewardPoints,
      'expiryDate': expiryDate.toIso8601String(),
      'currentProgress': currentProgress,
      'isCompleted': isCompleted,
      'isActive': isActive,
    };
  }

  /// إنشاء من Map
  factory Challenge.fromMap(Map<String, dynamic> map) {
    return Challenge(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: ChallengeType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ChallengeType.daily,
      ),
      targetValue: map['targetValue']?.toDouble() ?? 0.0,
      rewardPoints: map['rewardPoints']?.toInt() ?? 0,
      expiryDate: DateTime.parse(map['expiryDate']),
      currentProgress: map['currentProgress']?.toDouble() ?? 0.0,
      isCompleted: map['isCompleted'] ?? false,
      isActive: map['isActive'] ?? true,
    );
  }

  /// نسخ مع تعديل
  Challenge copyWith({
    String? id,
    String? title,
    String? description,
    ChallengeType? type,
    double? targetValue,
    int? rewardPoints,
    DateTime? expiryDate,
    double? currentProgress,
    bool? isCompleted,
    bool? isActive,
  }) {
    return Challenge(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      targetValue: targetValue ?? this.targetValue,
      rewardPoints: rewardPoints ?? this.rewardPoints,
      expiryDate: expiryDate ?? this.expiryDate,
      currentProgress: currentProgress ?? this.currentProgress,
      isCompleted: isCompleted ?? this.isCompleted,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Challenge(id: $id, title: $title, progress: $currentProgress/$targetValue)';
  }
}

/// أنواع التحديات
enum ChallengeType {
  daily('يومي', '📅'),
  weekly('أسبوعي', '📆'),
  monthly('شهري', '🗓️'),
  special('خاص', '⭐');

  const ChallengeType(this.displayName, this.icon);
  
  final String displayName;
  final String icon;
}

/// إحصائيات الإنجازات
class AchievementStats {
  final int totalAchievements;
  final int unlockedAchievements;
  final int totalPoints;
  final double completionPercentage;
  final int specialAchievements;
  final Achievement? latestAchievement;
  final List<Achievement> recentAchievements;

  const AchievementStats({
    required this.totalAchievements,
    required this.unlockedAchievements,
    required this.totalPoints,
    required this.completionPercentage,
    required this.specialAchievements,
    this.latestAchievement,
    this.recentAchievements = const [],
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalAchievements': totalAchievements,
      'unlockedAchievements': unlockedAchievements,
      'totalPoints': totalPoints,
      'completionPercentage': completionPercentage,
      'specialAchievements': specialAchievements,
      'latestAchievement': latestAchievement?.toMap(),
      'recentAchievements': recentAchievements.map((a) => a.toMap()).toList(),
    };
  }
}
