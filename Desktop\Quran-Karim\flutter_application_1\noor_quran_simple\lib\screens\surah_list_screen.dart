import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/audio_provider.dart';
import 'surah_reading_screen.dart';

class SurahListScreen extends StatelessWidget {
  const SurahListScreen({super.key});

  final List<Map<String, dynamic>> surahs = const [
    {
      'number': 1,
      'name': 'الفاتحة',
      'englishName': 'Al-Fatiha',
      'ayahs': 7,
      'type': 'مكية',
    },
    {
      'number': 2,
      'name': 'البقرة',
      'englishName': 'Al-Baqarah',
      'ayahs': 286,
      'type': 'مدنية',
    },
    {
      'number': 3,
      'name': 'آل عمران',
      'englishName': 'Ali \'Imran',
      'ayahs': 200,
      'type': 'مدنية',
    },
    {
      'number': 4,
      'name': 'النساء',
      'englishName': 'An-Nisa',
      'ayahs': 176,
      'type': 'مدنية',
    },
    {
      'number': 5,
      'name': 'المائدة',
      'englishName': 'Al-Ma\'idah',
      'ayahs': 120,
      'type': 'مدنية',
    },
    {
      'number': 6,
      'name': 'الأنعام',
      'englishName': 'Al-An\'am',
      'ayahs': 165,
      'type': 'مكية',
    },
    {
      'number': 7,
      'name': 'الأعراف',
      'englishName': 'Al-A\'raf',
      'ayahs': 206,
      'type': 'مكية',
    },
    {
      'number': 8,
      'name': 'الأنفال',
      'englishName': 'Al-Anfal',
      'ayahs': 75,
      'type': 'مدنية',
    },
    {
      'number': 9,
      'name': 'التوبة',
      'englishName': 'At-Tawbah',
      'ayahs': 129,
      'type': 'مدنية',
    },
    {
      'number': 10,
      'name': 'يونس',
      'englishName': 'Yunus',
      'ayahs': 109,
      'type': 'مكية',
    },
    {
      'number': 11,
      'name': 'هود',
      'englishName': 'Hud',
      'ayahs': 123,
      'type': 'مكية',
    },
    {
      'number': 12,
      'name': 'يوسف',
      'englishName': 'Yusuf',
      'ayahs': 111,
      'type': 'مكية',
    },
    {
      'number': 13,
      'name': 'الرعد',
      'englishName': 'Ar-Ra\'d',
      'ayahs': 43,
      'type': 'مدنية',
    },
    {
      'number': 14,
      'name': 'إبراهيم',
      'englishName': 'Ibrahim',
      'ayahs': 52,
      'type': 'مكية',
    },
    {
      'number': 15,
      'name': 'الحجر',
      'englishName': 'Al-Hijr',
      'ayahs': 99,
      'type': 'مكية',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة السور'),
        actions: [
          IconButton(
            icon: const Icon(Icons.grid_view),
            onPressed: () {
              // Toggle between list and grid view
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: surahs.length,
        itemBuilder: (context, index) {
          final surah = surahs[index];
          return _buildSurahCard(context, surah);
        },
      ),
    );
  }

  Widget _buildSurahCard(BuildContext context, Map<String, dynamic> surah) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              '${surah['number']}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ),
        ),
        title: Text(
          surah['name'],
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              surah['englishName'],
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: surah['type'] == 'مكية'
                        ? AppConstants.accentColor.withOpacity(0.2)
                        : AppConstants.secondaryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    surah['type'],
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: surah['type'] == 'مكية'
                          ? AppConstants.accentColor
                          : AppConstants.secondaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${surah['ayahs']} آية',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                return IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () {
                    final audioUrl =
                        'https://server8.mp3quran.net/afs/${surah['number'].toString().padLeft(3, '0')}.mp3';
                    audioProvider.playAudio(audioUrl);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تشغيل سورة ${surah['name']}'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                );
              },
            ),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahReadingScreen(
                surahNumber: surah['number'],
                surahName: surah['name'],
              ),
            ),
          );
        },
      ),
    );
  }
}
