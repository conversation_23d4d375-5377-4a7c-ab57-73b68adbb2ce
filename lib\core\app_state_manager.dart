import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_preferences.dart';
import '../services/analytics_service.dart';

/// مدير الحالة العام للتطبيق - لمسة خاصة لإدارة شاملة
class AppStateManager extends ChangeNotifier {
  static final AppStateManager _instance = AppStateManager._internal();
  factory AppStateManager() => _instance;
  AppStateManager._internal();

  // حالة التطبيق العامة
  bool _isInitialized = false;
  bool _isOnline = true;
  String _currentLanguage = 'ar';
  UserPreferences? _userPreferences;
  
  // إحصائيات الاستخدام
  DateTime? _lastOpenTime;
  int _dailyReadingStreak = 0;
  Duration _totalReadingTime = Duration.zero;
  int _totalAyahsRead = 0;
  
  // الإعدادات الذكية
  bool _smartNotificationsEnabled = true;
  bool _adaptiveThemeEnabled = true;
  bool _voiceControlEnabled = false;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isOnline => _isOnline;
  String get currentLanguage => _currentLanguage;
  UserPreferences? get userPreferences => _userPreferences;
  DateTime? get lastOpenTime => _lastOpenTime;
  int get dailyReadingStreak => _dailyReadingStreak;
  Duration get totalReadingTime => _totalReadingTime;
  int get totalAyahsRead => _totalAyahsRead;
  bool get smartNotificationsEnabled => _smartNotificationsEnabled;
  bool get adaptiveThemeEnabled => _adaptiveThemeEnabled;
  bool get voiceControlEnabled => _voiceControlEnabled;

  /// تهيئة التطبيق
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadUserPreferences();
      await _loadUsageStatistics();
      await _checkConnectivity();
      await _initializeAnalytics();
      
      _isInitialized = true;
      notifyListeners();
      
      debugPrint('✅ تم تهيئة مدير الحالة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الحالة: $e');
    }
  }

  /// تحميل تفضيلات المستخدم
  Future<void> _loadUserPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    
    _currentLanguage = prefs.getString('language') ?? 'ar';
    _smartNotificationsEnabled = prefs.getBool('smart_notifications') ?? true;
    _adaptiveThemeEnabled = prefs.getBool('adaptive_theme') ?? true;
    _voiceControlEnabled = prefs.getBool('voice_control') ?? false;
    
    // تحميل تفضيلات المستخدم المخصصة
    final fontSize = prefs.getDouble('font_size') ?? 16.0;
    final themeMode = prefs.getString('theme_mode') ?? 'system';
    final preferredReciter = prefs.getString('preferred_reciter') ?? '';
    
    _userPreferences = UserPreferences(
      fontSize: fontSize,
      themeMode: themeMode,
      preferredReciter: preferredReciter,
      language: _currentLanguage,
    );
  }

  /// تحميل إحصائيات الاستخدام
  Future<void> _loadUsageStatistics() async {
    final prefs = await SharedPreferences.getInstance();
    
    final lastOpenString = prefs.getString('last_open_time');
    if (lastOpenString != null) {
      _lastOpenTime = DateTime.parse(lastOpenString);
    }
    
    _dailyReadingStreak = prefs.getInt('daily_reading_streak') ?? 0;
    _totalAyahsRead = prefs.getInt('total_ayahs_read') ?? 0;
    
    final totalMinutes = prefs.getInt('total_reading_minutes') ?? 0;
    _totalReadingTime = Duration(minutes: totalMinutes);
    
    // تحديث سلسلة القراءة اليومية
    _updateDailyStreak();
  }

  /// تحديث سلسلة القراءة اليومية
  void _updateDailyStreak() {
    if (_lastOpenTime == null) return;
    
    final now = DateTime.now();
    final lastOpen = _lastOpenTime!;
    final daysDifference = now.difference(lastOpen).inDays;
    
    if (daysDifference == 0) {
      // نفس اليوم - لا تغيير
      return;
    } else if (daysDifference == 1) {
      // يوم واحد فقط - زيادة السلسلة
      _dailyReadingStreak++;
    } else {
      // أكثر من يوم - إعادة تعيين السلسلة
      _dailyReadingStreak = 1;
    }
    
    _saveDailyStreak();
  }

  /// فحص الاتصال بالإنترنت
  Future<void> _checkConnectivity() async {
    // سيتم تطبيق فحص الاتصال الفعلي لاحقاً
    _isOnline = true;
  }

  /// تهيئة نظام التحليلات
  Future<void> _initializeAnalytics() async {
    await AnalyticsService.initialize();
    await AnalyticsService.logAppOpen();
  }

  /// تحديث وقت القراءة
  void updateReadingTime(Duration additionalTime) {
    _totalReadingTime += additionalTime;
    _saveReadingTime();
    notifyListeners();
  }

  /// تحديث عدد الآيات المقروءة
  void updateAyahsRead(int count) {
    _totalAyahsRead += count;
    _saveAyahsRead();
    notifyListeners();
  }

  /// تغيير اللغة
  Future<void> changeLanguage(String language) async {
    _currentLanguage = language;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', language);
    notifyListeners();
  }

  /// تبديل الإشعارات الذكية
  Future<void> toggleSmartNotifications() async {
    _smartNotificationsEnabled = !_smartNotificationsEnabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('smart_notifications', _smartNotificationsEnabled);
    notifyListeners();
  }

  /// تبديل الثيم التكيفي
  Future<void> toggleAdaptiveTheme() async {
    _adaptiveThemeEnabled = !_adaptiveThemeEnabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('adaptive_theme', _adaptiveThemeEnabled);
    notifyListeners();
  }

  /// تبديل التحكم الصوتي
  Future<void> toggleVoiceControl() async {
    _voiceControlEnabled = !_voiceControlEnabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('voice_control', _voiceControlEnabled);
    notifyListeners();
  }

  /// حفظ السلسلة اليومية
  Future<void> _saveDailyStreak() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('daily_reading_streak', _dailyReadingStreak);
    await prefs.setString('last_open_time', DateTime.now().toIso8601String());
  }

  /// حفظ وقت القراءة
  Future<void> _saveReadingTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('total_reading_minutes', _totalReadingTime.inMinutes);
  }

  /// حفظ عدد الآيات
  Future<void> _saveAyahsRead() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('total_ayahs_read', _totalAyahsRead);
  }

  /// تسجيل جلسة قراءة جديدة
  void startReadingSession() {
    _lastOpenTime = DateTime.now();
    _updateDailyStreak();
    AnalyticsService.logReadingSessionStart();
  }

  /// إنهاء جلسة القراءة
  void endReadingSession(Duration sessionDuration, int ayahsRead) {
    updateReadingTime(sessionDuration);
    updateAyahsRead(ayahsRead);
    AnalyticsService.logReadingSessionEnd(sessionDuration, ayahsRead);
  }

  /// الحصول على إحصائيات سريعة
  Map<String, dynamic> getQuickStats() {
    return {
      'dailyStreak': _dailyReadingStreak,
      'totalReadingHours': _totalReadingTime.inHours,
      'totalAyahs': _totalAyahsRead,
      'averageDaily': _totalAyahsRead / (_dailyReadingStreak > 0 ? _dailyReadingStreak : 1),
    };
  }

  /// إعادة تعيين الإحصائيات
  Future<void> resetStatistics() async {
    _dailyReadingStreak = 0;
    _totalReadingTime = Duration.zero;
    _totalAyahsRead = 0;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('daily_reading_streak');
    await prefs.remove('total_reading_minutes');
    await prefs.remove('total_ayahs_read');
    await prefs.remove('last_open_time');
    
    notifyListeners();
  }
}
