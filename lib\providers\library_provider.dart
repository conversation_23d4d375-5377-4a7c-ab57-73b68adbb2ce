import 'package:flutter/foundation.dart';
import '../models/library_models.dart';
import '../services/fiqh_books_service.dart';
// import '../services/hadith_service.dart'; // تم إزالته لتجنب تضارب الأنواع
import '../services/reciter_service.dart';

/// مزود البيانات للمكتبة الدينية
class LibraryProvider extends ChangeNotifier {
  // الحالة العامة
  bool _isLoading = false;
  String? _error;

  // الكتب الفقهية
  List<Book> _fiqhBooks = [];
  List<Book> _tafsirBooks = [];
  Book? _currentBook;
  Chapter? _currentChapter;
  Section? _currentSection;

  // الأحاديث
  List<Hadith> _hadiths = [];
  List<Hadith> _favoriteHadiths = [];
  String _selectedHadithSource = 'bukhari';

  // القراء
  List<Reciter> _reciters = [];
  Reciter? _selectedReciter;

  // البحث
  List<SearchResult> _searchResults = [];
  String _lastSearchQuery = '';

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;

  List<Book> get fiqhBooks => _fiqhBooks;
  List<Book> get tafsirBooks => _tafsirBooks;
  Book? get currentBook => _currentBook;
  Chapter? get currentChapter => _currentChapter;
  Section? get currentSection => _currentSection;

  List<Hadith> get hadiths => _hadiths;
  List<Hadith> get favoriteHadiths => _favoriteHadiths;
  String get selectedHadithSource => _selectedHadithSource;

  List<Reciter> get reciters => _reciters;
  Reciter? get selectedReciter => _selectedReciter;

  List<SearchResult> get searchResults => _searchResults;
  String get lastSearchQuery => _lastSearchQuery;

  /// تهيئة المكتبة
  Future<void> initializeLibrary() async {
    try {
      _setLoading(true);
      _clearError();

      debugPrint('📚 تهيئة المكتبة الدينية...');

      // تحميل الكتب الفقهية
      await loadFiqhBooks();

      // تحميل كتب التفسير
      await loadTafsirBooks();

      // تحميل الأحاديث
      await loadHadiths();

      // تحميل القراء
      await loadReciters();

      debugPrint('✅ تم تهيئة المكتبة بنجاح');
    } catch (e) {
      _setError('فشل في تهيئة المكتبة: $e');
      debugPrint('❌ خطأ في تهيئة المكتبة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل الكتب الفقهية
  Future<void> loadFiqhBooks() async {
    try {
      debugPrint('📖 تحميل الكتب الفقهية...');
      _fiqhBooks = await FiqhBooksService.getAllFiqhBooks();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الكتب: $e');
      throw Exception('فشل في تحميل الكتب الفقهية');
    }
  }

  /// تحميل كتب التفسير
  Future<void> loadTafsirBooks() async {
    try {
      debugPrint('📖 تحميل كتب التفسير...');
      _tafsirBooks = await FiqhBooksService.getAllTafsirBooks();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل كتب التفسير: $e');
      throw Exception('فشل في تحميل كتب التفسير');
    }
  }

  /// تحميل الأحاديث
  Future<void> loadHadiths() async {
    try {
      debugPrint('📖 تحميل الأحاديث...');
      _hadiths = _getLocalHadiths(_selectedHadithSource);
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأحاديث: $e');
      throw Exception('فشل في تحميل الأحاديث');
    }
  }

  /// الحصول على الأحاديث المحلية
  List<Hadith> _getLocalHadiths(String source) {
    switch (source) {
      case 'bukhari':
        return [
          Hadith(
            id: 'bukhari_1',
            text: 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى',
            narrator: 'عمر بن الخطاب',
            source: 'صحيح البخاري',
            grade: 'صحيح',
            explanation:
                'هذا الحديث أصل عظيم من أصول الدين، يبين أن صحة العمل وفساده بحسب النية',
            tags: ['نية', 'أعمال', 'قصد'],
          ),
        ];
      case 'muslim':
        return [
          Hadith(
            id: 'muslim_1',
            text:
                'الإيمان بضع وسبعون شعبة، فأفضلها قول لا إله إلا الله، وأدناها إماطة الأذى عن الطريق',
            narrator: 'أبو هريرة',
            source: 'صحيح مسلم',
            grade: 'صحيح',
            explanation:
                'يبين هذا الحديث أن الإيمان له شعب كثيرة، وأن أعلاها التوحيد وأدناها إزالة الأذى',
            tags: ['إيمان', 'شعب', 'توحيد'],
          ),
        ];
      default:
        return [];
    }
  }

  /// تحميل القراء
  Future<void> loadReciters() async {
    try {
      debugPrint('📖 تحميل القراء...');
      _reciters = await ReciterService.getAllReciters();
      if (_reciters.isNotEmpty && _selectedReciter == null) {
        _selectedReciter = _reciters.first;
      }
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل القراء: $e');
      throw Exception('فشل في تحميل القراء');
    }
  }

  /// تحديد الكتاب الحالي
  Future<void> setCurrentBook(String bookId) async {
    try {
      _currentBook = await FiqhBooksService.getBookById(bookId);
      _currentChapter = null;
      _currentSection = null;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الكتاب: $e');
    }
  }

  /// تحديد الفصل الحالي
  void setCurrentChapter(Chapter chapter) {
    _currentChapter = chapter;
    _currentSection = null;
    notifyListeners();
  }

  /// تحديد القسم الحالي
  void setCurrentSection(Section section) {
    _currentSection = section;
    notifyListeners();
  }

  /// تحديد القارئ
  void setSelectedReciter(Reciter reciter) {
    final previousReciter = _selectedReciter;
    _selectedReciter = reciter;
    notifyListeners();

    debugPrint('🎵 تم تغيير القارئ إلى: ${reciter.nameArabic}');

    // إشعار بتغيير القارئ للمكونات الأخرى
    if (previousReciter != null && previousReciter.id != reciter.id) {
      debugPrint('🔄 القارئ السابق: ${previousReciter.nameArabic}');
      debugPrint('🔄 القارئ الجديد: ${reciter.nameArabic}');
    }
  }

  /// تحديد مصدر الأحاديث
  Future<void> setHadithSource(String source) async {
    if (_selectedHadithSource != source) {
      _selectedHadithSource = source;
      await loadHadiths();
    }
  }

  /// البحث في المكتبة
  Future<void> searchLibrary(String query) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      _lastSearchQuery = '';
      notifyListeners();
      return;
    }

    try {
      _setLoading(true);
      _lastSearchQuery = query;

      final results = <SearchResult>[];

      // البحث في الكتب الفقهية
      for (final book in _fiqhBooks) {
        for (final chapter in book.chapters) {
          for (final section in chapter.sections) {
            if (_containsQuery(section.title, query) ||
                _containsQuery(section.content, query)) {
              results.add(SearchResult(
                id: '${book.id}_${chapter.id}_${section.id}',
                title: section.title,
                content: _highlightQuery(section.content, query),
                type: 'فقه',
                bookId: book.id,
                chapterId: chapter.id,
                sectionId: section.id,
                relevanceScore: _calculateRelevance(section, query),
              ));
            }
          }
        }
      }

      // البحث في كتب التفسير
      for (final book in _tafsirBooks) {
        for (final chapter in book.chapters) {
          for (final section in chapter.sections) {
            if (_containsQuery(section.title, query) ||
                _containsQuery(section.content, query)) {
              results.add(SearchResult(
                id: '${book.id}_${chapter.id}_${section.id}',
                title: section.title,
                content: _highlightQuery(section.content, query),
                type: 'تفسير',
                bookId: book.id,
                chapterId: chapter.id,
                sectionId: section.id,
                relevanceScore: _calculateRelevance(section, query),
              ));
            }
          }
        }
      }

      // البحث في الأحاديث
      for (final hadith in _hadiths) {
        if (_containsQuery(hadith.text, query) ||
            _containsQuery(hadith.narrator, query)) {
          results.add(SearchResult(
            id: hadith.id,
            title: 'حديث شريف',
            content: _highlightQuery(hadith.text, query),
            type: 'حديث',
            bookId: hadith.source,
            relevanceScore: _calculateHadithRelevance(hadith, query),
          ));
        }
      }

      // ترتيب النتائج حسب الصلة
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

      _searchResults = results.take(50).toList();
      notifyListeners();
    } catch (e) {
      _setError('فشل في البحث: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من وجود النص في المحتوى
  bool _containsQuery(String content, String query) {
    return content.toLowerCase().contains(query.toLowerCase());
  }

  /// تمييز النص المطلوب في النتائج
  String _highlightQuery(String content, String query) {
    if (content.length > 200) {
      final index = content.toLowerCase().indexOf(query.toLowerCase());
      if (index != -1) {
        final start = (index - 50).clamp(0, content.length);
        final end = (index + query.length + 50).clamp(0, content.length);
        return '...${content.substring(start, end)}...';
      }
      return '${content.substring(0, 200)}...';
    }
    return content;
  }

  /// حساب درجة الصلة للأقسام
  double _calculateRelevance(Section section, String query) {
    double score = 0.0;
    final lowerQuery = query.toLowerCase();
    final lowerTitle = section.title.toLowerCase();
    final lowerContent = section.content.toLowerCase();

    if (lowerTitle.contains(lowerQuery)) score += 10.0;
    final matches = lowerQuery.allMatches(lowerContent).length;
    score += matches * 2.0;

    return score;
  }

  /// حساب درجة الصلة للأحاديث
  double _calculateHadithRelevance(Hadith hadith, String query) {
    double score = 0.0;
    final lowerQuery = query.toLowerCase();
    final lowerText = hadith.text.toLowerCase();
    final lowerNarrator = hadith.narrator.toLowerCase();

    if (lowerNarrator.contains(lowerQuery)) score += 5.0;
    final matches = lowerQuery.allMatches(lowerText).length;
    score += matches * 3.0;

    return score;
  }

  /// إضافة حديث للمفضلة
  void toggleHadithFavorite(Hadith hadith) {
    final index = _favoriteHadiths.indexWhere((h) => h.id == hadith.id);
    if (index != -1) {
      _favoriteHadiths.removeAt(index);
    } else {
      _favoriteHadiths.add(hadith.copyWith(isFavorite: true));
    }
    notifyListeners();
  }

  /// التحقق من كون الحديث مفضل
  bool isHadithFavorite(String hadithId) {
    return _favoriteHadiths.any((h) => h.id == hadithId);
  }

  /// مساعدات الحالة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// تنظيف البيانات
  void clearSearch() {
    _searchResults.clear();
    _lastSearchQuery = '';
    notifyListeners();
  }

  void clearCurrentSelection() {
    _currentBook = null;
    _currentChapter = null;
    _currentSection = null;
    notifyListeners();
  }
}
