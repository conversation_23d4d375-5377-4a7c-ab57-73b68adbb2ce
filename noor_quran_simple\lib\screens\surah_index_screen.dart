import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/quran_models.dart';
import '../providers/quran_provider.dart';
import 'surah_reading_screen.dart';

class SurahIndexScreen extends StatefulWidget {
  const SurahIndexScreen({super.key});

  @override
  State<SurahIndexScreen> createState() => _SurahIndexScreenState();
}

class _SurahIndexScreenState extends State<SurahIndexScreen> {
  String _searchQuery = '';
  bool _isGridView = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فهرس السور'),
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            tooltip: _isGridView ? 'عرض قائمة' : 'عرض شبكة',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildSurahStats(),
          Expanded(
            child: Consumer<QuranProvider>(
              builder: (context, quranProvider, child) {
                if (quranProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (quranProvider.surahs.isEmpty) {
                  return const Center(
                    child: Text('لا توجد سور متاحة'),
                  );
                }

                final filteredSurahs = _getFilteredSurahs(quranProvider.surahs);

                if (filteredSurahs.isEmpty) {
                  return const Center(
                    child: Text('لا توجد نتائج للبحث'),
                  );
                }

                return _isGridView
                    ? _buildGridView(filteredSurahs)
                    : _buildListView(filteredSurahs);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'البحث في السور...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildSurahStats() {
    return Consumer<QuranProvider>(
      builder: (context, quranProvider, child) {
        final totalSurahs = quranProvider.surahs.length;
        final meccanSurahs = quranProvider.surahs
            .where((s) => s.revelationType == 'Meccan')
            .length;
        final medinanSurahs = totalSurahs - meccanSurahs;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('المجموع', totalSurahs.toString(), Icons.book),
              _buildStatItem('مكية', meccanSurahs.toString(), Icons.location_on),
              _buildStatItem('مدنية', medinanSurahs.toString(), Icons.location_city),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppConstants.primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  List<Surah> _getFilteredSurahs(List<Surah> surahs) {
    if (_searchQuery.isEmpty) {
      return surahs;
    }

    return surahs.where((surah) {
      return surah.name.contains(_searchQuery) ||
          surah.englishName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          surah.number.toString().contains(_searchQuery);
    }).toList();
  }

  Widget _buildListView(List<Surah> surahs) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahListTile(surah);
      },
    );
  }

  Widget _buildGridView(List<Surah> surahs) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: AppConstants.paddingMedium,
        mainAxisSpacing: AppConstants.paddingMedium,
      ),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        return _buildSurahGridTile(surah);
      },
    );
  }

  Widget _buildSurahListTile(Surah surah) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              '${surah.number}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ),
        ),
        title: Text(
          surah.name,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              surah.englishName,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: surah.revelationType == 'Meccan'
                        ? AppConstants.accentColor.withValues(alpha: 0.2)
                        : AppConstants.secondaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: surah.revelationType == 'Meccan'
                          ? AppConstants.accentColor
                          : AppConstants.secondaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${surah.numberOfAyahs} آية',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _navigateToSurah(surah),
      ),
    );
  }

  Widget _buildSurahGridTile(Surah surah) {
    return Card(
      child: InkWell(
        onTap: () => _navigateToSurah(surah),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.fontSizeLarge,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                surah.name,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                '${surah.numberOfAyahs} آية',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: surah.revelationType == 'Meccan'
                      ? AppConstants.accentColor.withValues(alpha: 0.2)
                      : AppConstants.secondaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: surah.revelationType == 'Meccan'
                        ? AppConstants.accentColor
                        : AppConstants.secondaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToSurah(Surah surah) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SurahReadingScreen(
          surahNumber: surah.number,
          surahName: surah.name,
        ),
      ),
    );
  }
}
