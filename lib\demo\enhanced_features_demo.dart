import 'package:flutter/material.dart';
import '../screens/enhanced_dashboard_screen.dart';
import '../screens/achievements_screen.dart';
import '../screens/image_gallery_screen.dart';
import '../core/app_state_manager.dart';
import '../services/smart_notification_service.dart';
import '../services/achievement_service.dart';

/// تطبيق تجريبي لعرض جميع الميزات المطورة
class EnhancedFeaturesDemo extends StatelessWidget {
  const EnhancedFeaturesDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نور القرآن - المطور',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
      ),
      darkTheme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
      ),
      home: const FeaturesShowcaseScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// شاشة عرض الميزات
class FeaturesShowcaseScreen extends StatefulWidget {
  const FeaturesShowcaseScreen({super.key});

  @override
  State<FeaturesShowcaseScreen> createState() => _FeaturesShowcaseScreenState();
}

class _FeaturesShowcaseScreenState extends State<FeaturesShowcaseScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  Future<void> _initializeServices() async {
    await AppStateManager().initialize();
    await SmartNotificationService.initialize();
    await AchievementService.initialize();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildWelcomeCard(),
                    const SizedBox(height: 20),
                    _buildFeaturesGrid(),
                    const SizedBox(height: 20),
                    _buildStatisticsCard(),
                    const SizedBox(height: 20),
                    _buildQuickStartCard(),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.blue,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'نور القرآن المطور',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue,
                Colors.blueAccent,
                Colors.lightBlue,
              ],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 60,
                ),
                SizedBox(height: 8),
                Text(
                  'تجربة محسنة مع ميزات ذكية',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.green[50]!, Colors.green[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.celebration, color: Colors.green, size: 32),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'مرحباً بك في النسخة المطورة!',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        'اكتشف الميزات الجديدة والذكية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '✨ نظام إنجازات وتحديات\n'
              '🔔 إشعارات ذكية ومخصصة\n'
              '📊 تحليلات وإحصائيات متقدمة\n'
              '🖼️ معرض صور محسن\n'
              '🎯 لوحة تحكم تفاعلية',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesGrid() {
    final features = [
      {
        'title': 'لوحة التحكم المحسنة',
        'description': 'واجهة تفاعلية مع إحصائيات فورية',
        'icon': Icons.dashboard,
        'color': Colors.blue,
        'screen': const EnhancedDashboardScreen(),
      },
      {
        'title': 'نظام الإنجازات',
        'description': 'إنجازات وتحديات لتحفيز القراءة',
        'icon': Icons.emoji_events,
        'color': Colors.amber,
        'screen': const AchievementsScreen(),
      },
      {
        'title': 'معرض الصور المطور',
        'description': 'تصفح وإدارة الصور بطريقة ذكية',
        'icon': Icons.image,
        'color': Colors.purple,
        'screen': const ImageGalleryScreen(),
      },
      {
        'title': 'الإشعارات الذكية',
        'description': 'تذكيرات مخصصة حسب سلوكك',
        'icon': Icons.notifications_active,
        'color': Colors.orange,
        'action': () => _showNotificationDemo(),
      },
      {
        'title': 'التحليلات المتقدمة',
        'description': 'فهم عميق لعادات القراءة',
        'icon': Icons.analytics,
        'color': Colors.green,
        'action': () => _showAnalyticsDemo(),
      },
      {
        'title': 'إدارة الحالة الذكية',
        'description': 'تتبع شامل للتقدم والإحصائيات',
        'icon': Icons.settings_applications,
        'color': Colors.teal,
        'action': () => _showStateManagementDemo(),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الميزات الجديدة',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: features.length,
          itemBuilder: (context, index) {
            final feature = features[index];
            return _buildFeatureCard(feature);
          },
        ),
      ],
    );
  }

  Widget _buildFeatureCard(Map<String, dynamic> feature) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () {
          if (feature['screen'] != null) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => feature['screen']),
            );
          } else if (feature['action'] != null) {
            feature['action']();
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                (feature['color'] as Color).withValues(alpha: 0.1),
                (feature['color'] as Color).withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                feature['icon'],
                color: feature['color'],
                size: 32,
              ),
              const SizedBox(height: 12),
              Text(
                feature['title'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  feature['description'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: feature['color'],
                  size: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات التطوير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('15+', 'إنجاز جديد', Icons.emoji_events),
                _buildStatItem('5', 'خدمات ذكية', Icons.smart_toy),
                _buildStatItem('10+', 'ويدجت محسن', Icons.widgets),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQuickStartCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ابدأ الاستكشاف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'جرب الميزات الجديدة واكتشف كيف يمكن أن تحسن تجربتك مع القرآن الكريم',
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EnhancedDashboardScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.rocket_launch),
                label: const Text('ابدأ الآن'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationDemo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشعارات الذكية'),
        content: const Text(
          'نظام إشعارات متطور يتكيف مع عاداتك:\n\n'
          '• تذكيرات مخصصة\n'
          '• رسائل تحفيزية\n'
          '• تقارير دورية\n'
          '• احترام أوقات الصمت',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showAnalyticsDemo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحليلات المتقدمة'),
        content: const Text(
          'نظام تحليل شامل لفهم عاداتك:\n\n'
          '• تتبع جلسات القراءة\n'
          '• تحليل أنماط الاستخدام\n'
          '• إحصائيات مفصلة\n'
          '• تقارير تقدم شخصية',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showStateManagementDemo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة الحالة الذكية'),
        content: const Text(
          'نظام إدارة متطور للحالة والبيانات:\n\n'
          '• حفظ تلقائي للتقدم\n'
          '• مزامنة البيانات\n'
          '• إدارة التفضيلات\n'
          '• تحسين الأداء',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}

/// نقطة دخول التطبيق التجريبي
void main() {
  runApp(const EnhancedFeaturesDemo());
}
