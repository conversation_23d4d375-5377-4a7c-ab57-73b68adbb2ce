import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/quran_api_service.dart';
import '../services/offline_audio_service.dart';

class AudioProvider extends ChangeNotifier {
  AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _playbackSpeed = 1.0;
  String _selectedReciter = 'mishari_alafasy';
  bool _autoPlay = false;
  bool _repeatMode = false;
  int? _currentSurah;

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;
  Duration get duration => _duration;
  Duration get position => _position;
  double get playbackSpeed => _playbackSpeed;
  String get selectedReciter => _selectedReciter;
  bool get autoPlay => _autoPlay;
  bool get repeatMode => _repeatMode;

  AudioProvider() {
    _initializeAudioPlayer();
    _loadPreferences();
  }

  void _initializeAudioPlayer() {
    _audioPlayer.onDurationChanged.listen((duration) {
      _duration = duration;
      notifyListeners();
    });

    _audioPlayer.onPositionChanged.listen((position) {
      _position = position;
      notifyListeners();
    });

    _audioPlayer.onPlayerStateChanged.listen((state) {
      _isPlaying = state == PlayerState.playing;
      _isLoading = state == PlayerState.playing;
      notifyListeners();
    });
  }

  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedReciter =
          prefs.getString(AppConstants.keySelectedReciter) ?? 'mishari_alafasy';
      _autoPlay = prefs.getBool(AppConstants.keyAutoPlay) ?? false;
      _repeatMode = prefs.getBool(AppConstants.keyRepeatMode) ?? false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading audio preferences: $e');
    }
  }

  Future<void> playAudio(String url) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _audioPlayer.play(UrlSource(url));
    } catch (e) {
      debugPrint('Error playing audio: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> playSurah(int surahNumber) async {
    try {
      _isLoading = true;
      _currentSurah = surahNumber; // حفظ السورة الحالية
      notifyListeners();

      debugPrint(
        '🎵 بدء تشغيل السورة $surahNumber بصوت القارئ $_selectedReciter',
      );

      // جلب مسار الصوت (محلي أو عبر الإنترنت)
      final audioPath = await OfflineAudioService.getAudioPath(
        _selectedReciter,
        surahNumber,
      );

      debugPrint('🔗 مسار الصوت: $audioPath');

      // تشغيل الصوت
      if (audioPath.startsWith('http')) {
        debugPrint('🌐 تشغيل من الإنترنت: $audioPath');
        await _audioPlayer.play(UrlSource(audioPath));
      } else {
        debugPrint('📱 تشغيل من الملف المحلي: $audioPath');
        await _audioPlayer.play(DeviceFileSource(audioPath));
      }

      _isPlaying = true;
      debugPrint('✅ تم بدء التشغيل بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل السورة: $e');
      _isPlaying = false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> playAyah(int surahNumber, int ayahNumber) async {
    try {
      _isLoading = true;
      notifyListeners();

      // للآيات المنفردة، نستخدم API مؤقتاً
      final audioUrl = QuranApiService.getAyahAudioUrl(
        _selectedReciter,
        surahNumber,
        ayahNumber,
      );
      await _audioPlayer.play(UrlSource(audioUrl));
    } catch (e) {
      debugPrint('Error playing ayah: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> setReciter(String reciter) async {
    // حفظ حالة التشغيل الحالية
    final wasPlaying = _isPlaying;
    final currentSurah = _currentSurah;
    final currentPosition = _position;

    debugPrint('🔄 بدء تغيير القارئ من $_selectedReciter إلى $reciter');

    // إيقاف الصوت الحالي بشكل كامل
    if (_isPlaying) {
      debugPrint('⏹️ إيقاف الصوت الحالي...');
      await _audioPlayer.stop();
      await _audioPlayer.dispose();

      // إعادة تهيئة المشغل
      _audioPlayer = AudioPlayer();
      _initializeAudioPlayer();

      _isPlaying = false;
      _position = Duration.zero;
      _duration = Duration.zero;

      await Future.delayed(const Duration(milliseconds: 500));
    }

    // تغيير القارئ
    final oldReciter = _selectedReciter;
    _selectedReciter = reciter;

    debugPrint('✅ تم تغيير القارئ من $oldReciter إلى $reciter');

    // حفظ القارئ الجديد
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter);
      debugPrint('💾 تم حفظ القارئ الجديد: $reciter');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ القارئ: $e');
    }

    // إشعار المستمعين بالتغيير
    notifyListeners();

    // إذا كان الصوت يعمل، أعد تشغيله بالقارئ الجديد
    if (wasPlaying && currentSurah != null) {
      // انتظار للتأكد من تطبيق التغييرات
      await Future.delayed(const Duration(milliseconds: 800));

      debugPrint('🎵 إعادة تشغيل السورة $currentSurah بالقارئ الجديد');

      // تشغيل الصوت بالقارئ الجديد
      await playSurah(currentSurah);

      // محاولة العودة إلى نفس الموضع إذا أمكن
      if (currentPosition.inSeconds > 5) {
        await Future.delayed(const Duration(milliseconds: 1000));
        debugPrint('⏭️ العودة للموضع: ${currentPosition.inSeconds} ثانية');
        await seekTo(currentPosition);
      }
    }

    debugPrint('🎉 انتهى تغيير القارئ بنجاح');
  }

  Future<void> pauseAudio() async {
    await _audioPlayer.pause();
  }

  Future<void> stopAudio() async {
    await _audioPlayer.stop();
  }

  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed;
    await _audioPlayer.setPlaybackRate(speed);
    notifyListeners();
  }

  Future<void> setSelectedReciter(String reciter) async {
    _selectedReciter = reciter;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keySelectedReciter, reciter);
    } catch (e) {
      debugPrint('Error saving reciter: $e');
    }
  }

  void toggleAutoPlay() {
    _autoPlay = !_autoPlay;
    notifyListeners();
    _saveAutoPlay();
  }

  void toggleRepeatMode() {
    _repeatMode = !_repeatMode;
    notifyListeners();
    _saveRepeatMode();
  }

  Future<void> _saveAutoPlay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyAutoPlay, _autoPlay);
    } catch (e) {
      debugPrint('Error saving auto play: $e');
    }
  }

  Future<void> _saveRepeatMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyRepeatMode, _repeatMode);
    } catch (e) {
      debugPrint('Error saving repeat mode: $e');
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
