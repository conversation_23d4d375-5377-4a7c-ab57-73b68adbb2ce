import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/quran_provider.dart';
import '../providers/theme_provider.dart';
import '../models/surah.dart';
import 'quran_reader_screen.dart';

class BookmarksScreen extends StatelessWidget {
  const BookmarksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final quranProvider = context.watch<QuranProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: [
          if (quranProvider.hasBookmarks)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () => _showClearAllDialog(context, quranProvider),
              tooltip: 'مسح الكل',
            ),
        ],
      ),
      body: quranProvider.hasBookmarks
          ? ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: quranProvider.bookmarkedAyahs.length,
              itemBuilder: (context, index) {
                final ayah = quranProvider.bookmarkedAyahs[index];
                final surah = quranProvider.getSurahByNumber(ayah.surahNumber);
                return _buildBookmarkCard(
                  context,
                  ayah,
                  surah,
                  theme,
                  themeProvider,
                  quranProvider,
                );
              },
            )
          : _buildEmptyState(context, theme),
    );
  }

  Widget _buildBookmarkCard(
    BuildContext context,
    Ayah ayah,
    Surah? surah,
    ThemeData theme,
    ThemeProvider themeProvider,
    QuranProvider quranProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: InkWell(
        onTap: () {
          if (surah != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuranReaderScreen(surah: surah),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with surah info and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          surah?.name ?? 'سورة غير معروفة',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'الآية ${ayah.numberInSurah}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.share),
                        onPressed: () {
                          // TODO: Implement share ayah
                        },
                        tooltip: 'مشاركة',
                      ),
                      IconButton(
                        icon: const Icon(Icons.bookmark_remove),
                        onPressed: () => _showRemoveBookmarkDialog(
                          context,
                          ayah,
                          quranProvider,
                        ),
                        tooltip: 'إزالة من المفضلة',
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              // Ayah text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  border: Border.all(
                    color: theme.dividerColor.withOpacity(0.3),
                  ),
                ),
                child: SelectableText(
                  ayah.text,
                  style: themeProvider.getQuranTextStyle(context).copyWith(
                    fontSize: themeProvider.fontSize - 2,
                  ),
                  textAlign: TextAlign.justify,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              // Reference
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'سورة ${surah?.name ?? ''} - الآية ${ayah.numberInSurah}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
                    ),
                  ),
                  if (ayah.sajda)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'سجدة',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.3),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          Text(
            'لا توجد آيات مفضلة',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'ابدأ بإضافة آيات للمفضلة من خلال قراءة القرآن',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to surah list
            },
            icon: const Icon(Icons.menu_book),
            label: const Text('ابدأ القراءة'),
          ),
        ],
      ),
    );
  }

  void _showRemoveBookmarkDialog(
    BuildContext context,
    Ayah ayah,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة من المفضلة'),
        content: const Text('هل تريد إزالة هذه الآية من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              quranProvider.removeBookmark(ayah);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إزالة الآية من المفضلة'),
                ),
              );
            },
            child: const Text('إزالة'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(
    BuildContext context,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text('هل تريد مسح جميع الآيات المفضلة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              quranProvider.clearAllBookmarks();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المفضلة'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }
}
