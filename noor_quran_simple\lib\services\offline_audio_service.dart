import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'real_audio_service.dart';

class OfflineAudioService {
  static const String _audioFolderName = 'quran_audio';

  /// جلب مجلد الصوت المحلي
  static Future<Directory> _getAudioDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final audioDir = Directory('${appDir.path}/$_audioFolderName');

    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }

    return audioDir;
  }

  /// جلب مسار الملف الصوتي المحلي
  static Future<String> _getLocalAudioPath(
    String reciterIdentifier,
    int surahNumber,
  ) async {
    final audioDir = await _getAudioDirectory();
    final paddedSurahNumber = surahNumber.toString().padLeft(3, '0');
    return '${audioDir.path}/${reciterIdentifier}_$paddedSurahNumber.mp3';
  }

  /// التحقق من وجود الملف الصوتي محلياً
  static Future<bool> isAudioDownloaded(
    String reciterIdentifier,
    int surahNumber,
  ) async {
    final localPath = await _getLocalAudioPath(reciterIdentifier, surahNumber);
    return File(localPath).exists();
  }

  /// تحميل الملف الصوتي وحفظه محلياً
  static Future<String?> downloadAudio(
    String reciterIdentifier,
    int surahNumber, {
    Function(double)? onProgress,
  }) async {
    try {
      // جلب رابط الصوت من الخدمة الجديدة
      final audioUrl = RealAudioService.getSafeAudioUrl(
        reciterIdentifier,
        surahNumber,
      );
      final localPath = await _getLocalAudioPath(
        reciterIdentifier,
        surahNumber,
      );

      // التحقق من وجود الملف
      if (await File(localPath).exists()) {
        return localPath;
      }

      // تحميل الملف
      final response = await http.get(Uri.parse(audioUrl));

      if (response.statusCode == 200) {
        final file = File(localPath);
        await file.writeAsBytes(response.bodyBytes);
        return localPath;
      } else {
        throw Exception('فشل في تحميل الملف الصوتي');
      }
    } catch (e) {
      print('خطأ في تحميل الصوت: $e');
      return null;
    }
  }

  /// جلب مسار الملف الصوتي (محلي أو عبر الإنترنت)
  static Future<String> getAudioPath(
    String reciterIdentifier,
    int surahNumber,
  ) async {
    // التحقق من وجود الملف محلياً
    if (await isAudioDownloaded(reciterIdentifier, surahNumber)) {
      return await _getLocalAudioPath(reciterIdentifier, surahNumber);
    }

    // إرجاع الرابط عبر الإنترنت من الخدمة الجديدة
    return RealAudioService.getSafeAudioUrl(reciterIdentifier, surahNumber);
  }

  /// حذف الملفات الصوتية المحلية
  static Future<void> clearDownloadedAudio() async {
    try {
      final audioDir = await _getAudioDirectory();
      if (await audioDir.exists()) {
        await audioDir.delete(recursive: true);
      }
    } catch (e) {
      print('خطأ في حذف الملفات الصوتية: $e');
    }
  }

  /// جلب حجم الملفات الصوتية المحملة
  static Future<int> getDownloadedAudioSize() async {
    try {
      final audioDir = await _getAudioDirectory();
      if (!await audioDir.exists()) return 0;

      int totalSize = 0;
      await for (final entity in audioDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// جلب قائمة الملفات الصوتية المحملة
  static Future<List<Map<String, dynamic>>> getDownloadedAudioList() async {
    try {
      final audioDir = await _getAudioDirectory();
      if (!await audioDir.exists()) return [];

      final List<Map<String, dynamic>> downloadedFiles = [];

      await for (final entity in audioDir.list()) {
        if (entity is File && entity.path.endsWith('.mp3')) {
          final fileName = entity.path.split('/').last;
          final parts = fileName.replaceAll('.mp3', '').split('_');

          if (parts.length >= 2) {
            final reciterIdentifier = parts
                .sublist(0, parts.length - 1)
                .join('_');
            final surahNumber = int.tryParse(parts.last) ?? 0;

            // البحث عن اسم القارئ
            final reciterName = AppConstants.popularReciters.firstWhere(
              (reciter) => reciter['identifier'] == reciterIdentifier,
              orElse: () => {'name': reciterIdentifier},
            )['name'];

            final stat = await entity.stat();

            downloadedFiles.add({
              'reciterIdentifier': reciterIdentifier,
              'reciterName': reciterName,
              'surahNumber': surahNumber,
              'filePath': entity.path,
              'fileSize': stat.size,
              'downloadDate': stat.modified,
            });
          }
        }
      }

      // ترتيب حسب رقم السورة
      downloadedFiles.sort(
        (a, b) => a['surahNumber'].compareTo(b['surahNumber']),
      );

      return downloadedFiles;
    } catch (e) {
      return [];
    }
  }

  /// حذف ملف صوتي محدد
  static Future<bool> deleteAudioFile(
    String reciterIdentifier,
    int surahNumber,
  ) async {
    try {
      final localPath = await _getLocalAudioPath(
        reciterIdentifier,
        surahNumber,
      );
      final file = File(localPath);

      if (await file.exists()) {
        await file.delete();
        return true;
      }

      return false;
    } catch (e) {
      print('خطأ في حذف الملف الصوتي: $e');
      return false;
    }
  }

  /// تحميل سورة كاملة لقارئ معين
  static Future<void> downloadCompleteSurah(
    String reciterIdentifier,
    int surahNumber, {
    Function(double)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      await downloadAudio(
        reciterIdentifier,
        surahNumber,
        onProgress: onProgress,
      );
    } catch (e) {
      if (onError != null) {
        onError('فشل في تحميل السورة: $e');
      }
    }
  }

  /// تحميل عدة سور لقارئ معين
  static Future<void> downloadMultipleSurahs(
    String reciterIdentifier,
    List<int> surahNumbers, {
    Function(int, int)? onProgress, // (current, total)
    Function(String)? onError,
  }) async {
    for (int i = 0; i < surahNumbers.length; i++) {
      try {
        await downloadAudio(reciterIdentifier, surahNumbers[i]);
        if (onProgress != null) {
          onProgress(i + 1, surahNumbers.length);
        }
      } catch (e) {
        if (onError != null) {
          onError('فشل في تحميل السورة ${surahNumbers[i]}: $e');
        }
      }
    }
  }

  /// التحقق من مساحة التخزين المتاحة
  static Future<bool> hasEnoughStorage(int requiredBytes) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final stat = await dir.stat();
      // تقدير تقريبي - في الواقع نحتاج لطريقة أفضل للتحقق من المساحة المتاحة
      return true; // مؤقتاً
    } catch (e) {
      return false;
    }
  }
}
