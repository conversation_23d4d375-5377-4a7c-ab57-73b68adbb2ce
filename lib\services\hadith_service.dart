import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/religious_book_models.dart';

/// خدمة الأحاديث النبوية
class HadithService {
  static const String _baseUrl = 'https://hadithapi.com/api';
  static const String _apiKey = 'YOUR_API_KEY'; // يحتاج تسجيل في الموقع
  
  // URLs بديلة مجانية
  static const String _alternativeBaseUrl = 'https://api.sunnah.com/v1';
  static const String _hadithDbUrl = 'https://hadithdb.com/api';

  /// الحصول على جميع كتب الحديث
  static Future<List<HadithBook>> getAllHadithBooks() async {
    try {
      // محاولة استخدام API الرئيسي
      final response = await http.get(
        Uri.parse('$_baseUrl/books'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['books'] as List)
            .map((book) => HadithBook.fromJson(book))
            .toList();
      } else {
        // استخدام البيانات المحلية كبديل
        return _getLocalHadithBooks();
      }
    } catch (e) {
      debugPrint('خطأ في جلب كتب الحديث: $e');
      return _getLocalHadithBooks();
    }
  }

  /// البحث في الأحاديث
  static Future<List<Hadith>> searchHadiths(String query) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search?q=${Uri.encodeComponent(query)}'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['hadiths'] as List)
            .map((hadith) => Hadith.fromJson(hadith))
            .toList();
      } else {
        return _searchLocalHadiths(query);
      }
    } catch (e) {
      debugPrint('خطأ في البحث في الأحاديث: $e');
      return _searchLocalHadiths(query);
    }
  }

  /// الحصول على أحاديث كتاب معين
  static Future<List<Hadith>> getHadithsByBook(String bookId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/books/$bookId/hadiths'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['hadiths'] as List)
            .map((hadith) => Hadith.fromJson(hadith))
            .toList();
      } else {
        return _getLocalHadithsByBook(bookId);
      }
    } catch (e) {
      debugPrint('خطأ في جلب أحاديث الكتاب: $e');
      return _getLocalHadithsByBook(bookId);
    }
  }

  /// الحصول على حديث بالرقم
  static Future<Hadith?> getHadithByNumber(String bookId, int hadithNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/books/$bookId/hadiths/$hadithNumber'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Hadith.fromJson(data['hadith']);
      } else {
        return _getLocalHadithByNumber(bookId, hadithNumber);
      }
    } catch (e) {
      debugPrint('خطأ في جلب الحديث: $e');
      return _getLocalHadithByNumber(bookId, hadithNumber);
    }
  }

  /// الحصول على الأحاديث العشوائية
  static Future<List<Hadith>> getRandomHadiths({int count = 5}) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/random?count=$count'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['hadiths'] as List)
            .map((hadith) => Hadith.fromJson(hadith))
            .toList();
      } else {
        return _getLocalRandomHadiths(count);
      }
    } catch (e) {
      debugPrint('خطأ في جلب الأحاديث العشوائية: $e');
      return _getLocalRandomHadiths(count);
    }
  }

  /// دالة للتوافق مع الكود القديم
  static Future<List<Hadith>> getHadiths(String source) async {
    return await getHadithsByBook(source);
  }

  /// جلب جميع مصادر الأحاديث المتاحة
  static List<String> getAvailableSources() {
    return [
      'bukhari',
      'muslim',
      'nawawi40',
      'abu_dawud',
      'tirmidhi',
      'nasai',
      'ibn_majah',
    ];
  }

  /// البيانات المحلية كبديل
  static List<HadithBook> _getLocalHadithBooks() {
    return [
      HadithBook(
        id: 'bukhari',
        name: 'Sahih al-Bukhari',
        nameArabic: 'صحيح البخاري',
        author: 'Imam al-Bukhari',
        authorArabic: 'الإمام البخاري',
        description: 'أصح كتاب بعد كتاب الله',
        totalHadiths: 7563,
        chapters: _getBukhariChapters(),
      ),
      HadithBook(
        id: 'muslim',
        name: 'Sahih Muslim',
        nameArabic: 'صحيح مسلم',
        author: 'Imam Muslim',
        authorArabic: 'الإمام مسلم',
        description: 'ثاني أصح كتب الحديث',
        totalHadiths: 5362,
        chapters: _getMuslimChapters(),
      ),
      HadithBook(
        id: 'nawawi40',
        name: 'An-Nawawi\'s Forty Hadiths',
        nameArabic: 'الأربعون النووية',
        author: 'Imam an-Nawawi',
        authorArabic: 'الإمام النووي',
        description: 'أربعون حديثاً جامعة لأصول الدين',
        totalHadiths: 42,
        chapters: _getNawawiChapters(),
      ),
    ];
  }

  static List<HadithChapter> _getBukhariChapters() {
    return [
      HadithChapter(
        id: 'bukhari_ch1',
        title: 'Revelation',
        titleArabic: 'كتاب بدء الوحي',
        chapterNumber: 1,
        hadiths: [
          Hadith(
            id: 'bukhari_1',
            arabicText: 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى',
            englishText: 'Actions are but by intention and every man shall have but that which he intended.',
            narrator: 'Umar ibn al-Khattab',
            narratorArabic: 'عمر بن الخطاب',
            bookName: 'Sahih al-Bukhari',
            bookNameArabic: 'صحيح البخاري',
            hadithNumber: 1,
            grade: HadithGrade.sahih,
            explanationArabic: 'هذا الحديث أصل عظيم من أصول الدين، يبين أن صحة العمل وفساده بحسب النية',
            keywords: ['نية', 'أعمال', 'قصد'],
          ),
        ],
      ),
    ];
  }

  static List<HadithChapter> _getMuslimChapters() {
    return [
      HadithChapter(
        id: 'muslim_ch1',
        title: 'Faith',
        titleArabic: 'كتاب الإيمان',
        chapterNumber: 1,
        hadiths: [
          Hadith(
            id: 'muslim_1',
            arabicText: 'الإيمان بضع وسبعون شعبة، فأفضلها قول لا إله إلا الله، وأدناها إماطة الأذى عن الطريق',
            englishText: 'Faith has over seventy branches, the highest of which is saying "There is no god but Allah"',
            narrator: 'Abu Hurairah',
            narratorArabic: 'أبو هريرة',
            bookName: 'Sahih Muslim',
            bookNameArabic: 'صحيح مسلم',
            hadithNumber: 35,
            grade: HadithGrade.sahih,
            explanationArabic: 'يبين هذا الحديث أن الإيمان له شعب كثيرة، وأن أعلاها التوحيد وأدناها إزالة الأذى',
            keywords: ['إيمان', 'شعب', 'توحيد'],
          ),
        ],
      ),
    ];
  }

  static List<HadithChapter> _getNawawiChapters() {
    return [
      HadithChapter(
        id: 'nawawi_ch1',
        title: 'The Forty Hadiths',
        titleArabic: 'الأحاديث الأربعون',
        chapterNumber: 1,
        hadiths: [
          Hadith(
            id: 'nawawi_1',
            arabicText: 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى',
            englishText: 'Actions are but by intention and every man shall have but that which he intended.',
            narrator: 'Umar ibn al-Khattab',
            narratorArabic: 'عمر بن الخطاب',
            bookName: 'An-Nawawi\'s Forty Hadiths',
            bookNameArabic: 'الأربعون النووية',
            hadithNumber: 1,
            grade: HadithGrade.sahih,
            explanationArabic: 'الحديث الأول من الأربعين النووية، وهو أصل في باب النيات',
            keywords: ['نية', 'أعمال', 'نووية'],
          ),
        ],
      ),
    ];
  }

  static List<Hadith> _searchLocalHadiths(String query) {
    final allBooks = _getLocalHadithBooks();
    final results = <Hadith>[];
    
    for (final book in allBooks) {
      for (final chapter in book.chapters) {
        for (final hadith in chapter.hadiths) {
          if (hadith.arabicText.contains(query) ||
              hadith.englishText.toLowerCase().contains(query.toLowerCase()) ||
              hadith.keywords.any((keyword) => keyword.contains(query))) {
            results.add(hadith);
          }
        }
      }
    }
    
    return results;
  }

  static List<Hadith> _getLocalHadithsByBook(String bookId) {
    final books = _getLocalHadithBooks();
    final book = books.where((b) => b.id == bookId).firstOrNull;
    if (book == null) return [];
    
    final hadiths = <Hadith>[];
    for (final chapter in book.chapters) {
      hadiths.addAll(chapter.hadiths);
    }
    return hadiths;
  }

  static Hadith? _getLocalHadithByNumber(String bookId, int hadithNumber) {
    final hadiths = _getLocalHadithsByBook(bookId);
    return hadiths.where((h) => h.hadithNumber == hadithNumber).firstOrNull;
  }

  static List<Hadith> _getLocalRandomHadiths(int count) {
    final allBooks = _getLocalHadithBooks();
    final allHadiths = <Hadith>[];
    
    for (final book in allBooks) {
      for (final chapter in book.chapters) {
        allHadiths.addAll(chapter.hadiths);
      }
    }
    
    allHadiths.shuffle();
    return allHadiths.take(count).toList();
  }
}
