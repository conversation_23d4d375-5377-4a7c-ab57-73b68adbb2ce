import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../constants/app_constants.dart';

class SearchHistoryScreen extends StatefulWidget {
  const SearchHistoryScreen({super.key});

  @override
  State<SearchHistoryScreen> createState() => _SearchHistoryScreenState();
}

class _SearchHistoryScreenState extends State<SearchHistoryScreen> {
  List<SearchHistoryItem> _searchHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
  }

  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('search_history') ?? [];
      
      setState(() {
        _searchHistory = historyJson
            .map((json) => SearchHistoryItem.fromJson(jsonDecode(json)))
            .toList();
        _searchHistory.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = _searchHistory
          .map((item) => jsonEncode(item.toJson()))
          .toList();
      await prefs.setStringList('search_history', historyJson);
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _addSearchItem(String query, String category, String result) async {
    final newItem = SearchHistoryItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      query: query,
      category: category,
      result: result,
      timestamp: DateTime.now(),
    );

    setState(() {
      _searchHistory.removeWhere((item) => item.query == query && item.category == category);
      _searchHistory.insert(0, newItem);
      
      // الاحتفاظ بآخر 50 عملية بحث فقط
      if (_searchHistory.length > 50) {
        _searchHistory = _searchHistory.take(50).toList();
      }
    });

    await _saveSearchHistory();
  }

  Future<void> _removeSearchItem(String id) async {
    setState(() {
      _searchHistory.removeWhere((item) => item.id == id);
    });
    await _saveSearchHistory();
  }

  Future<void> _clearAllHistory() async {
    setState(() {
      _searchHistory.clear();
    });
    await _saveSearchHistory();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل البحث'),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_searchHistory.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () => _showClearAllDialog(),
              tooltip: 'مسح الكل',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _searchHistory.isEmpty
              ? _buildEmptyState()
              : _buildHistoryList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddSearchDialog(),
        backgroundColor: theme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
        tooltip: 'إضافة بحث جديد',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد سجل بحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بالبحث لرؤية السجل هنا',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    final groupedHistory = _groupHistoryByDate();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedHistory.length,
      itemBuilder: (context, index) {
        final group = groupedHistory[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                group.date,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
            ...group.items.map((item) => _buildHistoryItem(item)),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildHistoryItem(SearchHistoryItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCategoryColor(item.category),
          child: Icon(
            _getCategoryIcon(item.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          item.query,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الفئة: ${item.category}'),
            if (item.result.isNotEmpty)
              Text(
                'النتيجة: ${item.result}',
                style: TextStyle(color: Colors.grey[600]),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            Text(
              _formatTime(item.timestamp),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'search_again':
                _searchAgain(item);
                break;
              case 'delete':
                _removeSearchItem(item.id);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'search_again',
              child: Row(
                children: [
                  Icon(Icons.search),
                  SizedBox(width: 8),
                  Text('البحث مرة أخرى'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _searchAgain(item),
      ),
    );
  }

  List<HistoryGroup> _groupHistoryByDate() {
    final groups = <String, List<SearchHistoryItem>>{};
    
    for (final item in _searchHistory) {
      final dateKey = _formatDate(item.timestamp);
      groups[dateKey] ??= [];
      groups[dateKey]!.add(item);
    }
    
    return groups.entries
        .map((entry) => HistoryGroup(date: entry.key, items: entry.value))
        .toList();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final itemDate = DateTime(date.year, date.month, date.day);
    
    if (itemDate == today) {
      return 'اليوم';
    } else if (itemDate == yesterday) {
      return 'أمس';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'قرآن':
        return Colors.green;
      case 'حديث':
        return Colors.blue;
      case 'تفسير':
        return Colors.purple;
      case 'فقه':
        return Colors.orange;
      case 'صور':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'قرآن':
        return Icons.menu_book;
      case 'حديث':
        return Icons.format_quote;
      case 'تفسير':
        return Icons.library_books;
      case 'فقه':
        return Icons.gavel;
      case 'صور':
        return Icons.image;
      default:
        return Icons.search;
    }
  }

  void _searchAgain(SearchHistoryItem item) {
    // TODO: تنفيذ البحث مرة أخرى
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('البحث عن: ${item.query} في ${item.category}'),
        action: SnackBarAction(
          label: 'موافق',
          onPressed: () {},
        ),
      ),
    );
  }

  void _showAddSearchDialog() {
    String query = '';
    String category = 'قرآن';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة بحث جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'كلمة البحث',
                hintText: 'أدخل كلمة البحث...',
              ),
              onChanged: (value) => query = value,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: category,
              decoration: const InputDecoration(labelText: 'الفئة'),
              items: ['قرآن', 'حديث', 'تفسير', 'فقه', 'صور']
                  .map((cat) => DropdownMenuItem(value: cat, child: Text(cat)))
                  .toList(),
              onChanged: (value) => category = value ?? 'قرآن',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (query.isNotEmpty) {
                _addSearchItem(query, category, 'تم البحث يدوياً');
                Navigator.pop(context);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح السجل'),
        content: const Text('هل تريد مسح جميع عمليات البحث؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _clearAllHistory();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

// نماذج البيانات
class SearchHistoryItem {
  final String id;
  final String query;
  final String category;
  final String result;
  final DateTime timestamp;

  SearchHistoryItem({
    required this.id,
    required this.query,
    required this.category,
    required this.result,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'query': query,
      'category': category,
      'result': result,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory SearchHistoryItem.fromJson(Map<String, dynamic> json) {
    return SearchHistoryItem(
      id: json['id'],
      query: json['query'],
      category: json['category'],
      result: json['result'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class HistoryGroup {
  final String date;
  final List<SearchHistoryItem> items;

  HistoryGroup({required this.date, required this.items});
}
