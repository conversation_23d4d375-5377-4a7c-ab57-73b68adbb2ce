# نور القرآن - <PERSON>or Quran

تطبيق شامل لقراءة القرآن الكريم مع التلاوات الصوتية والميزات الذكية

## 📱 المميزات

### 🕌 القراءة والتلاوة
- **قراءة القرآن الكريم** مع نص واضح وجميل
- **التلاوة الصوتية** مع مجموعة من أشهر القراء
- **البحث المتقدم** في آيات القرآن الكريم
- **العلامات المرجعية** لحفظ الآيات المفضلة
- **تتبع التقدم** في القراءة

### 🎨 التصميم والواجهة
- **الوضع الليلي والنهاري** للراحة البصرية
- **تخصيص حجم الخط** حسب الحاجة
- **واجهة عربية** بالكامل مع دعم RTL
- **تصميم عصري** باستخدام Material Design 3

### 🔊 المشغل الصوتي
- **تشغيل السور** مع أشهر القراء
- **التحكم في السرعة** من 0.5x إلى 2x
- **أوضاع التكرار** (بدون تكرار، تكرار السورة، تكرار الكل)
- **التشغيل التلقائي** للسورة التالية

### 📊 الإحصائيات والتقدم
- **تتبع السور المقروءة** والآيات
- **سلسلة القراءة اليومية** لتحفيز الاستمرار
- **الأهداف اليومية والأسبوعية** القابلة للتخصيص
- **إحصائيات مفصلة** عن وقت القراءة والتقدم

## 🛠️ التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Provider** - إدارة الحالة
- **SQLite** - قاعدة البيانات المحلية
- **SharedPreferences** - حفظ الإعدادات
- **Just Audio** - تشغيل الملفات الصوتية
- **HTTP** - الاتصال بـ APIs

## 🚀 التشغيل والتطوير

### المتطلبات
- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK (2.17.0 أو أحدث)
- Android Studio أو VS Code

### خطوات التشغيل

1. **تثبيت المكتبات**
```bash
flutter pub get
```

2. **تشغيل التطبيق**
```bash
flutter run
```

3. **تشغيل الاختبارات**
```bash
flutter test
```

---

**"وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"**

*الإسراء: 82*
