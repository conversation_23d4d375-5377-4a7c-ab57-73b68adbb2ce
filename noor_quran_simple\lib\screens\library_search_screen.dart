import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/library_provider.dart';
import '../models/library_models.dart';

/// شاشة البحث في المكتبة
class LibrarySearchScreen extends StatefulWidget {
  const LibrarySearchScreen({super.key});

  @override
  State<LibrarySearchScreen> createState() => _LibrarySearchScreenState();
}

class _LibrarySearchScreenState extends State<LibrarySearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث في المكتبة'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: Consumer<LibraryProvider>(
        builder: (context, libraryProvider, child) {
          return Column(
            children: [
              _buildSearchBar(libraryProvider),
              Expanded(
                child: _buildSearchResults(libraryProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(LibraryProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            focusNode: _searchFocusNode,
            decoration: InputDecoration(
              hintText: 'ابحث في الكتب والأحاديث والتفاسير...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_searchController.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        provider.clearSearchResults();
                      },
                    ),
                  IconButton(
                    icon: const Icon(Icons.tune),
                    onPressed: () => _showFilterDialog(),
                  ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              setState(() {});
            },
            onSubmitted: (query) {
              if (query.trim().isNotEmpty) {
                provider.searchAll(query.trim());
              }
            },
          ),
          const SizedBox(height: 12),
          if (provider.lastSearchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'نتائج البحث عن: "${provider.lastSearchQuery}"',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(LibraryProvider provider) {
    if (provider.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري البحث...'),
          ],
        ),
      );
    }

    if (provider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              provider.error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                if (provider.lastSearchQuery.isNotEmpty) {
                  provider.searchAll(provider.lastSearchQuery);
                }
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (provider.searchResults.isEmpty) {
      if (provider.lastSearchQuery.isEmpty) {
        return _buildSearchSuggestions();
      } else {
        return _buildNoResults();
      }
    }

    return _buildResultsList(provider);
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اقتراحات البحث',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSuggestionCard(
            'الوضوء',
            'ابحث عن أحكام الوضوء في الفقه',
            Icons.water_drop,
          ),
          _buildSuggestionCard(
            'الصلاة',
            'أحكام وآداب الصلاة',
            Icons.mosque,
          ),
          _buildSuggestionCard(
            'النية',
            'أحاديث وأحكام النية',
            Icons.favorite,
          ),
          _buildSuggestionCard(
            'التوحيد',
            'مسائل العقيدة والتوحيد',
            Icons.star,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionCard(String title, String description, IconData icon) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(icon, color: AppConstants.primaryColor),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.search),
        onTap: () {
          _searchController.text = title;
          context.read<LibraryProvider>().searchAll(title);
        },
      ),
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'لم يتم العثور على نتائج',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'جرب كلمات مفتاحية أخرى أو تأكد من الإملاء',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              _searchController.clear();
              context.read<LibraryProvider>().clearSearchResults();
            },
            child: const Text('مسح البحث'),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList(LibraryProvider provider) {
    final results = provider.searchResults;
    
    // تجميع النتائج حسب النوع
    final groupedResults = <SearchResultType, List<SearchResult>>{};
    for (final result in results) {
      groupedResults.putIfAbsent(result.type, () => []).add(result);
    }

    return ListView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      children: [
        Text(
          'النتائج (${results.length})',
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...groupedResults.entries.map((entry) {
          return _buildResultsSection(entry.key, entry.value);
        }),
      ],
    );
  }

  Widget _buildResultsSection(SearchResultType type, List<SearchResult> results) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getTypeColor(type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            '${type.displayName} (${results.length})',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: _getTypeColor(type),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 12),
        ...results.map((result) => _buildResultCard(result)),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildResultCard(SearchResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(
          _getTypeIcon(result.type),
          color: _getTypeColor(result.type),
        ),
        title: Text(
          result.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              result.content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              result.source,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.primaryColor,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _openResult(result),
      ),
    );
  }

  Color _getTypeColor(SearchResultType type) {
    switch (type) {
      case SearchResultType.fiqh:
        return Colors.blue;
      case SearchResultType.hadith:
        return Colors.green;
      case SearchResultType.tafsir:
        return Colors.orange;
      case SearchResultType.verse:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(SearchResultType type) {
    switch (type) {
      case SearchResultType.fiqh:
        return Icons.gavel;
      case SearchResultType.hadith:
        return Icons.format_quote;
      case SearchResultType.tafsir:
        return Icons.description;
      case SearchResultType.verse:
        return Icons.menu_book;
      default:
        return Icons.article;
    }
  }

  void _openResult(SearchResult result) {
    // هنا يمكن إضافة منطق فتح النتيجة حسب نوعها
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح: ${result.title}'),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('خيارات البحث'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('ستتم إضافة خيارات البحث المتقدم قريباً'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
}
