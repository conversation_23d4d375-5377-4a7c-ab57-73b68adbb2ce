import 'dart:html' as html;
import 'package:flutter/foundation.dart';

class HtmlAudioService {
  static final HtmlAudioService _instance = HtmlAudioService._internal();
  factory HtmlAudioService() => _instance;
  HtmlAudioService._internal();

  html.AudioElement? _audioElement;
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;
  double _playbackRate = 1.0;

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;
  Duration get duration => _duration;
  Duration get position => _position;
  double get volume => _volume;
  double get playbackRate => _playbackRate;

  // Stream controllers for state changes
  final List<Function(bool)> _playingListeners = [];
  final List<Function(Duration)> _positionListeners = [];
  final List<Function(Duration)> _durationListeners = [];

  void addPlayingListener(Function(bool) listener) {
    _playingListeners.add(listener);
  }

  void addPositionListener(Function(Duration) listener) {
    _positionListeners.add(listener);
  }

  void addDurationListener(Function(Duration) listener) {
    _durationListeners.add(listener);
  }

  void removePlayingListener(Function(bool) listener) {
    _playingListeners.remove(listener);
  }

  void removePositionListener(Function(Duration) listener) {
    _positionListeners.remove(listener);
  }

  void removeDurationListener(Function(Duration) listener) {
    _durationListeners.remove(listener);
  }

  Future<void> setUrl(String url) async {
    try {
      _isLoading = true;
      _notifyPlayingListeners();

      // Dispose previous audio element
      _audioElement?.pause();
      _audioElement?.remove();
      _audioElement = null;

      // Create new audio element
      _audioElement = html.AudioElement();
      _audioElement!.src = url;
      _audioElement!.preload = 'auto';
      _audioElement!.crossOrigin = 'anonymous';

      // Set up event listeners
      _setupEventListeners();

      // Load the audio
      _audioElement!.load();

      debugPrint('Audio URL set: $url');
    } catch (e) {
      _isLoading = false;
      debugPrint('Error setting audio URL: $e');
      _notifyPlayingListeners();
      rethrow;
    }
  }

  void _setupEventListeners() {
    if (_audioElement == null) return;

    // Loading events
    _audioElement!.onLoadedData.listen((_) {
      _isLoading = true;
      _notifyPlayingListeners();
    });

    _audioElement!.onCanPlay.listen((_) {
      _isLoading = false;
      _notifyPlayingListeners();
    });

    _audioElement!.onLoadedMetadata.listen((_) {
      final duration = _audioElement!.duration;
      if (!duration.isNaN && duration.isFinite) {
        _duration = Duration(seconds: duration.toInt());
        _notifyDurationListeners();
      }
    });

    // Playback events
    _audioElement!.onPlay.listen((_) {
      _isPlaying = true;
      _isLoading = false;
      _notifyPlayingListeners();
    });

    _audioElement!.onPause.listen((_) {
      _isPlaying = false;
      _notifyPlayingListeners();
    });

    _audioElement!.onEnded.listen((_) {
      _isPlaying = false;
      _position = Duration.zero;
      _notifyPlayingListeners();
      _notifyPositionListeners();
    });

    // Time update
    _audioElement!.onTimeUpdate.listen((_) {
      final currentTime = _audioElement!.currentTime;
      if (!currentTime.isNaN && currentTime.isFinite) {
        _position = Duration(seconds: currentTime.toInt());
        _notifyPositionListeners();
      }
    });

    // Error handling
    _audioElement!.onError.listen((event) {
      _isLoading = false;
      _isPlaying = false;
      debugPrint('Audio error: $event');
      _notifyPlayingListeners();
    });
  }

  Future<void> play() async {
    if (_audioElement == null) return;

    try {
      await _audioElement!.play();
    } catch (e) {
      debugPrint('Error playing audio: $e');
      // Try to handle autoplay restrictions
      if (e.toString().contains('user activation')) {
        debugPrint('Autoplay blocked - user interaction required');
      }
      rethrow;
    }
  }

  Future<void> pause() async {
    if (_audioElement == null) return;

    try {
      _audioElement!.pause();
    } catch (e) {
      debugPrint('Error pausing audio: $e');
    }
  }

  Future<void> stop() async {
    if (_audioElement == null) return;

    try {
      _audioElement!.pause();
      _audioElement!.currentTime = 0;
      _position = Duration.zero;
      _notifyPositionListeners();
    } catch (e) {
      debugPrint('Error stopping audio: $e');
    }
  }

  Future<void> seek(Duration position) async {
    if (_audioElement == null) return;

    try {
      _audioElement!.currentTime = position.inSeconds.toDouble();
      _position = position;
      _notifyPositionListeners();
    } catch (e) {
      debugPrint('Error seeking audio: $e');
    }
  }

  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    if (_audioElement != null) {
      _audioElement!.volume = _volume;
    }
  }

  Future<void> setPlaybackRate(double rate) async {
    _playbackRate = rate.clamp(0.25, 4.0);
    if (_audioElement != null) {
      _audioElement!.playbackRate = _playbackRate;
    }
  }

  void _notifyPlayingListeners() {
    for (final listener in _playingListeners) {
      try {
        listener(_isPlaying);
      } catch (e) {
        debugPrint('Error in playing listener: $e');
      }
    }
  }

  void _notifyPositionListeners() {
    for (final listener in _positionListeners) {
      try {
        listener(_position);
      } catch (e) {
        debugPrint('Error in position listener: $e');
      }
    }
  }

  void _notifyDurationListeners() {
    for (final listener in _durationListeners) {
      try {
        listener(_duration);
      } catch (e) {
        debugPrint('Error in duration listener: $e');
      }
    }
  }

  void dispose() {
    _audioElement?.pause();
    _audioElement?.remove();
    _audioElement = null;
    _playingListeners.clear();
    _positionListeners.clear();
    _durationListeners.clear();
  }

  // Test method to check if audio URLs are working
  Future<bool> testAudioUrl(String url) async {
    try {
      final testElement = html.AudioElement();
      testElement.src = url;
      testElement.preload = 'metadata';
      testElement.crossOrigin = 'anonymous';

      bool loaded = false;
      bool error = false;

      testElement.onCanPlay.listen((_) {
        loaded = true;
      });

      testElement.onError.listen((_) {
        error = true;
      });

      testElement.load();

      // Wait for up to 5 seconds
      for (int i = 0; i < 50; i++) {
        if (loaded || error) break;
        await Future.delayed(const Duration(milliseconds: 100));
      }

      testElement.remove();
      return loaded && !error;
    } catch (e) {
      debugPrint('Error testing audio URL: $e');
      return false;
    }
  }
}
