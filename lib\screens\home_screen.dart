import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/theme_provider.dart';
import '../providers/quran_provider.dart';
import '../providers/audio_provider.dart';
import 'surah_list_screen.dart';
import 'quran_reader_screen.dart';
import 'bookmarks_screen.dart';
import 'settings_screen.dart';
import 'search_screen.dart';

import 'library_main_screen.dart';
import 'reciter_selection_screen.dart';
import 'religious_library_screen.dart';
import 'image_gallery_screen.dart';
import 'search_history_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const HomeTabScreen(),
    const SurahListScreen(),
    const ReligiousLibraryScreen(),
    const BookmarksScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: AppConstants.animationDuration,
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_rounded),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book_rounded),
            label: 'السور',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.library_books_rounded),
            label: 'المكتبة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bookmark_rounded),
            label: 'المفضلة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings_rounded),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }
}

class HomeTabScreen extends StatelessWidget {
  const HomeTabScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final quranProvider = context.watch<QuranProvider>();
    final audioProvider = context.watch<AudioProvider>();

    return Scaffold(
      appBar: AppBar(
        title: Text(AppConstants.appName),
        actions: [
          IconButton(
            icon: Icon(themeProvider.themeModeIcon),
            onPressed: () => themeProvider.toggleTheme(),
            tooltip: 'تغيير المظهر',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(context, theme, themeProvider),
            const SizedBox(height: AppConstants.paddingLarge),

            // Continue Reading Section
            if (quranProvider.lastReadSurahNumber > 0)
              _buildContinueReadingSection(context, theme, quranProvider),

            const SizedBox(height: AppConstants.paddingLarge),

            // Quick Actions
            _buildQuickActionsSection(context, theme, themeProvider),
            const SizedBox(height: AppConstants.paddingLarge),

            // Audio Player Section
            if (audioProvider.currentSurah != null)
              _buildAudioPlayerSection(context, theme, audioProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    final now = DateTime.now();
    String greeting;

    if (now.hour < 12) {
      greeting = 'صباح الخير';
    } else if (now.hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: themeProvider.getGradientColors(context),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            greeting,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أهلاً بك في تطبيق نور القرآن',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(Icons.menu_book_rounded, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'ابدأ رحلتك مع القرآن الكريم',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContinueReadingSection(
    BuildContext context,
    ThemeData theme,
    QuranProvider quranProvider,
  ) {
    final lastSurah = quranProvider.getSurahByNumber(
      quranProvider.lastReadSurahNumber,
    );

    return Card(
      child: InkWell(
        onTap: () {
          if (lastSurah != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuranReaderScreen(surah: lastSurah),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: theme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusSmall,
                  ),
                ),
                child: Icon(
                  Icons.play_arrow_rounded,
                  color: theme.primaryColor,
                  size: 30,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'متابعة القراءة',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      lastSurah?.name ?? 'سورة غير معروفة',
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      'الآية ${quranProvider.lastReadAyahNumber}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodySmall?.color?.withOpacity(
                          0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios_rounded,
                color: theme.textTheme.bodySmall?.color?.withOpacity(0.5),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(
    BuildContext context,
    ThemeData theme,
    ThemeProvider themeProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.paddingMedium,
          mainAxisSpacing: AppConstants.paddingMedium,
          childAspectRatio: 1.5,
          children: [
            _buildQuickActionCard(
              context,
              theme,
              'قراءة القرآن',
              Icons.menu_book_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SurahListScreen(),
                ),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'البحث',
              Icons.search_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SearchScreen()),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'المفضلة',
              Icons.bookmark_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BookmarksScreen(),
                ),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'المكتبة الدينية',
              Icons.library_books_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const LibraryMainScreen()),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'اختيار القارئ',
              Icons.record_voice_over_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const ReciterSelectionScreen()),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'فهرس الصور',
              Icons.photo_library_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ImageGalleryScreen(),
                ),
              ),
            ),
            _buildQuickActionCard(
              context,
              theme,
              'سجل البحث',
              Icons.history_rounded,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SearchHistoryScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    ThemeData theme,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: theme.primaryColor),
              const SizedBox(height: 8),
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAudioPlayerSection(
    BuildContext context,
    ThemeData theme,
    AudioProvider audioProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المشغل الصوتي',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        audioProvider.currentSurah?.name ?? '',
                        style: theme.textTheme.bodyLarge,
                      ),
                      Text(
                        audioProvider.selectedReciter?.name ?? '',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: audioProvider.isPlaying
                      ? audioProvider.pause
                      : audioProvider.play,
                  icon: Icon(
                    audioProvider.isPlaying
                        ? Icons.pause_rounded
                        : Icons.play_arrow_rounded,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
