import 'package:flutter/material.dart';
import '../models/recommendation.dart';
import '../services/ai_service.dart';
import '../constants/app_constants.dart';

class AIRecommendationsScreen extends StatefulWidget {
  const AIRecommendationsScreen({super.key});

  @override
  State<AIRecommendationsScreen> createState() =>
      _AIRecommendationsScreenState();
}

class _AIRecommendationsScreenState extends State<AIRecommendationsScreen>
    with TickerProviderStateMixin {
  final AIService _aiService = AIService();

  List<Recommendation> _recommendations = [];
  List<ReadingInsight> _insights = [];
  bool _isLoadingRecommendations = false;
  bool _isLoadingInsights = false;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([_loadRecommendations(), _loadInsights()]);
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoadingRecommendations = true;
    });

    try {
      final recommendations = await _aiService.generateRecommendations(
        limit: 10,
      );
      setState(() {
        _recommendations = recommendations;
        _isLoadingRecommendations = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingRecommendations = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل التوصيات: $e')));
      }
    }
  }

  Future<void> _loadInsights() async {
    setState(() {
      _isLoadingInsights = true;
    });

    try {
      final insights = await _aiService.generateInsights();
      setState(() {
        _insights = insights;
        _isLoadingInsights = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingInsights = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الإحصائيات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('التوصيات الذكية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.lightbulb_outline), text: 'التوصيات'),
            Tab(icon: Icon(Icons.insights), text: 'الإحصائيات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildRecommendationsTab(theme), _buildInsightsTab(theme)],
      ),
    );
  }

  Widget _buildRecommendationsTab(ThemeData theme) {
    if (_isLoadingRecommendations) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري إنشاء التوصيات الذكية...'),
          ],
        ),
      );
    }

    if (_recommendations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lightbulb_outline, size: 64, color: theme.hintColor),
            const SizedBox(height: 16),
            Text(
              'لا توجد توصيات متاحة حالياً',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'استمر في القراءة لتحصل على توصيات مخصصة',
              style: TextStyle(color: theme.hintColor),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadRecommendations,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRecommendations,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _recommendations.length,
        itemBuilder: (context, index) {
          final recommendation = _recommendations[index];
          return _buildRecommendationCard(recommendation, theme);
        },
      ),
    );
  }

  Widget _buildInsightsTab(ThemeData theme) {
    if (_isLoadingInsights) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحليل بياناتك...'),
          ],
        ),
      );
    }

    if (_insights.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.insights, size: 64, color: theme.hintColor),
            const SizedBox(height: 16),
            Text('لا توجد إحصائيات متاحة', style: theme.textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(
              'ابدأ القراءة لتحصل على تحليلات مفيدة',
              style: TextStyle(color: theme.hintColor),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadInsights,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _insights.length,
        itemBuilder: (context, index) {
          final insight = _insights[index];
          return _buildInsightCard(insight, theme);
        },
      ),
    );
  }

  Widget _buildRecommendationCard(
    Recommendation recommendation,
    ThemeData theme,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _getRecommendationIcon(recommendation.type, theme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        recommendation.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildPriorityChip(recommendation.priority, theme),
                          const SizedBox(width: 8),
                          _buildTypeChip(recommendation.type, theme),
                          if (recommendation.isPersonalized) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.purple.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'مخصص',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple[700]!,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Text(
              recommendation.description,
              style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
            ),

            if (recommendation.content != null) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                decoration: BoxDecoration(
                  color: theme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  recommendation.content!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Text(
                  'الصلة: ${(recommendation.relevanceScore * 100).toInt()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _handleRecommendationAction(recommendation),
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('تطبيق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightCard(ReadingInsight insight, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.insights, color: theme.primaryColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getConfidenceColor(
                      insight.confidence,
                    ).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    insight.confidenceDisplayName,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getConfidenceColor(insight.confidence),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),

            Text(
              insight.description,
              style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            Text(
              'الفئة: ${insight.category}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getRecommendationIcon(RecommendationType type, ThemeData theme) {
    IconData iconData;
    Color color;

    switch (type) {
      case RecommendationType.surah:
        iconData = Icons.menu_book;
        color = Colors.blue;
        break;
      case RecommendationType.ayah:
        iconData = Icons.format_quote;
        color = Colors.green;
        break;
      case RecommendationType.topic:
        iconData = Icons.topic;
        color = Colors.orange;
        break;
      case RecommendationType.reading:
        iconData = Icons.auto_stories;
        color = Colors.purple;
        break;
      case RecommendationType.reflection:
        iconData = Icons.psychology;
        color = Colors.teal;
        break;
      case RecommendationType.memorization:
        iconData = Icons.memory;
        color = Colors.red;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(iconData, color: color, size: 20),
    );
  }

  Widget _buildPriorityChip(RecommendationPriority priority, ThemeData theme) {
    Color color;
    switch (priority) {
      case RecommendationPriority.low:
        color = Colors.grey;
        break;
      case RecommendationPriority.medium:
        color = Colors.orange;
        break;
      case RecommendationPriority.high:
        color = Colors.red;
        break;
      case RecommendationPriority.urgent:
        color = Colors.deepOrange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        priority.name,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildTypeChip(RecommendationType type, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        type.name,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: theme.primaryColor,
        ),
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.5) return Colors.orange;
    return Colors.red;
  }

  void _handleRecommendationAction(Recommendation recommendation) {
    // TODO: Implement recommendation actions
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم تطبيق التوصية: ${recommendation.title}')),
    );
  }
}
