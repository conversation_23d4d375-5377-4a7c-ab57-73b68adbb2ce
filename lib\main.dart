import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'constants/app_themes.dart';
import 'constants/app_constants.dart';
import 'providers/theme_provider.dart';
import 'providers/quran_provider.dart';
import 'providers/audio_provider.dart';
import 'providers/tafsir_provider.dart';
import 'providers/library_provider.dart';
import 'providers/religious_books_provider.dart';
import 'providers/hadith_provider.dart';
import 'providers/tafsir_api_provider.dart';
import 'core/app_state_manager.dart';
import 'services/smart_notification_service.dart';
import 'services/achievement_service.dart';
import 'screens/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // تهيئة الخدمات الأساسية
  await _initializeServices();

  runApp(const NoorQuranApp());
}

/// تهيئة الخدمات الأساسية
Future<void> _initializeServices() async {
  try {
    // تهيئة مدير الحالة العام
    await AppStateManager().initialize();

    // تهيئة خدمة الإشعارات الذكية
    await SmartNotificationService.initialize();

    // تهيئة خدمة الإنجازات
    await AchievementService.initialize();

    debugPrint('✅ تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة الخدمات: $e');
  }
}

class NoorQuranApp extends StatelessWidget {
  const NoorQuranApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => QuranProvider()),
        ChangeNotifierProvider(create: (_) => AudioProvider()),
        ChangeNotifierProvider(create: (_) => TafsirProvider()),
        ChangeNotifierProvider(create: (_) => LibraryProvider()),
        ChangeNotifierProvider(create: (_) => ReligiousBooksProvider()),
        ChangeNotifierProvider(create: (_) => HadithProvider()),
        ChangeNotifierProvider(create: (_) => TafsirApiProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: AppThemes.lightTheme,
            darkTheme: AppThemes.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const SplashScreen(),
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.rtl,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
