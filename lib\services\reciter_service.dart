import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/library_models.dart';

/// خدمة القراء
class ReciterService {
  static List<Reciter>? _cachedReciters;

  /// جلب جميع القراء
  static Future<List<Reciter>> getAllReciters() async {
    if (_cachedReciters != null) {
      return _cachedReciters!;
    }

    try {
      debugPrint('🎵 جلب قائمة القراء...');

      // محاولة تحميل القراء المحليين أولاً
      List<Reciter> reciters = [];
      try {
        reciters = await _loadLocalReciters();
        debugPrint('✅ تم تحميل ${reciters.length} قارئ من الملف المحلي');
      } catch (e) {
        debugPrint(
            '⚠️ فشل تحميل القراء المحليين، استخدام القائمة الافتراضية: $e');
        reciters = _getDefaultReciters();
      }

      _cachedReciters = reciters;
      return reciters;
    } catch (e) {
      debugPrint('❌ خطأ في جلب القراء: $e');
      return [];
    }
  }

  /// تحميل القراء من الملف المحلي
  static Future<List<Reciter>> _loadLocalReciters() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/local_reciters.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> recitersData = jsonData['reciters'];

      final List<Reciter> localReciters = recitersData.map((reciterJson) {
        return Reciter(
          id: reciterJson['id'],
          name: reciterJson['name'],
          nameArabic: reciterJson['nameArabic'],
          country: reciterJson['country'],
          style: reciterJson['style'],
          isAvailable: reciterJson['isLocal'] ?? true,
          audioUrls: Map<String, String>.from(reciterJson['audioUrls']),
        );
      }).toList();

      // إضافة القراء الافتراضيين أيضاً
      final defaultReciters = _getDefaultReciters();

      return [...localReciters, ...defaultReciters];
    } catch (e) {
      debugPrint('❌ خطأ في تحميل القراء المحليين: $e');
      rethrow;
    }
  }

  /// جلب قارئ محدد
  static Future<Reciter?> getReciterById(String reciterId) async {
    final reciters = await getAllReciters();
    try {
      return reciters.firstWhere((reciter) => reciter.id == reciterId);
    } catch (e) {
      debugPrint('❌ لم يتم العثور على القارئ: $reciterId');
      return null;
    }
  }

  /// جلب القراء حسب البلد
  static Future<List<Reciter>> getRecitersByCountry(String country) async {
    final reciters = await getAllReciters();
    return reciters.where((reciter) => reciter.country == country).toList();
  }

  /// جلب القراء حسب النمط
  static Future<List<Reciter>> getRecitersByStyle(String style) async {
    final reciters = await getAllReciters();
    return reciters.where((reciter) => reciter.style == style).toList();
  }

  /// البحث في القراء
  static Future<List<Reciter>> searchReciters(String query) async {
    final reciters = await getAllReciters();
    final lowerQuery = query.toLowerCase();

    return reciters.where((reciter) {
      return reciter.name.toLowerCase().contains(lowerQuery) ||
          reciter.nameArabic.toLowerCase().contains(lowerQuery) ||
          reciter.country.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// الحصول على القراء الافتراضيين
  static List<Reciter> _getDefaultReciters() {
    return [
      Reciter(
        id: 'abdul_basit',
        name: 'Abdul Basit Abdul Samad',
        nameArabic: 'عبد الباسط عبد الصمد',
        country: 'مصر',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001':
              'https://server8.mp3quran.net/abd_basit/Almusshaf-Al-Mojawwad/001.mp3',
          '002':
              'https://server8.mp3quran.net/abd_basit/Almusshaf-Al-Mojawwad/002.mp3',
          '003':
              'https://server8.mp3quran.net/abd_basit/Almusshaf-Al-Mojawwad/003.mp3',
        },
      ),
      Reciter(
        id: 'mishary_rashid',
        name: 'Mishary Rashid Alafasy',
        nameArabic: 'مشاري راشد العفاسي',
        country: 'الكويت',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server12.mp3quran.net/maher/001.mp3',
          '002': 'https://server12.mp3quran.net/maher/002.mp3',
          '003': 'https://server12.mp3quran.net/maher/003.mp3',
        },
      ),
      Reciter(
        id: 'maher_muaiqly',
        name: 'Maher Al Muaiqly',
        nameArabic: 'ماهر المعيقلي',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server12.mp3quran.net/maher/001.mp3',
          '002': 'https://server12.mp3quran.net/maher/002.mp3',
          '003': 'https://server12.mp3quran.net/maher/003.mp3',
        },
      ),
      Reciter(
        id: 'sudais',
        name: 'Abdul Rahman Al-Sudais',
        nameArabic: 'عبد الرحمن السديس',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server11.mp3quran.net/sds/001.mp3',
          '002': 'https://server11.mp3quran.net/sds/002.mp3',
          '003': 'https://server11.mp3quran.net/sds/003.mp3',
        },
      ),
      Reciter(
        id: 'shuraim',
        name: 'Saad Al-Ghamdi',
        nameArabic: 'سعد الغامدي',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server7.mp3quran.net/s_gmd/001.mp3',
          '002': 'https://server7.mp3quran.net/s_gmd/002.mp3',
          '003': 'https://server7.mp3quran.net/s_gmd/003.mp3',
        },
      ),
      Reciter(
        id: 'husary',
        name: 'Mahmoud Khalil Al-Husary',
        nameArabic: 'محمود خليل الحصري',
        country: 'مصر',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server13.mp3quran.net/husr/001.mp3',
          '002': 'https://server13.mp3quran.net/husr/002.mp3',
          '003': 'https://server13.mp3quran.net/husr/003.mp3',
        },
      ),
      Reciter(
        id: 'minshawi',
        name: 'Mohamed Siddiq Al-Minshawi',
        nameArabic: 'محمد صديق المنشاوي',
        country: 'مصر',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server10.mp3quran.net/minsh/001.mp3',
          '002': 'https://server10.mp3quran.net/minsh/002.mp3',
          '003': 'https://server10.mp3quran.net/minsh/003.mp3',
        },
      ),
      Reciter(
        id: 'ajmy',
        name: 'Ahmad Al-Ajmy',
        nameArabic: 'أحمد العجمي',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server6.mp3quran.net/ajm/001.mp3',
          '002': 'https://server6.mp3quran.net/ajm/002.mp3',
          '003': 'https://server6.mp3quran.net/ajm/003.mp3',
        },
      ),
      Reciter(
        id: 'tablawi',
        name: 'Mohamed Al-Tablawi',
        nameArabic: 'محمد الطبلاوي',
        country: 'مصر',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server8.mp3quran.net/mtawla/001.mp3',
          '002': 'https://server8.mp3quran.net/mtawla/002.mp3',
          '003': 'https://server8.mp3quran.net/mtawla/003.mp3',
        },
      ),
      Reciter(
        id: 'juhany',
        name: 'Abdullah Al-Juhany',
        nameArabic: 'عبد الله الجهني',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server5.mp3quran.net/jhn/001.mp3',
          '002': 'https://server5.mp3quran.net/jhn/002.mp3',
          '003': 'https://server5.mp3quran.net/jhn/003.mp3',
        },
      ),
      Reciter(
        id: 'basfar',
        name: 'Abdullah Basfar',
        nameArabic: 'عبد الله بصفر',
        country: 'السعودية',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server9.mp3quran.net/basfar/001.mp3',
          '002': 'https://server9.mp3quran.net/basfar/002.mp3',
          '003': 'https://server9.mp3quran.net/basfar/003.mp3',
        },
      ),
      Reciter(
        id: 'bukhatir',
        name: 'Ahmed Bukhatir',
        nameArabic: 'أحمد بوخاطر',
        country: 'الإمارات',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server14.mp3quran.net/bukhatir/001.mp3',
          '002': 'https://server14.mp3quran.net/bukhatir/002.mp3',
          '003': 'https://server14.mp3quran.net/bukhatir/003.mp3',
        },
      ),
      // القراء الموريتانيون
      Reciter(
        id: 'mohamed_ould_meimoun',
        name: 'Mohamed Ould Meimoun',
        nameArabic: 'محمد ولد ميمون',
        country: 'موريتانيا',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server15.mp3quran.net/meimoun/001.mp3',
          '002': 'https://server15.mp3quran.net/meimoun/002.mp3',
          '003': 'https://server15.mp3quran.net/meimoun/003.mp3',
        },
      ),
      Reciter(
        id: 'ahmed_ould_mohamed_salem',
        name: 'Ahmed Ould Mohamed Salem',
        nameArabic: 'أحمد ولد محمد سالم',
        country: 'موريتانيا',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server16.mp3quran.net/salem/001.mp3',
          '002': 'https://server16.mp3quran.net/salem/002.mp3',
          '003': 'https://server16.mp3quran.net/salem/003.mp3',
        },
      ),
      Reciter(
        id: 'mohamed_ould_taleb',
        name: 'Mohamed Ould Taleb',
        nameArabic: 'محمد ولد الطالب',
        country: 'موريتانيا',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server17.mp3quran.net/taleb/001.mp3',
          '002': 'https://server17.mp3quran.net/taleb/002.mp3',
          '003': 'https://server17.mp3quran.net/taleb/003.mp3',
        },
      ),
      Reciter(
        id: 'sidi_mohamed_ould_habib',
        name: 'Sidi Mohamed Ould Habib',
        nameArabic: 'سيدي محمد ولد حبيب',
        country: 'موريتانيا',
        style: 'مرتل',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server18.mp3quran.net/habib/001.mp3',
          '002': 'https://server18.mp3quran.net/habib/002.mp3',
          '003': 'https://server18.mp3quran.net/habib/003.mp3',
        },
      ),
      Reciter(
        id: 'mohamed_ould_sidi_yahya',
        name: 'Mohamed Ould Sidi Yahya',
        nameArabic: 'محمد ولد سيدي يحيى',
        country: 'موريتانيا',
        style: 'مجود',
        imageUrl: null,
        isAvailable: true,
        audioUrls: {
          '001': 'https://server19.mp3quran.net/yahya/001.mp3',
          '002': 'https://server19.mp3quran.net/yahya/002.mp3',
          '003': 'https://server19.mp3quran.net/yahya/003.mp3',
        },
      ),
    ];
  }

  /// الحصول على البلدان المتاحة
  static Future<List<String>> getAvailableCountries() async {
    final reciters = await getAllReciters();
    final countries = reciters.map((r) => r.country).toSet().toList();
    countries.sort();
    return countries;
  }

  /// الحصول على الأنماط المتاحة
  static Future<List<String>> getAvailableStyles() async {
    final reciters = await getAllReciters();
    final styles = reciters.map((r) => r.style).toSet().toList();
    styles.sort();
    return styles;
  }

  /// تنظيف الذاكرة المؤقتة
  static void clearCache() {
    _cachedReciters = null;
  }
}
