class Reciter {
  final String id;
  final String name;
  final String englishName;
  final String style;
  final String country;
  final String language;
  final String format;
  final int bitrate;
  final String baseUrl;
  final String? imageUrl;
  final String? description;
  final bool isPopular;
  final List<String> availableSurahs;

  Reciter({
    required this.id,
    required this.name,
    required this.englishName,
    required this.style,
    required this.country,
    required this.language,
    required this.format,
    required this.bitrate,
    required this.baseUrl,
    this.imageUrl,
    this.description,
    this.isPopular = false,
    this.availableSurahs = const [],
  });

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      style: json['style'] ?? '',
      country: json['country'] ?? '',
      language: json['language'] ?? 'ar',
      format: json['format'] ?? 'mp3',
      bitrate: json['bitrate'] ?? 128,
      baseUrl: json['baseUrl'] ?? '',
      imageUrl: json['imageUrl'],
      description: json['description'],
      isPopular: json['isPopular'] ?? false,
      availableSurahs: json['availableSurahs'] != null
          ? List<String>.from(json['availableSurahs'])
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'englishName': englishName,
      'style': style,
      'country': country,
      'language': language,
      'format': format,
      'bitrate': bitrate,
      'baseUrl': baseUrl,
      'imageUrl': imageUrl,
      'description': description,
      'isPopular': isPopular,
      'availableSurahs': availableSurahs,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'englishName': englishName,
      'style': style,
      'country': country,
      'language': language,
      'format': format,
      'bitrate': bitrate,
      'baseUrl': baseUrl,
      'imageUrl': imageUrl,
      'description': description,
      'isPopular': isPopular ? 1 : 0,
      'availableSurahs': availableSurahs.join(','),
    };
  }

  factory Reciter.fromMap(Map<String, dynamic> map) {
    return Reciter(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      englishName: map['englishName'] ?? '',
      style: map['style'] ?? '',
      country: map['country'] ?? '',
      language: map['language'] ?? 'ar',
      format: map['format'] ?? 'mp3',
      bitrate: map['bitrate'] ?? 128,
      baseUrl: map['baseUrl'] ?? '',
      imageUrl: map['imageUrl'],
      description: map['description'],
      isPopular: map['isPopular'] == 1,
      availableSurahs: map['availableSurahs'] != null
          ? map['availableSurahs'].toString().split(',')
          : [],
    );
  }

  @override
  String toString() {
    return 'Reciter{id: $id, name: $name, englishName: $englishName, country: $country}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Reciter && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  String getAudioUrl(int surahNumber) {
    String formattedSurahNumber = surahNumber.toString().padLeft(3, '0');

    // Use a working audio URL for testing
    if (id == 'abdulbasit_abdulsamad') {
      return 'https://server8.mp3quran.net/afs/$formattedSurahNumber.mp3';
    } else if (id == 'mishari_alafasy') {
      return 'https://server8.mp3quran.net/mishary/$formattedSurahNumber.mp3';
    } else if (id == 'abdurrahman_sudais') {
      return 'https://server8.mp3quran.net/sudais/$formattedSurahNumber.mp3';
    } else if (id == 'saad_alghamdi') {
      return 'https://server8.mp3quran.net/s_ghamdi/$formattedSurahNumber.mp3';
    } else if (id == 'maher_almuaiqly') {
      return 'https://server8.mp3quran.net/maher/$formattedSurahNumber.mp3';
    }

    // Fallback to base URL
    return '$baseUrl/$formattedSurahNumber.$format';
  }

  bool hasSurah(int surahNumber) {
    if (availableSurahs.isEmpty) {
      return true; // Assume all surahs available if not specified
    }
    return availableSurahs.contains(surahNumber.toString().padLeft(3, '0'));
  }

  String get displayName => name.isNotEmpty ? name : englishName;

  String get qualityDescription {
    if (bitrate >= 320) return 'جودة عالية جداً';
    if (bitrate >= 192) return 'جودة عالية';
    if (bitrate >= 128) return 'جودة متوسطة';
    return 'جودة منخفضة';
  }

  String get styleDescription {
    switch (style.toLowerCase()) {
      case 'murattal':
        return 'مرتل';
      case 'mujawwad':
        return 'مجود';
      case 'muallim':
        return 'تعليمي';
      default:
        return style;
    }
  }
}

// Predefined popular reciters
class PopularReciters {
  static List<Reciter> get all => [
    Reciter(
      id: 'abdulbasit_abdulsamad',
      name: 'عبد الباسط عبد الصمد',
      englishName: 'Abdul Basit Abdul Samad',
      style: 'Mujawwad',
      country: 'مصر',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/afs',
      isPopular: true,
      description: 'من أشهر قراء القرآن الكريم في العالم الإسلامي',
    ),
    Reciter(
      id: 'mishari_alafasy',
      name: 'مشاري العفاسي',
      englishName: 'Mishari Alafasy',
      style: 'Murattal',
      country: 'الكويت',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/mishary',
      isPopular: true,
      description: 'قارئ كويتي مشهور بصوته العذب',
    ),
    Reciter(
      id: 'abdurrahman_sudais',
      name: 'عبد الرحمن السديس',
      englishName: 'Abdurrahman As-Sudais',
      style: 'Murattal',
      country: 'السعودية',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/sudais',
      isPopular: true,
      description: 'إمام وخطيب المسجد الحرام',
    ),
    Reciter(
      id: 'ahmed_alajamy',
      name: 'أحمد العجمي',
      englishName: 'Ahmed Al Ajamy',
      style: 'Murattal',
      country: 'السعودية',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/ajm',
      isPopular: true,
      description: 'قارئ سعودي معروف بتلاوته المؤثرة',
    ),
    Reciter(
      id: 'saad_alghamdi',
      name: 'سعد الغامدي',
      englishName: 'Saad Al Ghamdi',
      style: 'Murattal',
      country: 'السعودية',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/s_ghamdi',
      isPopular: true,
      description: 'إمام المسجد الحرام سابقاً',
    ),
    Reciter(
      id: 'yasser_aldosari',
      name: 'ياسر الدوسري',
      englishName: 'Yasser Al Dosari',
      style: 'Murattal',
      country: 'السعودية',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/yasser',
      isPopular: true,
      description: 'إمام المسجد الحرام',
    ),
    Reciter(
      id: 'maher_almuaiqly',
      name: 'ماهر المعيقلي',
      englishName: 'Maher Al Muaiqly',
      style: 'Murattal',
      country: 'السعودية',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/maher',
      isPopular: true,
      description: 'إمام المسجد الحرام',
    ),
    Reciter(
      id: 'mohamed_siddiq_alminshawi',
      name: 'محمد صديق المنشاوي',
      englishName: 'Mohamed Siddiq Al-Minshawi',
      style: 'Mujawwad',
      country: 'مصر',
      language: 'ar',
      format: 'mp3',
      bitrate: 128,
      baseUrl: 'https://server8.mp3quran.net/minshawi',
      isPopular: true,
      description: 'من أعظم قراء القرآن في التاريخ الحديث',
    ),
  ];

  static Reciter? getById(String id) {
    try {
      return all.firstWhere((reciter) => reciter.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<Reciter> getPopular() {
    return all.where((reciter) => reciter.isPopular).toList();
  }
}
