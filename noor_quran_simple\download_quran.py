#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import time

def download_quran_data():
    """تحميل بيانات القرآن الكريم من API خارجي"""
    
    base_url = "http://api.alquran.cloud/v1"
    
    print("بدء تحميل بيانات القرآن الكريم...")
    
    try:
        # جلب قائمة السور
        print("جلب قائمة السور...")
        surahs_response = requests.get(f"{base_url}/surah")
        if surahs_response.status_code != 200:
            print("فشل في جلب قائمة السور")
            return
        
        surahs_list = surahs_response.json()['data']
        
        quran_data = {
            "surahs": []
        }
        
        # جلب كل سورة مع آياتها
        for i, surah_info in enumerate(surahs_list[:10], 1):  # أول 10 سور فقط للاختبار
            surah_number = surah_info['number']
            print(f"جلب السورة {surah_number}: {surah_info['name']}...")
            
            try:
                # جلب السورة مع الآيات
                surah_response = requests.get(f"{base_url}/surah/{surah_number}")
                if surah_response.status_code == 200:
                    surah_data = surah_response.json()['data']
                    
                    # تحويل البيانات للتنسيق المطلوب
                    formatted_surah = {
                        "number": surah_data['number'],
                        "name": surah_data['name'],
                        "englishName": surah_data['englishName'],
                        "englishNameTranslation": surah_data['englishNameTranslation'],
                        "revelationType": surah_data['revelationType'],
                        "numberOfAyahs": surah_data['numberOfAyahs'],
                        "ayahs": []
                    }
                    
                    # إضافة الآيات
                    for ayah in surah_data['ayahs']:
                        formatted_ayah = {
                            "number": ayah['number'],
                            "text": ayah['text'],
                            "numberInSurah": ayah['numberInSurah'],
                            "juz": ayah.get('juz', 1),
                            "manzil": ayah.get('manzil', 1),
                            "page": ayah.get('page', 1),
                            "ruku": ayah.get('ruku', 1),
                            "hizbQuarter": ayah.get('hizbQuarter', 1),
                            "sajda": ayah.get('sajda', False)
                        }
                        formatted_surah['ayahs'].append(formatted_ayah)
                    
                    quran_data['surahs'].append(formatted_surah)
                    print(f"تم جلب السورة {surah_number} بنجاح ({len(formatted_surah['ayahs'])} آية)")
                    
                else:
                    print(f"فشل في جلب السورة {surah_number}")
                
                # انتظار قصير لتجنب إرهاق الخادم
                time.sleep(0.5)
                
            except Exception as e:
                print(f"خطأ في جلب السورة {surah_number}: {e}")
                continue
        
        # حفظ البيانات
        print("حفظ البيانات...")
        with open('assets/data/quran_data.json', 'w', encoding='utf-8') as f:
            json.dump(quran_data, f, ensure_ascii=False, indent=2)
        
        print(f"تم تحميل وحفظ {len(quran_data['surahs'])} سورة بنجاح!")
        
        # إحصائيات
        total_ayahs = sum(len(surah['ayahs']) for surah in quran_data['surahs'])
        print(f"إجمالي الآيات: {total_ayahs}")
        
    except Exception as e:
        print(f"خطأ عام: {e}")

def create_sample_data():
    """إنشاء بيانات عينة في حالة فشل التحميل"""
    
    sample_data = {
        "surahs": [
            {
                "number": 1,
                "name": "الفاتحة",
                "englishName": "Al-Fatihah",
                "englishNameTranslation": "The Opening",
                "revelationType": "Meccan",
                "numberOfAyahs": 7,
                "ayahs": [
                    {
                        "number": 1,
                        "text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                        "numberInSurah": 1,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 2,
                        "text": "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
                        "numberInSurah": 2,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 3,
                        "text": "الرَّحْمَٰنِ الرَّحِيمِ",
                        "numberInSurah": 3,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 4,
                        "text": "مَالِكِ يَوْمِ الدِّينِ",
                        "numberInSurah": 4,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 5,
                        "text": "إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ",
                        "numberInSurah": 5,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 6,
                        "text": "اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ",
                        "numberInSurah": 6,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    },
                    {
                        "number": 7,
                        "text": "صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ",
                        "numberInSurah": 7,
                        "juz": 1,
                        "manzil": 1,
                        "page": 1,
                        "ruku": 1,
                        "hizbQuarter": 1,
                        "sajda": False
                    }
                ]
            }
        ]
    }
    
    with open('assets/data/quran_data.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("تم إنشاء بيانات عينة")

if __name__ == "__main__":
    try:
        download_quran_data()
    except:
        print("فشل التحميل، إنشاء بيانات عينة...")
        create_sample_data()
