import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/library_models.dart';

/// خدمة الكتب الفقهية
class FiqhBooksService {
  static List<Book>? _cachedBooks;
  static List<Book>? _cachedTafsirBooks;

  /// جلب جميع الكتب الفقهية
  static Future<List<Book>> getAllFiqhBooks() async {
    if (_cachedBooks != null) {
      return _cachedBooks!;
    }

    try {
      debugPrint('📚 جلب الكتب الفقهية...');
      final books = await _loadLocalBooks();
      _cachedBooks = books;
      return books;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الكتب الفقهية: $e');
      return _getDefaultFiqhBooks();
    }
  }

  /// جلب جميع كتب التفسير
  static Future<List<Book>> getAllTafsirBooks() async {
    if (_cachedTafsirBooks != null) {
      return _cachedTafsirBooks!;
    }

    try {
      debugPrint('📚 جلب كتب التفسير...');
      final books = await _loadTafsirBooks();
      _cachedTafsirBooks = books;
      return books;
    } catch (e) {
      debugPrint('❌ خطأ في جلب كتب التفسير: $e');
      return _getDefaultTafsirBooks();
    }
  }

  /// جلب كتاب محدد
  static Future<Book?> getBookById(String bookId) async {
    final fiqhBooks = await getAllFiqhBooks();
    final tafsirBooks = await getAllTafsirBooks();
    final allBooks = [...fiqhBooks, ...tafsirBooks];
    
    try {
      return allBooks.firstWhere((book) => book.id == bookId);
    } catch (e) {
      debugPrint('❌ لم يتم العثور على الكتاب: $bookId');
      return null;
    }
  }

  /// تحميل الكتب من ملف محلي
  static Future<List<Book>> _loadLocalBooks() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/data/fiqh_books.json');
      final List<dynamic> jsonData = json.decode(jsonString);
      return jsonData.map((json) => Book.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على ملف البيانات المحلي، استخدام البيانات الافتراضية');
      return _getDefaultFiqhBooks();
    }
  }

  /// تحميل كتب التفسير من ملف محلي
  static Future<List<Book>> _loadTafsirBooks() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/data/tafsir_books.json');
      final List<dynamic> jsonData = json.decode(jsonString);
      return jsonData.map((json) => Book.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على ملف كتب التفسير، استخدام البيانات الافتراضية');
      return _getDefaultTafsirBooks();
    }
  }

  /// الحصول على الكتب الفقهية الافتراضية
  static List<Book> _getDefaultFiqhBooks() {
    return [
      Book(
        id: 'fiqh_siyam',
        title: 'فقه الصيام',
        author: 'مجموعة من العلماء',
        description: 'كتاب شامل في أحكام الصيام وآدابه',
        type: BookType.fiqh,
        chapters: [
          Chapter(
            id: 'chapter_1',
            title: 'تعريف الصيام وحكمه',
            order: 1,
            summary: 'تعريف الصيام لغة واصطلاحاً وبيان حكمه الشرعي',
            sections: [
              Section(
                id: 'section_1_1',
                title: 'تعريف الصيام',
                content: '''الصيام لغة: الإمساك والكف عن الشيء.

واصطلاحاً: التعبد لله تعالى بالإمساك عن المفطرات من طلوع الفجر الصادق إلى غروب الشمس.

والصيام عبادة عظيمة فرضها الله على عباده، وجعلها ركناً من أركان الإسلام الخمسة.''',
                order: 1,
                type: SectionType.text,
                explanation: 'هذا التعريف يوضح المعنى اللغوي والاصطلاحي للصيام، ويبين أنه عبادة مفروضة.',
              ),
              Section(
                id: 'section_1_2',
                title: 'حكم الصيام',
                content: '''الصيام فريضة على كل مسلم بالغ عاقل قادر، وهو ركن من أركان الإسلام.

قال الله تعالى: "يَا أَيُّهَا الَّذِينَ آمَنُوا كُتِبَ عَلَيْكُمُ الصِّيَامُ كَمَا كُتِبَ عَلَى الَّذِينَ مِن قَبْلِكُمْ لَعَلَّكُمْ تَتَّقُونَ"

وقال النبي صلى الله عليه وسلم: "بني الإسلام على خمس: شهادة أن لا إله إلا الله وأن محمداً رسول الله، وإقام الصلاة، وإيتاء الزكاة، وحج البيت، وصوم رمضان".''',
                order: 2,
                type: SectionType.text,
                explanation: 'هذا القسم يبين الأدلة من القرآن والسنة على فرضية الصيام.',
              ),
            ],
          ),
          Chapter(
            id: 'chapter_2',
            title: 'شروط وجوب الصيام',
            order: 2,
            summary: 'الشروط التي يجب توفرها لوجوب الصيام على المكلف',
            sections: [
              Section(
                id: 'section_2_1',
                title: 'الشروط العامة',
                content: '''يشترط لوجوب الصيام الشروط التالية:

1. الإسلام: فلا يجب على الكافر
2. البلوغ: فلا يجب على الصغير
3. العقل: فلا يجب على المجنون
4. القدرة: فلا يجب على العاجز
5. الإقامة: فللمسافر الفطر
6. السلامة من الموانع: كالحيض والنفاس للمرأة''',
                order: 1,
                type: SectionType.text,
                explanation: 'هذه الشروط مستنبطة من النصوص الشرعية وإجماع العلماء.',
              ),
            ],
          ),
        ],
      ),
      Book(
        id: 'fiqh_hajj',
        title: 'فقه الحج والعمرة',
        author: 'مجموعة من العلماء',
        description: 'دليل شامل لأحكام الحج والعمرة',
        type: BookType.fiqh,
        chapters: [
          Chapter(
            id: 'chapter_1',
            title: 'تعريف الحج وحكمه',
            order: 1,
            summary: 'تعريف الحج وبيان حكمه وفضله',
            sections: [
              Section(
                id: 'section_1_1',
                title: 'تعريف الحج',
                content: '''الحج لغة: القصد إلى معظم.

واصطلاحاً: التعبد لله بأداء المناسك المخصوصة في المكان المخصوص في الزمان المخصوص.

والحج ركن من أركان الإسلام الخمسة، فرضه الله على المستطيع من عباده مرة واحدة في العمر.''',
                order: 1,
                type: SectionType.text,
                explanation: 'تعريف شامل للحج يوضح معناه اللغوي والشرعي.',
              ),
            ],
          ),
        ],
      ),
      Book(
        id: 'fiqh_salah',
        title: 'فقه الصلاة',
        author: 'مجموعة من العلماء',
        description: 'كتاب شامل في أحكام الصلاة وآدابها',
        type: BookType.fiqh,
        chapters: [
          Chapter(
            id: 'chapter_1',
            title: 'أحكام الطهارة',
            order: 1,
            summary: 'أحكام الطهارة الصغرى والكبرى وما يتعلق بها',
            sections: [
              Section(
                id: 'section_1_1',
                title: 'تعريف الطهارة',
                content: '''الطهارة لغة: النظافة والنزاهة من الأقذار.

واصطلاحاً: رفع الحدث وإزالة النجس.

والطهارة شرط لصحة الصلاة، قال تعالى: "وَثِيَابَكَ فَطَهِّرْ"''',
                order: 1,
                type: SectionType.text,
                explanation: 'الطهارة أساس العبادة في الإسلام وشرط لصحة الصلاة.',
              ),
            ],
          ),
        ],
      ),
    ];
  }

  /// الحصول على كتب التفسير الافتراضية
  static List<Book> _getDefaultTafsirBooks() {
    return [
      Book(
        id: 'tafsir_jalalayn',
        title: 'تفسير الجلالين',
        author: 'جلال الدين المحلي وجلال الدين السيوطي',
        description: 'تفسير مختصر للقرآن الكريم، يجمع بين الإيجاز والوضوح',
        type: BookType.tafsir,
        chapters: [
          Chapter(
            id: 'chapter_1',
            title: 'تفسير سورة الفاتحة',
            order: 1,
            summary: 'تفسير آيات سورة الفاتحة',
            sections: [
              Section(
                id: 'section_1_1',
                title: 'بسم الله الرحمن الرحيم',
                content: '''بسم الله الرحمن الرحيم: أي أبتدئ بتسمية الله مستعيناً به، والله علم على المعبود بحق.

الرحمن: ذو الرحمة العامة التي وسعت كل شيء.

الرحيم: ذو الرحمة الخاصة بالمؤمنين في الآخرة.''',
                order: 1,
                type: SectionType.verse,
                explanation: 'البسملة افتتاح لكل عمل مبارك.',
              ),
            ],
          ),
        ],
      ),
      Book(
        id: 'tafsir_ibn_kathir',
        title: 'تفسير ابن كثير (مختارات)',
        author: 'إسماعيل بن عمر بن كثير',
        description: 'تفسير القرآن العظيم، من أشهر التفاسير بالمأثور',
        type: BookType.tafsir,
        chapters: [
          Chapter(
            id: 'chapter_1',
            title: 'فضائل القرآن الكريم',
            order: 1,
            summary: 'بيان فضل القرآن الكريم وآدابه',
            sections: [
              Section(
                id: 'section_1_1',
                title: 'فضل تلاوة القرآن',
                content: '''قال رسول الله صلى الله عليه وسلم: "من قرأ حرفاً من كتاب الله فله به حسنة، والحسنة بعشر أمثالها، لا أقول ألم حرف، ولكن ألف حرف ولام حرف وميم حرف".

وقال أيضاً: "اقرؤوا القرآن فإنه يأتي يوم القيامة شفيعاً لأصحابه".''',
                order: 1,
                type: SectionType.hadith,
                explanation: 'هذه الأحاديث تبين عظم أجر تلاوة القرآن الكريم.',
              ),
            ],
          ),
        ],
      ),
    ];
  }

  /// تنظيف الذاكرة المؤقتة
  static void clearCache() {
    _cachedBooks = null;
    _cachedTafsirBooks = null;
  }
}
