import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/achievement.dart';
import '../core/app_state_manager.dart';
import 'smart_notification_service.dart';
import 'analytics_service.dart';
import '../models/analytics_event.dart';

/// خدمة الإنجازات والتحديات - لمسة تحفيزية خاصة
class AchievementService {
  static const String _keyPrefix = 'achievements_';
  static const String _unlockedKey = '${_keyPrefix}unlocked';
  static const String _progressKey = '${_keyPrefix}progress';
  static const String _challengesKey = '${_keyPrefix}challenges';

  static List<Achievement> _allAchievements = [];
  static Set<String> _unlockedAchievements = {};
  static Map<String, double> _achievementProgress = {};
  static List<Challenge> _activeChallenges = [];

  /// تهيئة خدمة الإنجازات
  static Future<void> initialize() async {
    await _loadAchievements();
    await _loadProgress();
    await _loadChallenges();
    _initializeDefaultAchievements();

    debugPrint('✅ تم تهيئة خدمة الإنجازات');
  }

  /// تهيئة الإنجازات الافتراضية
  static void _initializeDefaultAchievements() {
    _allAchievements = [
      // إنجازات القراءة
      Achievement(
        id: 'first_read',
        title: 'القراءة الأولى',
        description: 'اقرأ أول آية في التطبيق',
        icon: '📖',
        category: AchievementCategory.reading,
        targetValue: 1,
        rewardPoints: 10,
      ),
      Achievement(
        id: 'ayah_master_100',
        title: 'قارئ مبتدئ',
        description: 'اقرأ 100 آية',
        icon: '🌟',
        category: AchievementCategory.reading,
        targetValue: 100,
        rewardPoints: 50,
      ),
      Achievement(
        id: 'ayah_master_500',
        title: 'قارئ متوسط',
        description: 'اقرأ 500 آية',
        icon: '⭐',
        category: AchievementCategory.reading,
        targetValue: 500,
        rewardPoints: 100,
      ),
      Achievement(
        id: 'ayah_master_1000',
        title: 'قارئ متقدم',
        description: 'اقرأ 1000 آية',
        icon: '🏆',
        category: AchievementCategory.reading,
        targetValue: 1000,
        rewardPoints: 200,
      ),
      Achievement(
        id: 'complete_quran',
        title: 'خاتم القرآن',
        description: 'اقرأ القرآن الكريم كاملاً',
        icon: '👑',
        category: AchievementCategory.reading,
        targetValue: 6236, // عدد آيات القرآن
        rewardPoints: 1000,
        isSpecial: true,
      ),

      // إنجازات السلسلة
      Achievement(
        id: 'streak_3',
        title: 'بداية قوية',
        description: 'اقرأ لمدة 3 أيام متتالية',
        icon: '🔥',
        category: AchievementCategory.streak,
        targetValue: 3,
        rewardPoints: 30,
      ),
      Achievement(
        id: 'streak_7',
        title: 'أسبوع مبارك',
        description: 'اقرأ لمدة أسبوع كامل',
        icon: '🔥',
        category: AchievementCategory.streak,
        targetValue: 7,
        rewardPoints: 70,
      ),
      Achievement(
        id: 'streak_30',
        title: 'شهر من النور',
        description: 'اقرأ لمدة 30 يوماً متتالية',
        icon: '🔥',
        category: AchievementCategory.streak,
        targetValue: 30,
        rewardPoints: 300,
        isSpecial: true,
      ),
      Achievement(
        id: 'streak_100',
        title: 'المثابر الذهبي',
        description: 'اقرأ لمدة 100 يوم متتالي',
        icon: '🏅',
        category: AchievementCategory.streak,
        targetValue: 100,
        rewardPoints: 1000,
        isSpecial: true,
      ),

      // إنجازات الوقت
      Achievement(
        id: 'time_1hour',
        title: 'ساعة مع القرآن',
        description: 'اقض ساعة في قراءة القرآن',
        icon: '⏰',
        category: AchievementCategory.time,
        targetValue: 60, // دقائق
        rewardPoints: 60,
      ),
      Achievement(
        id: 'time_10hours',
        title: 'عاشق القرآن',
        description: 'اقض 10 ساعات في قراءة القرآن',
        icon: '💖',
        category: AchievementCategory.time,
        targetValue: 600,
        rewardPoints: 200,
      ),

      // إنجازات السور
      Achievement(
        id: 'surah_fatiha',
        title: 'أم الكتاب',
        description: 'اقرأ سورة الفاتحة',
        icon: '🕌',
        category: AchievementCategory.surah,
        targetValue: 1,
        rewardPoints: 20,
      ),
      Achievement(
        id: 'surah_baqarah',
        title: 'فسطاط القرآن',
        description: 'اقرأ سورة البقرة كاملة',
        icon: '📜',
        category: AchievementCategory.surah,
        targetValue: 1,
        rewardPoints: 100,
        isSpecial: true,
      ),

      // إنجازات اجتماعية
      Achievement(
        id: 'first_bookmark',
        title: 'أول علامة',
        description: 'أضف أول علامة مرجعية',
        icon: '🔖',
        category: AchievementCategory.social,
        targetValue: 1,
        rewardPoints: 15,
      ),
      Achievement(
        id: 'search_master',
        title: 'باحث ماهر',
        description: 'قم بـ 50 عملية بحث',
        icon: '🔍',
        category: AchievementCategory.social,
        targetValue: 50,
        rewardPoints: 75,
      ),
    ];
  }

  /// تحديث تقدم الإنجازات
  static Future<void> updateProgress({
    int? ayahsRead,
    int? streakDays,
    int? readingMinutes,
    String? surahCompleted,
    bool? bookmarkAdded,
    bool? searchPerformed,
  }) async {
    bool hasNewAchievement = false;

    for (final achievement in _allAchievements) {
      if (_unlockedAchievements.contains(achievement.id)) continue;

      double currentProgress = _achievementProgress[achievement.id] ?? 0;
      double newProgress = currentProgress;

      // تحديث التقدم حسب نوع الإنجاز
      switch (achievement.category) {
        case AchievementCategory.reading:
          if (ayahsRead != null) {
            newProgress =
                (currentProgress + ayahsRead).clamp(0, achievement.targetValue);
          }
          break;
        case AchievementCategory.streak:
          if (streakDays != null) {
            newProgress = streakDays.toDouble();
          }
          break;
        case AchievementCategory.time:
          if (readingMinutes != null) {
            newProgress = (currentProgress + readingMinutes)
                .clamp(0, achievement.targetValue);
          }
          break;
        case AchievementCategory.surah:
          if (surahCompleted != null &&
              achievement.id.contains(surahCompleted.toLowerCase())) {
            newProgress = achievement.targetValue;
          }
          break;
        case AchievementCategory.social:
          if (bookmarkAdded == true && achievement.id == 'first_bookmark') {
            newProgress = achievement.targetValue;
          } else if (searchPerformed == true &&
              achievement.id == 'search_master') {
            newProgress =
                (currentProgress + 1).clamp(0, achievement.targetValue);
          }
          break;
      }

      // حفظ التقدم الجديد
      if (newProgress != currentProgress) {
        _achievementProgress[achievement.id] = newProgress;

        // التحقق من إنجاز الهدف
        if (newProgress >= achievement.targetValue) {
          await _unlockAchievement(achievement);
          hasNewAchievement = true;
        }
      }
    }

    if (hasNewAchievement) {
      await _saveProgress();
    }
  }

  /// إلغاء قفل إنجاز
  static Future<void> _unlockAchievement(Achievement achievement) async {
    _unlockedAchievements.add(achievement.id);
    await _saveUnlockedAchievements();

    // إرسال إشعار
    await SmartNotificationService.showInstantNotification(
      title: '🏆 إنجاز جديد!',
      body: 'تهانينا! لقد حققت: ${achievement.title}',
      payload: 'achievement_${achievement.id}',
    );

    // تسجيل في التحليلات
    await AnalyticsService.logEvent(AnalyticsEventBuilder.featureUsed(
      featureName: 'achievement_unlocked',
      additionalData: {
        'achievement_id': achievement.id,
        'achievement_title': achievement.title,
        'reward_points': achievement.rewardPoints,
      },
    ));

    debugPrint('🏆 تم إلغاء قفل الإنجاز: ${achievement.title}');
  }

  /// الحصول على جميع الإنجازات
  static List<Achievement> getAllAchievements() {
    return _allAchievements.map((achievement) {
      final progress = _achievementProgress[achievement.id] ?? 0;
      final isUnlocked = _unlockedAchievements.contains(achievement.id);

      return achievement.copyWith(
        currentProgress: progress,
        isUnlocked: isUnlocked,
      );
    }).toList();
  }

  /// الحصول على الإنجازات المفتوحة
  static List<Achievement> getUnlockedAchievements() {
    return getAllAchievements().where((a) => a.isUnlocked).toList();
  }

  /// الحصول على الإنجازات قيد التقدم
  static List<Achievement> getInProgressAchievements() {
    return getAllAchievements()
        .where((a) => !a.isUnlocked && a.currentProgress > 0)
        .toList();
  }

  /// الحصول على إحصائيات الإنجازات
  static Map<String, dynamic> getAchievementStats() {
    final all = getAllAchievements();
    final unlocked = all.where((a) => a.isUnlocked).toList();
    final totalPoints = unlocked.fold<int>(0, (sum, a) => sum + a.rewardPoints);

    return {
      'total_achievements': all.length,
      'unlocked_achievements': unlocked.length,
      'completion_percentage': (unlocked.length / all.length * 100).round(),
      'total_points': totalPoints,
      'special_achievements': unlocked.where((a) => a.isSpecial).length,
    };
  }

  /// إنشاء تحدي يومي
  static Challenge createDailyChallenge() {
    final challenges = [
      Challenge(
        id: 'daily_10_ayahs',
        title: 'اقرأ 10 آيات',
        description: 'اقرأ 10 آيات اليوم',
        targetValue: 10,
        rewardPoints: 20,
        expiryDate: DateTime.now().add(const Duration(days: 1)),
      ),
      Challenge(
        id: 'daily_surah',
        title: 'سورة كاملة',
        description: 'اقرأ سورة كاملة اليوم',
        targetValue: 1,
        rewardPoints: 30,
        expiryDate: DateTime.now().add(const Duration(days: 1)),
      ),
      Challenge(
        id: 'daily_15_minutes',
        title: '15 دقيقة قراءة',
        description: 'اقض 15 دقيقة في القراءة',
        targetValue: 15,
        rewardPoints: 25,
        expiryDate: DateTime.now().add(const Duration(days: 1)),
      ),
    ];

    return challenges[DateTime.now().day % challenges.length];
  }

  /// حفظ البيانات
  static Future<void> _saveUnlockedAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_unlockedKey, _unlockedAchievements.toList());
  }

  static Future<void> _saveProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressMap = _achievementProgress
        .map((key, value) => MapEntry(key, value.toString()));
    await prefs.setString(_progressKey, progressMap.toString());
  }

  /// تحميل البيانات
  static Future<void> _loadAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    final unlockedList = prefs.getStringList(_unlockedKey) ?? [];
    _unlockedAchievements = unlockedList.toSet();
  }

  static Future<void> _loadProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressString = prefs.getString(_progressKey) ?? '{}';
    // تحليل بسيط للبيانات المحفوظة
    _achievementProgress = {};
  }

  static Future<void> _loadChallenges() async {
    // تحميل التحديات النشطة
    _activeChallenges = [];
  }
}
