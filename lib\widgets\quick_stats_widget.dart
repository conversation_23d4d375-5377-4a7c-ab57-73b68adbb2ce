import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_state_manager.dart';

/// ويدجت الإحصائيات السريعة - عرض جذاب للإنجازات
class QuickStatsWidget extends StatelessWidget {
  const QuickStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateManager>(
      builder: (context, appState, child) {
        final stats = appState.getQuickStats();
        
        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إحصائياتك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      '${stats['dailyStreak']}',
                      'سلسلة أيام',
                      Icons.local_fire_department,
                      Colors.orange,
                    ),
                    _buildStatItem(
                      '${stats['totalReadingHours']}',
                      'ساعات قراءة',
                      Icons.access_time,
                      Colors.blue,
                    ),
                    _buildStatItem(
                      '${stats['totalAyahs']}',
                      'آية مقروءة',
                      Icons.menu_book,
                      Colors.green,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildProgressIndicator(stats),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String value, String label, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(Map<String, dynamic> stats) {
    final dailyGoal = 10; // هدف يومي: 10 آيات
    final todayProgress = (stats['averageDaily'] as double).clamp(0.0, dailyGoal.toDouble());
    final progressPercentage = todayProgress / dailyGoal;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الهدف اليومي',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${todayProgress.toInt()} / $dailyGoal آيات',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progressPercentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            progressPercentage >= 1.0 ? Colors.green : Colors.blue,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${(progressPercentage * 100).toInt()}% مكتمل',
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
